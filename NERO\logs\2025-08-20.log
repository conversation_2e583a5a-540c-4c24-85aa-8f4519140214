{"timestamp":"19/08/2025, 21:00:43","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:00:43","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:01","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:01","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:01","level":"info","message":"Sistema de backup automático configurado (a cada 6 horas)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:01","level":"info","message":"Lazy Loader inicializado: 53 comandos mapeados em 374ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:01","level":"info","message":"Comandos essenciais pré-carregados: 7/7 em 0ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:01","level":"success","message":"Modo conversacional Gemini ativado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:01","level":"success","message":"Sistema Gemini AI legado inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:01","level":"info","message":"Credenciais existentes encontradas - reconectando automaticamente","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:01","level":"info","message":"Realizando reconexão automática","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:01","level":"success","message":"Sistema de automação Gemini inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:01","level":"success","message":"Sistema de automação inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:03","level":"success","message":"Conexão estabelecida com sucesso","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:03","level":"success","message":"NERO Bot conectado e operacional!","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:14","level":"info","message":"Sugestão de comando: \"e\" -> \"efeito8d\" (100%)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:14","level":"info","message":"Sugestão de comando: \"ei\" -> \"beija\" (46%)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:24","level":"info","message":"Sugestão de comando: \"sim\" -> \"sc\" (43%)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:29","level":"info","message":"Comando não encontrado sem sugestão: \"oxi\"","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:30","level":"info","message":"Sugestão de comando: \"erro\" -> \"nero\" (30%)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:34","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:35","level":"info","message":"Sugestão de comando: \"a\" -> \"ai\" (100%)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:35","level":"info","message":"NERO invocado: formato=prefix_space, mensagem=\"1\"","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:19:38","level":"info","message":"Comando não encontrado sem sugestão: \"calmaa\"","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:23","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:23","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:23","level":"info","message":"Sistema de backup automático configurado (a cada 6 horas)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:24","level":"info","message":"Lazy Loader inicializado: 53 comandos mapeados em 354ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:24","level":"info","message":"Comandos essenciais pré-carregados: 7/7 em 0ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:24","level":"success","message":"Modo conversacional Gemini ativado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:24","level":"success","message":"Sistema Gemini AI legado inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:24","level":"info","message":"Credenciais existentes encontradas - reconectando automaticamente","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:24","level":"info","message":"Realizando reconexão automática","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:24","level":"success","message":"Sistema de automação Gemini inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:24","level":"success","message":"Sistema de automação inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:25","level":"success","message":"Conexão estabelecida com sucesso","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:25","level":"success","message":"NERO Bot conectado e operacional!","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:48","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:48","level":"info","message":"NERO invocado: formato=greeting_nero, mensagem=\"oi!\"","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:48","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:49","level":"success","message":"Resposta Gemini gerada (tentativa 1)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:49","level":"command","message":"Comando 'gemini-conversation' <NAME_EMAIL> <NAME_EMAIL>","data":{"command":"gemini-conversation","user":"<EMAIL>","group":"<EMAIL>"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:49","level":"success","message":"Resposta gerada via Gemini AI (formato: greeting_nero)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:59","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:59","level":"info","message":"TIMESTAMP DEBUG:\n                📨 msg.messageTimestamp (original): 1755649438\n                📨 Type: number\n                🕐 Current Time: 1755649439901\n                🚀 Command Start: 1755649439901","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:59","level":"info","message":"TIMESTAMP ANALYSIS:\n                        📊 Original value: 1755649438\n                        🔍 Is seconds? true\n                        📅 As Date (original): Wed Jan 21 1970 04:40:49 GMT-0300 (Horário Padrão de Brasília)\n                        📅 As Date (x1000): Tue Aug 19 2025 21:23:58 GMT-0300 (Horário Padrão de Brasília)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:59","level":"info","message":"TIMESTAMP VALIDATION:\n                📨 Final messageTimestamp: 1755649438500\n                📅 As Date: Tue Aug 19 2025 21:23:58 GMT-0300 (Horário Padrão de Brasília)\n                🕐 Current Time: 1755649439901\n                📅 Current Date: Tue Aug 19 2025 21:23:59 GMT-0300 (Horário Padrão de Brasília)\n                ⏰ Time Difference: 1401ms\n                ✅ Is Valid? true","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:23:59","level":"info","message":"LATENCY RANGE: min=902ms, max=1901ms, estimated=1401ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:24:00","level":"info","message":"PING METRICS:\n                📨 Message Timestamp: 1755649438500 (adjusted +500ms)\n                🚀 Command Start: 1755649439901\n                💬 Reply Start: 1755649439906\n                ✅ Reply End: 1755649440224\n                ⏱️ Network Latency: 1401ms (range: 902-1901ms)\n                🔄 Processing Time: 5ms\n                📤 Reply Time: 318ms\n                🎯 Total Response Time: 323ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:24:00","level":"info","message":"PERFORMANCE ANALYSIS:\n                📊 Timestamp Valid: true\n                📅 Message Date: 19/08/2025, 21:23:58\n                📅 Command Date: 19/08/2025, 21:23:59\n                ⚡ Network Status: POOR\n                🚀 Reply Status: FAIR\n                🎯 Overall Status: GOOD","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:24:19","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:24:19","level":"command","message":"Comando 'menu' executado por Cauã em chat privado","data":{"command":"menu","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:24:31","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:25:31","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:25:31","level":"command","message":"Comando 'menu' executado por Cauã em grupo grupo","data":{"command":"menu","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:26:36","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:26:36","level":"command","message":"Comando 'play' executado por Cauã em grupo grupo","data":{"command":"play","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:27:25","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:28:23","level":"info","message":"Iniciando backup automático...","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:28:23","level":"info","message":"Backup de diretório criado: database_2025-08-20T00-28-23-663Z","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:28:23","level":"info","message":"Backup comprimido criado: nero-respostas_2025-08-20T00-28-23-663Z.bak.gz (36% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:28:23","level":"info","message":"Backup comprimido criado: config_2025-08-20T00-28-23-663Z.bak.gz (39% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:28:23","level":"success","message":"Backup automático concluído em 0s: config, database, nero-respostas","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:30:59","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:30:59","level":"command","message":"Comando 'grupo' executado por Cauã em grupo grupo","data":{"command":"grupo","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:31:05","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:31:05","level":"command","message":"Comando 'grupo' executado por Cauã em grupo grupo","data":{"command":"grupo","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:31:39","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:31:44","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:32:30","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:35:27","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:11","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:11","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:11","level":"info","message":"Sistema de backup automático configurado (a cada 6 horas)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:11","level":"info","message":"Lazy Loader inicializado: 57 comandos mapeados em 423ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:11","level":"info","message":"Comandos essenciais pré-carregados: 7/7 em 0ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:11","level":"success","message":"Modo conversacional Gemini ativado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:11","level":"success","message":"Sistema Gemini AI legado inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:11","level":"info","message":"Credenciais existentes encontradas - reconectando automaticamente","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:11","level":"info","message":"Realizando reconexão automática","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:11","level":"success","message":"Sistema de automação Gemini inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:11","level":"success","message":"Sistema de automação inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:12","level":"success","message":"Conexão estabelecida com sucesso","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:12","level":"success","message":"NERO Bot conectado e operacional!","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:42","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:42","level":"command","message":"Comando 'tiktok' executado por Cauã em chat privado","data":{"command":"tiktok","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:42","level":"info","message":"[TIKTOK] Download iniciado: https://vm.tiktok.com/ZMhxxx","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:42","level":"info","message":"[TIKTOK] Tentando API 1...","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:44","level":"warn","message":"[TIKTOK] API 1 falhou:","data":"Request failed with status code 401","bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:44","level":"info","message":"[TIKTOK] Tentando API 2...","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:36:56","level":"warn","message":"[TIKTOK] API 2 falhou:","data":"getaddrinfo ENOTFOUND api.ssyoutube.com","bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:39:57","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:41:11","level":"info","message":"Iniciando backup automático...","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:41:11","level":"info","message":"Backup de diretório criado: database_2025-08-20T00-41-11-255Z","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:41:11","level":"info","message":"Backup comprimido criado: config_2025-08-20T00-41-11-255Z.bak.gz (39% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:41:11","level":"info","message":"Backup comprimido criado: nero-respostas_2025-08-20T00-41-11-255Z.bak.gz (36% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:41:11","level":"success","message":"Backup automático concluído em 0s: config, database, nero-respostas","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:41:46","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:42:06","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:42:08","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:42:19","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:42:44","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:43:36","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:43:46","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:44:04","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:44:34","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:34","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:34","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:34","level":"info","message":"Sistema de backup automático configurado (a cada 6 horas)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:34","level":"info","message":"Lazy Loader inicializado: 59 comandos mapeados em 539ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:34","level":"info","message":"Comandos essenciais pré-carregados: 7/7 em 0ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:34","level":"success","message":"Modo conversacional Gemini ativado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:34","level":"success","message":"Sistema Gemini AI legado inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:34","level":"info","message":"Credenciais existentes encontradas - reconectando automaticamente","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:34","level":"info","message":"Realizando reconexão automática","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:35","level":"success","message":"Sistema de automação Gemini inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:35","level":"success","message":"Sistema de automação inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:38","level":"success","message":"Conexão estabelecida com sucesso","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:38","level":"success","message":"NERO Bot conectado e operacional!","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:43","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:43","level":"command","message":"Comando 'tiktok' executado por Cauã em chat privado","data":{"command":"tiktok","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:43","level":"info","message":"[TIKTOK] Download iniciado: https://vm.tiktok.com/ZMhxxx","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:43","level":"info","message":"[TIKTOK] Iniciando download: https://vm.tiktok.com/ZMhxxx","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:46:44","level":"error","message":"[TIKTOK] Erro no download:","data":{"error":"Request failed with status code 404"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:48:23","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:48:23","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:48:23","level":"info","message":"Sistema de backup automático configurado (a cada 6 horas)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:48:23","level":"info","message":"Lazy Loader inicializado: 59 comandos mapeados em 521ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:48:23","level":"info","message":"Comandos essenciais pré-carregados: 7/7 em 0ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:48:23","level":"success","message":"Modo conversacional Gemini ativado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:48:23","level":"success","message":"Sistema Gemini AI legado inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:48:23","level":"info","message":"Credenciais existentes encontradas - reconectando automaticamente","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:48:23","level":"info","message":"Realizando reconexão automática","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:48:24","level":"success","message":"Sistema de automação Gemini inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:48:24","level":"success","message":"Sistema de automação inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:48:25","level":"success","message":"Conexão estabelecida com sucesso","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:48:25","level":"success","message":"NERO Bot conectado e operacional!","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:49:01","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:49:01","level":"command","message":"Comando 'tiktok' executado por Cauã em chat privado","data":{"command":"tiktok","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:49:01","level":"info","message":"[TIKTOK] Download iniciado: https://vm.tiktok.com/ZMALJaKXo/","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:49:02","level":"info","message":"[TIKTOK] Tentando TikWM API...","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:49:03","level":"info","message":"[TIKTOK] Resposta de TikWM API:","data":"{\"code\":0,\"msg\":\"success\",\"processed_time\":0.6539,\"data\":{\"id\":\"7535167198118104342\",\"region\":\"GB\",\"title\":\"Spotify playlist in bio  #sweaterweather #8d #lyrics \",\"cover\":\"https://p16-pu-sign-no.tikto","bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:49:09","level":"info","message":"[TIKTOK] Download concluído: Song Lyrics","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:53:23","level":"info","message":"Iniciando backup automático...","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:53:23","level":"info","message":"Backup comprimido criado: config_2025-08-20T00-53-23-254Z.bak.gz (39% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:53:23","level":"info","message":"Backup comprimido criado: nero-respostas_2025-08-20T00-53-23-254Z.bak.gz (36% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:53:23","level":"info","message":"Backup de diretório criado: database_2025-08-20T00-53-23-254Z","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:53:23","level":"success","message":"Backup automático concluído em 0s: config, database, nero-respostas","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:54:10","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:54:41","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:54:43","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:54:43","level":"command","message":"Comando 'tiktok' executado por Cauã em grupo grupo","data":{"command":"tiktok","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:54:43","level":"info","message":"[TIKTOK] Download iniciado: https://vm.tiktok.com/ZMALJaKXo/","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:54:44","level":"info","message":"[TIKTOK] Tentando TikWM API...","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:54:45","level":"info","message":"[TIKTOK] Resposta de TikWM API:","data":"{\"code\":0,\"msg\":\"success\",\"processed_time\":0.586,\"data\":{\"id\":\"7535167198118104342\",\"region\":\"GB\",\"title\":\"Spotify playlist in bio  #sweaterweather #8d #lyrics \",\"cover\":\"https://p16-pu-sign-no.tiktok","bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:54:46","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:54:46","level":"command","message":"Comando 'tiktok' executado por Cauã em grupo grupo","data":{"command":"tiktok","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:54:46","level":"info","message":"[TIKTOK] Download iniciado: https://vm.tiktok.com/ZMALJaKXo/","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:54:46","level":"info","message":"[TIKTOK] Tentando TikWM API...","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:54:48","level":"info","message":"[TIKTOK] Resposta de TikWM API:","data":"{\"code\":0,\"msg\":\"success\",\"processed_time\":0.9046,\"data\":{\"id\":\"7535167198118104342\",\"region\":\"GB\",\"title\":\"Spotify playlist in bio  #sweaterweather #8d #lyrics \",\"cover\":\"https://p16-pu-sign-no.tikto","bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:54:58","level":"info","message":"[TIKTOK] Download concluído: Song Lyrics","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:55:02","level":"info","message":"[TIKTOK] Download concluído: Song Lyrics","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:55:31","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:55:39","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:56:58","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 21:57:41","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:40","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:40","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:40","level":"info","message":"Sistema de backup automático configurado (a cada 6 horas)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:41","level":"info","message":"Lazy Loader inicializado: 59 comandos mapeados em 538ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:41","level":"info","message":"Comandos essenciais pré-carregados: 7/7 em 0ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:41","level":"success","message":"Modo conversacional Gemini ativado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:41","level":"success","message":"Sistema Gemini AI legado inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:41","level":"info","message":"Credenciais existentes encontradas - reconectando automaticamente","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:41","level":"info","message":"Realizando reconexão automática","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:41","level":"success","message":"Sistema de automação Gemini inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:41","level":"success","message":"Sistema de automação inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:43","level":"success","message":"Conexão estabelecida com sucesso","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:43","level":"success","message":"NERO Bot conectado e operacional!","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:52","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:09:52","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:10:01","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:10:01","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:10:10","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:10:10","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:10:26","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:10:26","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:11:41","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:12:03","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:12:03","level":"command","message":"Comando 'selo' executado por Cauã em grupo grupo","data":{"command":"selo","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:12:18","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:12:18","level":"command","message":"Comando 'selo' executado por Cauã em grupo grupo","data":{"command":"selo","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:12:32","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:12:32","level":"command","message":"Comando 'selo' executado por Cauã em grupo grupo","data":{"command":"selo","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:12:44","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:12:44","level":"command","message":"Comando 'selo' executado por Cauã em grupo grupo","data":{"command":"selo","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:13:04","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:13:04","level":"command","message":"Comando 'selo' executado por Cauã em grupo grupo","data":{"command":"selo","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:14:40","level":"info","message":"Iniciando backup automático...","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:14:40","level":"info","message":"Backup de diretório criado: database_2025-08-20T01-14-40-718Z","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:14:40","level":"info","message":"Backup comprimido criado: config_2025-08-20T01-14-40-717Z.bak.gz (39% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:14:40","level":"info","message":"Backup comprimido criado: nero-respostas_2025-08-20T01-14-40-718Z.bak.gz (36% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:14:40","level":"success","message":"Backup automático concluído em 0s: config, database, nero-respostas","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:17:32","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:20:09","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:21:12","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:22:38","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:22:38","level":"command","message":"Comando 'menu' executado por Cauã em chat privado","data":{"command":"menu","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:25:11","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:25:37","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:25:49","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:25:59","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:26:16","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:26:23","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:26:23","level":"info","message":"NERO invocado: formato=prefix_space, mensagem=\"oi\"","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:26:23","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:26:25","level":"success","message":"Resposta Gemini gerada (tentativa 1)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:26:25","level":"command","message":"Comando 'gemini-conversation' <NAME_EMAIL> <NAME_EMAIL>","data":{"command":"gemini-conversation","user":"<EMAIL>","group":"<EMAIL>"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:26:25","level":"success","message":"Resposta gerada via Gemini AI (formato: prefix_space)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:26:57","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:27:25","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:27:44","level":"command","message":"Comando 'selo' executado por +71 em grupo grupo","data":{"command":"selo","user":"+71","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:28:09","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:28:09","level":"command","message":"Comando 'selo' executado por Cauã em grupo grupo","data":{"command":"selo","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:28:15","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:28:15","level":"command","message":"Comando 'selo' executado por Cauã em grupo grupo","data":{"command":"selo","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:28:30","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:28:30","level":"command","message":"Comando 'selo' executado por Cauã em grupo grupo","data":{"command":"selo","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:28:39","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:28:39","level":"command","message":"Comando 'selo' executado por Cauã em grupo grupo","data":{"command":"selo","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:28:57","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:28:57","level":"command","message":"Comando 'selo' executado por Cauã em grupo grupo","data":{"command":"selo","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:29:56","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:30:30","level":"command","message":"Comando 'selo' executado por +71 em grupo grupo","data":{"command":"selo","user":"+71","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:30:38","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:30:38","level":"command","message":"Comando 'selo' executado por Cauã em grupo grupo","data":{"command":"selo","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:30:42","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:31:11","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:31:11","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:32:14","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:32:14","level":"info","message":"Sugestão de comando: \"nero,\" -> \"nero\" (62%)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:32:27","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:32:27","level":"info","message":"NERO invocado: formato=prefix_space, mensagem=\"quanto tá o bitcoin\"","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:32:27","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:32:35","level":"success","message":"Resposta Gemini gerada (tentativa 1)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:32:35","level":"command","message":"Comando 'gemini-conversation' <NAME_EMAIL> em chat privado","data":{"command":"gemini-conversation","user":"<EMAIL>","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:32:35","level":"success","message":"Resposta gerada via Gemini AI (formato: prefix_space)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:32:35","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:32:35","level":"command","message":"Comando 'nero' executado por Cauã em chat privado","data":{"command":"nero","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:33:06","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:36:13","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:39:54","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:39:55","level":"info","message":"Sugestão de comando: \"selos\" -> \"selo\" (62%)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:05","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:16","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:16","level":"info","message":"Sugestão de comando: \"selos\" -> \"selo\" (62%)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:26","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:26","level":"info","message":"Sugestão de comando: \"selos\" -> \"selo\" (62%)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:39","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:39","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:39","level":"info","message":"Sistema de backup automático configurado (a cada 6 horas)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:40","level":"info","message":"Lazy Loader inicializado: 59 comandos mapeados em 393ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:40","level":"info","message":"Comandos essenciais pré-carregados: 7/7 em 0ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:40","level":"success","message":"Modo conversacional Gemini ativado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:40","level":"success","message":"Sistema Gemini AI legado inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:40","level":"info","message":"Credenciais existentes encontradas - reconectando automaticamente","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:40","level":"info","message":"Realizando reconexão automática","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:40","level":"success","message":"Sistema de automação Gemini inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:40","level":"success","message":"Sistema de automação inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:41","level":"success","message":"Conexão estabelecida com sucesso","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:41","level":"success","message":"NERO Bot conectado e operacional!","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:48","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:48","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:56","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:40:56","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:41:49","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:41:49","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:42:04","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:42:04","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:40","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:40","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:40","level":"info","message":"Sistema de backup automático configurado (a cada 6 horas)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:40","level":"info","message":"Lazy Loader inicializado: 59 comandos mapeados em 356ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:40","level":"info","message":"Comandos essenciais pré-carregados: 7/7 em 0ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:40","level":"success","message":"Modo conversacional Gemini ativado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:40","level":"success","message":"Sistema Gemini AI legado inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:40","level":"info","message":"Credenciais existentes encontradas - reconectando automaticamente","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:40","level":"info","message":"Realizando reconexão automática","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:41","level":"success","message":"Sistema de automação Gemini inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:41","level":"success","message":"Sistema de automação inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:42","level":"success","message":"Conexão estabelecida com sucesso","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:42","level":"success","message":"NERO Bot conectado e operacional!","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:47","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:50","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:50","level":"command","message":"Comando 'tiktok' executado por Cauã em grupo grupo","data":{"command":"tiktok","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:50","level":"info","message":"[TIKTOK] Download iniciado: https://vm.tiktok.com/ZMALJaKXo/","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:51","level":"info","message":"[TIKTOK] Tentando TikWM API...","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:52","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:52","level":"command","message":"Comando 'tiktok' executado por Cauã em grupo grupo","data":{"command":"tiktok","user":"Cauã","group":"grupo"},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:52","level":"warn","message":"[TIKTOK] Execução duplicada bloqueada: <EMAIL>-F2B97AA988F95F61EFCCA98527FB780D-tiktok","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:43:52","level":"info","message":"[TIKTOK] Resposta de TikWM API:","data":"{\"code\":0,\"msg\":\"success\",\"processed_time\":0.621,\"data\":{\"id\":\"7535167198118104342\",\"region\":\"GB\",\"title\":\"Spotify playlist in bio  #sweaterweather #8d #lyrics \",\"cover\":\"https://p16-pu-sign-no.tiktok","bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:44:16","level":"info","message":"[TIKTOK] Download concluído: Song Lyrics","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:47:38","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:47:38","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:47:39","level":"info","message":"Sistema de backup automático configurado (a cada 6 horas)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:47:54","level":"info","message":"Lazy Loader inicializado: 60 comandos mapeados em 4409ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:47:57","level":"info","message":"Comandos essenciais pré-carregados: 7/7 em 0ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:47:57","level":"success","message":"Modo conversacional Gemini ativado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:47:57","level":"success","message":"Sistema Gemini AI legado inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:47:58","level":"info","message":"Credenciais existentes encontradas - reconectando automaticamente","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:47:58","level":"info","message":"Realizando reconexão automática","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:48:01","level":"success","message":"Sistema de automação Gemini inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:48:01","level":"success","message":"Sistema de automação inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:48:03","level":"success","message":"Conexão estabelecida com sucesso","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:48:05","level":"success","message":"NERO Bot conectado e operacional!","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:48:05","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:48:05","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:48:29","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:48:29","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:48:52","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:48:52","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:49:07","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:49:07","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:49:48","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:49:48","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:52:39","level":"info","message":"Iniciando backup automático...","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:52:39","level":"info","message":"Backup de diretório criado: database_2025-08-20T01-52-39-239Z","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:52:39","level":"info","message":"Backup comprimido criado: config_2025-08-20T01-52-39-239Z.bak.gz (39% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:52:39","level":"info","message":"Backup comprimido criado: nero-respostas_2025-08-20T01-52-39-239Z.bak.gz (36% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:52:39","level":"success","message":"Backup automático concluído em 0s: config, database, nero-respostas","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:05","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:05","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:05","level":"info","message":"Sistema de backup automático configurado (a cada 6 horas)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:06","level":"info","message":"Lazy Loader inicializado: 61 comandos mapeados em 429ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:06","level":"info","message":"Comandos essenciais pré-carregados: 7/7 em 0ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:06","level":"success","message":"Modo conversacional Gemini ativado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:06","level":"success","message":"Sistema Gemini AI legado inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:06","level":"info","message":"Credenciais existentes encontradas - reconectando automaticamente","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:06","level":"info","message":"Realizando reconexão automática","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:06","level":"success","message":"Sistema de automação Gemini inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:06","level":"success","message":"Sistema de automação inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:07","level":"success","message":"Conexão estabelecida com sucesso","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:07","level":"success","message":"NERO Bot conectado e operacional!","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:18","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:18","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:25","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:25","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:54","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 22:56:54","level":"command","message":"Comando 'selo-real' executado por Cauã em chat privado","data":{"command":"selo-real","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:41","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:41","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:41","level":"info","message":"Sistema de backup automático configurado (a cada 6 horas)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:41","level":"info","message":"Lazy Loader inicializado: 61 comandos mapeados em 354ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:41","level":"info","message":"Comandos essenciais pré-carregados: 7/7 em 0ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:41","level":"success","message":"Modo conversacional Gemini ativado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:41","level":"success","message":"Sistema Gemini AI legado inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:41","level":"info","message":"Credenciais existentes encontradas - reconectando automaticamente","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:41","level":"info","message":"Realizando reconexão automática","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:41","level":"success","message":"Sistema de automação Gemini inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:41","level":"success","message":"Sistema de automação inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:43","level":"success","message":"Conexão estabelecida com sucesso","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:43","level":"success","message":"NERO Bot conectado e operacional!","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:48","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:01:48","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:06:41","level":"info","message":"Iniciando backup automático...","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:06:41","level":"info","message":"Backup de diretório criado: database_2025-08-20T02-06-41-119Z","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:06:41","level":"info","message":"Backup comprimido criado: config_2025-08-20T02-06-41-119Z.bak.gz (39% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:06:41","level":"info","message":"Backup comprimido criado: nero-respostas_2025-08-20T02-06-41-120Z.bak.gz (36% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:06:41","level":"success","message":"Backup automático concluído em 0s: config, database, nero-respostas","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:02","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:02","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:02","level":"info","message":"Sistema de backup automático configurado (a cada 6 horas)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:03","level":"info","message":"Lazy Loader inicializado: 61 comandos mapeados em 415ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:03","level":"info","message":"Comandos essenciais pré-carregados: 7/7 em 0ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:03","level":"success","message":"Modo conversacional Gemini ativado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:03","level":"success","message":"Sistema Gemini AI legado inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:03","level":"info","message":"Credenciais existentes encontradas - reconectando automaticamente","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:03","level":"info","message":"Realizando reconexão automática","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:03","level":"success","message":"Sistema de automação Gemini inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:03","level":"success","message":"Sistema de automação inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:05","level":"success","message":"Conexão estabelecida com sucesso","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:05","level":"success","message":"NERO Bot conectado e operacional!","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:20","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:20","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:27","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:27","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:34","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:34","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:43","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:43","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:51","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:51","level":"info","message":"NERO invocado: formato=solo_nero, mensagem=\"olá\"","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:51","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:52","level":"success","message":"Resposta Gemini gerada (tentativa 1)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:52","level":"command","message":"Comando 'gemini-conversation' <NAME_EMAIL> em chat privado","data":{"command":"gemini-conversation","user":"<EMAIL>","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:07:52","level":"success","message":"Resposta gerada via Gemini AI (formato: solo_nero)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:11:50","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:11:50","level":"info","message":"Sugestão de comando: \"selos\" -> \"selo\" (62%)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:11:54","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:11:54","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:12:00","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:12:00","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:12:02","level":"info","message":"Iniciando backup automático...","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:12:02","level":"info","message":"Backup comprimido criado: config_2025-08-20T02-12-02-761Z.bak.gz (39% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:12:02","level":"info","message":"Backup comprimido criado: nero-respostas_2025-08-20T02-12-02-761Z.bak.gz (36% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:12:02","level":"info","message":"Backup de diretório criado: database_2025-08-20T02-12-02-761Z","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:12:02","level":"success","message":"Backup automático concluído em 0s: config, database, nero-respostas","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:13:56","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:13:56","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:13:56","level":"info","message":"Sistema de backup automático configurado (a cada 6 horas)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:13:56","level":"info","message":"Lazy Loader inicializado: 61 comandos mapeados em 364ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:13:56","level":"info","message":"Comandos essenciais pré-carregados: 7/7 em 0ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:13:56","level":"success","message":"Modo conversacional Gemini ativado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:13:56","level":"success","message":"Sistema Gemini AI legado inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:13:56","level":"info","message":"Credenciais existentes encontradas - reconectando automaticamente","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:13:56","level":"info","message":"Realizando reconexão automática","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:13:56","level":"success","message":"Sistema de automação Gemini inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:13:56","level":"success","message":"Sistema de automação inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:13:57","level":"success","message":"Conexão estabelecida com sucesso","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:13:57","level":"success","message":"NERO Bot conectado e operacional!","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:14:10","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:14:10","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:14:21","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:14:21","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:14:57","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:14:57","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:15:09","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:15:09","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:15:29","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:15:29","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:00","level":"success","message":"Carregadas 14 chaves únicas do Gemini","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:00","level":"success","message":"Sistema de busca na internet inicializado (SerpApi - Google)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:00","level":"info","message":"Sistema de backup automático configurado (a cada 6 horas)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:00","level":"info","message":"Lazy Loader inicializado: 61 comandos mapeados em 369ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:00","level":"info","message":"Comandos essenciais pré-carregados: 7/7 em 0ms","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:00","level":"success","message":"Modo conversacional Gemini ativado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:00","level":"success","message":"Sistema Gemini AI legado inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:00","level":"info","message":"Credenciais existentes encontradas - reconectando automaticamente","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:00","level":"info","message":"Realizando reconexão automática","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:02","level":"success","message":"Sistema de automação Gemini inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:02","level":"success","message":"Sistema de automação inicializado","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:10","level":"success","message":"Conexão estabelecida com sucesso","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:10","level":"success","message":"NERO Bot conectado e operacional!","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:21","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:21","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:28","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:28","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:34","level":"info","message":"Dono identificado: <EMAIL>","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:23:34","level":"command","message":"Comando 'selo' executado por Cauã em chat privado","data":{"command":"selo","user":"Cauã","group":null},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:28:00","level":"info","message":"Iniciando backup automático...","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:28:00","level":"info","message":"Backup de diretório criado: database_2025-08-20T02-28-00-131Z","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:28:00","level":"info","message":"Backup comprimido criado: config_2025-08-20T02-28-00-131Z.bak.gz (39% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:28:00","level":"info","message":"Backup comprimido criado: nero-respostas_2025-08-20T02-28-00-131Z.bak.gz (36% do tamanho original)","data":{},"bot":"𝐍𝐄𝐑𝐎"}
{"timestamp":"19/08/2025, 23:28:00","level":"success","message":"Backup automático concluído em 0s: config, database, nero-respostas","data":{},"bot":"𝐍𝐄𝐑𝐎"}
