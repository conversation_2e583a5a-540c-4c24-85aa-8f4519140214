const axios = require('axios');
const { validateURL, validateCommandArgs } = require('../utils/validators');
const logger = require('../utils/logger');

module.exports = {
    name: 'twitter',
    aliases: ['tw', 'x'],
    description: 'Download de vídeos e imagens do Twitter/X',
    category: 'downloads',
    cooldown: 15, // 15 segundos de cooldown
    
    async execute({ sock, msg, args, reply, selo }) {
        try {
            // Validar argumentos
            const validation = validateCommandArgs(args, { minArgs: 1 });
            if (!validation.valid) {
                return reply(`❌ **Uso correto:** \`!twitter [URL do Twitter/X]\`\n\n**Exemplo:** \`!twitter https://twitter.com/user/status/xxx\`\n\n💡 **Suporta:** Vídeos, GIFs e imagens`);
            }

            const url = args.join(' ').trim();

            // Validar URL
            const urlValidation = validateTwitterURL(url);
            if (!urlValidation.valid) {
                return reply(`❌ **URL inválida!**\n\n${urlValidation.reason}\n\n**Formatos aceitos:**\n• https://twitter.com/user/status/xxx\n• https://x.com/user/status/xxx\n• https://mobile.twitter.com/user/status/xxx`);
            }

            logger.info(`[TWITTER] Download iniciado: ${url}`);

            // Reação de processamento
            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "🔍", key: msg.key }
            });

            await reply(`🐦 **Baixando do Twitter/X...**\n\n🔗 **URL:** ${url}\n⏳ Aguarde, estou processando...`);

            // Tentar download
            const result = await downloadTwitterMedia(url);

            if (!result.success) {
                await sock.sendMessage(msg.key.remoteJid, {
                    react: { text: "❌", key: msg.key }
                });

                return reply(`❌ **Erro no download**\n\n${result.error}\n\n💡 **Dicas:**\n• Verifique se o tweet é público\n• Alguns conteúdos podem estar protegidos\n• Tente novamente em alguns minutos`);
            }

            // Reação de sucesso
            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "📤", key: msg.key }
            });

            // Enviar mídia baseada no tipo
            if (result.type === 'video') {
                await sock.sendMessage(msg.key.remoteJid, {
                    video: { url: result.downloadUrl },
                    mimetype: 'video/mp4',
                    caption: `🐦 **Twitter/X Download Concluído**\n\n👤 **Autor:** ${result.username || 'N/A'}\n📝 **Tweet:** ${result.text ? result.text.substring(0, 100) + '...' : 'N/A'}\n📊 **Tipo:** ${result.mediaType || 'Vídeo'}\n🎯 **Qualidade:** ${result.quality || 'HD'}\n📅 **Data:** ${result.date || 'N/A'}\n\n🤖 **NERO Twitter Downloader**`
                }, { quoted: selo });
            } else if (result.type === 'image') {
                await sock.sendMessage(msg.key.remoteJid, {
                    image: { url: result.downloadUrl },
                    caption: `🐦 **Twitter/X Download Concluído**\n\n👤 **Autor:** ${result.username || 'N/A'}\n📝 **Tweet:** ${result.text ? result.text.substring(0, 100) + '...' : 'N/A'}\n📊 **Tipo:** ${result.mediaType || 'Imagem'}\n🎯 **Qualidade:** ${result.quality || 'HD'}\n📅 **Data:** ${result.date || 'N/A'}\n\n🤖 **NERO Twitter Downloader**`
                }, { quoted: selo });
            } else if (result.type === 'multiple') {
                // Para tweets com múltiplas mídias
                for (let i = 0; i < result.media.length; i++) {
                    const media = result.media[i];
                    const caption = i === 0 ? `🐦 **Twitter/X Múltiplas Mídias (${result.media.length})**\n\n👤 **Autor:** ${result.username || 'N/A'}\n📝 **Tweet:** ${result.text ? result.text.substring(0, 100) + '...' : 'N/A'}\n📅 **Data:** ${result.date || 'N/A'}\n\n🤖 **NERO Twitter Downloader**` : '';
                    
                    if (media.type === 'video') {
                        await sock.sendMessage(msg.key.remoteJid, {
                            video: { url: media.url },
                            mimetype: 'video/mp4',
                            caption: caption
                        }, { quoted: selo });
                    } else {
                        await sock.sendMessage(msg.key.remoteJid, {
                            image: { url: media.url },
                            caption: caption
                        }, { quoted: selo });
                    }
                }
            }

            logger.info(`[TWITTER] Download concluído: ${result.username || 'unknown'}`);

        } catch (error) {
            logger.error('Erro no comando twitter:', error);
            
            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "❌", key: msg.key }
            });

            reply('❌ **Erro interno**\n\nOcorreu um erro inesperado. Tente novamente em alguns minutos.');
        }
    }
};

/**
 * Valida URL do Twitter
 * @param {string} url - URL para validar
 * @returns {Object}
 */
function validateTwitterURL(url) {
    if (!url || typeof url !== 'string') {
        return { valid: false, reason: 'URL não fornecida' };
    }

    // Padrões de URL do Twitter/X
    const twitterPatterns = [
        /^https?:\/\/(www\.)?(twitter|x)\.com\/[\w]+\/status\/\d+/,
        /^https?:\/\/mobile\.twitter\.com\/[\w]+\/status\/\d+/,
        /^https?:\/\/t\.co\/[\w]+/
    ];

    const isValid = twitterPatterns.some(pattern => pattern.test(url));
    
    if (!isValid) {
        return { valid: false, reason: 'URL do Twitter/X inválida' };
    }

    return { valid: true };
}

/**
 * Baixa mídia do Twitter usando múltiplas APIs
 * @param {string} url - URL do Twitter
 * @returns {Promise<Object>}
 */
async function downloadTwitterMedia(url) {
    const apis = [
        {
            name: 'API 1',
            endpoint: 'https://api.twittervideodownloader.com/download',
            method: 'POST'
        },
        {
            name: 'API 2',
            endpoint: 'https://api.savetweet.net/api/ajaxSearch',
            method: 'POST'
        }
    ];

    for (const api of apis) {
        try {
            logger.info(`[TWITTER] Tentando ${api.name}...`);
            
            const response = await axios.post(api.endpoint, {
                url: url,
                q: url,
                t: 'media',
                lang: 'en'
            }, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            });

            const data = response.data;
            
            // Processar resposta baseada na API
            if (data && (data.data || data.result)) {
                const mediaData = data.data || data.result;
                
                // Determinar tipo de mídia
                if (mediaData.video_url || mediaData.video) {
                    return {
                        success: true,
                        type: 'video',
                        downloadUrl: mediaData.video_url || mediaData.video,
                        username: mediaData.username || mediaData.user?.screen_name,
                        text: mediaData.text || mediaData.full_text,
                        mediaType: 'Vídeo',
                        quality: mediaData.quality || 'HD',
                        date: mediaData.created_at
                    };
                } else if (mediaData.image_url || mediaData.photo) {
                    return {
                        success: true,
                        type: 'image',
                        downloadUrl: mediaData.image_url || mediaData.photo,
                        username: mediaData.username || mediaData.user?.screen_name,
                        text: mediaData.text || mediaData.full_text,
                        mediaType: 'Imagem',
                        quality: 'HD',
                        date: mediaData.created_at
                    };
                } else if (mediaData.media && Array.isArray(mediaData.media)) {
                    return {
                        success: true,
                        type: 'multiple',
                        media: mediaData.media,
                        username: mediaData.username || mediaData.user?.screen_name,
                        text: mediaData.text || mediaData.full_text,
                        date: mediaData.created_at
                    };
                }
            }

        } catch (error) {
            logger.warn(`[TWITTER] ${api.name} falhou:`, error.message);
            continue;
        }
    }

    return {
        success: false,
        error: 'Não foi possível baixar a mídia. Verifique se o tweet é público.'
    };
}
