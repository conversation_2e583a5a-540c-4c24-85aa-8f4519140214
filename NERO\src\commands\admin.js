/**
 * NERO Bot - Comando Admin
 * Comandos administrativos avançados para o dono do bot
 */

const { BOT_CONSTANTS, EMOJIS } = require('../constants');
const { validatePermissions } = require('../utils/permissions');
const { getStats } = require('../utils/rateLimiter');
const { performAutoBackup, listBackups } = require('../utils/backup');
const logger = require('../utils/logger');

module.exports = {
    name: 'admin',
    aliases: ['adm', 'sistema'],
    description: 'Comandos administrativos do sistema (apenas dono)',
    async execute({ sock, msg, args, reply, selo }) {
        // Validar permissões - apenas dono
        const validation = await validatePermissions({
            sock,
            msg,
            ownerOnly: true
        });

        if (!validation.valid) {
            return reply(`${EMOJIS.ERROR} ${validation.reason}`);
        }

        const subCommand = args[0]?.toLowerCase();

        if (!subCommand) {
            const helpText = `🛡️ *PAINEL ADMINISTRATIVO ${BOT_CONSTANTS.NAME}*\n\n` +
                           `📊 *Comandos disponíveis:*\n` +
                           `• \`admin stats\` - Estatísticas do sistema\n` +
                           `• \`admin backup\` - Criar backup manual\n` +
                           `• \`admin backups\` - Listar backups\n` +
                           `• \`admin logs\` - Visualizar logs recentes\n` +
                           `• \`admin cache\` - Limpar cache\n` +
                           `• \`admin restart\` - Reiniciar bot\n` +
                           `• \`admin eval [código]\` - Executar código\n\n` +
                           `🔖 *Sistema de Selos:*\n` +
                           `• \`selo auto on/off\` - Ativar/desativar selo automático\n` +
                           `• \`selo lista\` - Ver todos os selos disponíveis\n` +
                           `• \`selo info\` - Informações sobre selos\n\n` +
                           `⚠️ *Atenção:* Estes comandos são apenas para o desenvolvedor!`;
            
            return reply(helpText);
        }

        try {
            switch (subCommand) {
                case 'stats':
                case 'estatisticas':
                    await handleStats(reply);
                    break;

                case 'backup':
                    await handleBackup(reply);
                    break;

                case 'backups':
                case 'lista':
                    await handleListBackups(reply);
                    break;

                case 'logs':
                    await handleLogs(reply, args[1]);
                    break;

                case 'cache':
                    await handleClearCache(reply);
                    break;

                case 'restart':
                case 'reiniciar':
                    await handleRestart(sock, msg, reply);
                    break;

                case 'eval':
                case 'execute':
                    await handleEval(reply, args.slice(1).join(' '));
                    break;

                default:
                    reply(`${EMOJIS.ERROR} Subcomando não reconhecido. Use \`admin\` para ver a ajuda.`);
            }

        } catch (error) {
            logger.error('Erro no comando admin:', error);
            reply(`${EMOJIS.ERROR} Erro ao executar comando administrativo.`);
        }
    }
};

/**
 * Mostrar estatísticas do sistema
 */
const handleStats = async (reply) => {
    const rateLimitStats = getStats();
    const memUsage = process.memoryUsage();
    const uptime = process.uptime();

    const statsText = `📊 *ESTATÍSTICAS DO SISTEMA*\n\n` +
                     `⏰ *Uptime:* ${formatUptime(uptime)}\n` +
                     `💾 *Memória:* ${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB\n` +
                     `👥 *Usuários ativos:* ${rateLimitStats.activeUsers}\n` +
                     `🔄 *Cooldowns ativos:* ${rateLimitStats.activeCooldowns}\n` +
                     `🤖 *Bot:* ${BOT_CONSTANTS.NAME} v${require('../../package.json').version}\n` +
                     `🟢 *Status:* Online e funcionando`;

    await reply(statsText);
};

/**
 * Criar backup manual
 */
const handleBackup = async (reply) => {
    await reply(`${EMOJIS.PROCESSING} Criando backup manual...`);
    
    const success = await performAutoBackup();
    
    if (success) {
        await reply(`${EMOJIS.SUCCESS} Backup criado com sucesso!`);
    } else {
        await reply(`${EMOJIS.ERROR} Erro ao criar backup.`);
    }
};

/**
 * Listar backups disponíveis
 */
const handleListBackups = async (reply) => {
    const backups = listBackups();
    
    if (backups.length === 0) {
        return reply(`${EMOJIS.INFO} Nenhum backup encontrado.`);
    }

    let backupText = `📋 *BACKUPS DISPONÍVEIS*\n\n`;
    
    backups.slice(0, 10).forEach((backup, index) => {
        const date = backup.created.toLocaleString('pt-BR');
        const size = backup.type === 'file' ? `(${(backup.size / 1024).toFixed(1)}KB)` : '(pasta)';
        backupText += `${index + 1}. ${backup.name} ${size}\n   📅 ${date}\n\n`;
    });

    if (backups.length > 10) {
        backupText += `... e mais ${backups.length - 10} backup(s)`;
    }

    await reply(backupText);
};

/**
 * Visualizar logs recentes
 */
const handleLogs = async (reply, level = 'error') => {
    const fs = require('fs');
    const path = require('path');
    
    try {
        const logsDir = path.join(__dirname, '../../logs');
        const today = new Date().toISOString().split('T')[0];
        const logFile = path.join(logsDir, `${today}.log`);

        if (!fs.existsSync(logFile)) {
            return reply(`${EMOJIS.INFO} Nenhum log encontrado para hoje.`);
        }

        const logContent = fs.readFileSync(logFile, 'utf8');
        const logs = logContent.split('\n')
            .filter(line => line.trim())
            .map(line => {
                try {
                    return JSON.parse(line);
                } catch {
                    return null;
                }
            })
            .filter(log => log && (!level || log.level === level))
            .slice(-10); // Últimos 10 logs

        if (logs.length === 0) {
            return reply(`${EMOJIS.INFO} Nenhum log do tipo '${level}' encontrado.`);
        }

        let logText = `📋 *LOGS RECENTES (${level.toUpperCase()})*\n\n`;
        
        logs.forEach((log, index) => {
            const time = new Date(log.timestamp).toLocaleTimeString('pt-BR');
            logText += `${index + 1}. [${time}] ${log.message}\n`;
        });

        await reply(logText);

    } catch (error) {
        logger.error('Erro ao ler logs:', error);
        reply(`${EMOJIS.ERROR} Erro ao acessar logs.`);
    }
};

/**
 * Limpar cache
 */
const handleClearCache = async (reply) => {
    try {
        // Limpar cache do Node.js
        const moduleKeys = Object.keys(require.cache);
        let clearedCount = 0;

        moduleKeys.forEach(key => {
            if (key.includes('/src/commands/') || key.includes('/nero/')) {
                delete require.cache[key];
                clearedCount++;
            }
        });

        await reply(`${EMOJIS.SUCCESS} Cache limpo! ${clearedCount} módulos removidos do cache.`);

    } catch (error) {
        logger.error('Erro ao limpar cache:', error);
        reply(`${EMOJIS.ERROR} Erro ao limpar cache.`);
    }
};

/**
 * Reiniciar bot
 */
const handleRestart = async (sock, msg, reply) => {
    await reply(`${EMOJIS.PROCESSING} Reiniciando ${BOT_CONSTANTS.NAME}...`);
    
    logger.info('Bot sendo reiniciado pelo comando admin');
    
    setTimeout(() => {
        process.exit(0);
    }, 2000);
};

/**
 * Executar código JavaScript
 */
const handleEval = async (reply, code) => {
    if (!code) {
        return reply(`${EMOJIS.ERROR} Forneça o código para executar.`);
    }

    try {
        const result = eval(code);
        const output = typeof result === 'object' ? JSON.stringify(result, null, 2) : String(result);
        
        await reply(`✅ *Resultado:*\n\`\`\`${output}\`\`\``);

    } catch (error) {
        await reply(`❌ *Erro:*\n\`\`\`${error.message}\`\`\``);
    }
};

/**
 * Formatar tempo de atividade
 */
const formatUptime = (seconds) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    let result = '';
    if (days > 0) result += `${days}d `;
    if (hours > 0) result += `${hours}h `;
    if (minutes > 0) result += `${minutes}m `;
    result += `${secs}s`;

    return result;
};
