const fs = require('fs');
const path = require('path');
const { menu } = require('../menus');
const config = require('../config.json');
const pkg = require('../../package.json');
const interactiveButtons = require('../utils/interactiveButtons');

module.exports = {
  name: 'menu',
  aliases: ['help'],
  description: 'Exibe o menu principal com imagem e react',
  async execute({ sock, msg, selo }) {
    try {
      const sender = msg.key.participant || msg.key.remoteJid;
      const jid = msg.key.remoteJid;

      // Monta o texto do menu
      const caption = menu(
        config.Prefix.value,
        config.botName.value,
        sender,
        config.nameOwner.value,
        pkg
      );

      // Caminhos possíveis para a imagem do menu
      const possiblePaths = [
        path.join(__dirname, '..', '..', 'menuimg', 'menuimg.png'),
        path.join(__dirname, '..', '..', 'menuimg', 'menuimg.jpg'),
        path.join(__dirname, '..', '..', 'menuimg', 'menuimg2.png')
      ];

      let imageBuffer = null;
      let imagePath = null;

      // Procura por uma imagem válida
      for (const testPath of possiblePaths) {
        if (fs.existsSync(testPath)) {
          imagePath = testPath;
          imageBuffer = fs.readFileSync(imagePath);
          break;
        }
      }

      // Reage à mensagem com o emoji 👑
      await sock.sendMessage(jid, { react: { text: '👑', key: msg.key } });

      if (imageBuffer) {
        // Envia a imagem com legenda e menciona o usuário
        await sock.sendMessage(
          jid,
          {
            image: imageBuffer,
            caption: caption,
            mentions: [sender]
          },
          { quoted: selo }
        );
      } else {
        // Fallback: envia apenas o texto se não encontrar imagem
        console.warn('⚠️ Nenhuma imagem de menu encontrada, enviando apenas texto');
        await sock.sendMessage(
          jid,
          {
            text: `🖼️ *Imagem do menu não encontrada*\n\n${caption}`,
            mentions: [sender]
          },
          { quoted: selo }
        );
      }

    } catch (err) {
      console.error('Erro no comando menu:', err);
      await sock.sendMessage(
        msg.key.remoteJid,
        { text: '❌ Ocorreu um erro ao executar o menu.' },
        { quoted: selo }
      );
    }
  }
};
