//copiar códigos? modificar? deixa os créditos no yt
//base do gutinho (my channel↓
//YouTube:https: @gutoxtazv9
//WhatsApp: +55 51 8992-6591
//telegram: t.me/gutox9
//local: Brasil, Bahia, Candelária,

global.owner = ["555189926591"]
global.ownername = "ᴳᵘᵗᵒ 👁️‍🗨️"
global.location = "Brasil, Bahia, Candelária"
global.botname = 'GMOTEJID       <✓>' 
global.link ='https://www.youtube.com/@gutoxtazv9'
global.prefa = ['','!','.',','] 
global.limitawal = {
    premium: "Infinity",
    free: 20
}

let fs = require('fs')
let file = require.resolve(__filename)
fs.watchFile(file, () => {
fs.unwatchFile(file)
console.log(`Update ${__filename}`)
delete require.cache[file]
require(file)
})