{"noiseKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "KM3aH+UqMSsSmp0YESLxxwkdQb6YbFqwdSZ5kk8/PH4="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "gq9p0VK00AH0oSx150AdRb37wBxGI0M/HHBMDuohFjc="}}, "pairingEphemeralKeyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "OCeH+1drmGeCDEqn1IlJQKHtfIF4DOWZDFAjDsqbGWY="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "IFTR9fjRWBwUVbSn3ULYNSt4VqXzufQkXpcoVl2T4kI="}}, "signedIdentityKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "8FQ20u78USneZib59lo0xC0nQ4GvnmXpJWuEcAsVm2A="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "XzwPsfWNQaF4zWLwojyZVphVvx8Cw/S1KgTQ3RcNJ3I="}}, "signedPreKey": {"keyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "4H9tPJZe7dPybScAalO6MaSFWMcX3FaiGW0EPKd9gng="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "25zTt8nluM3Bl5ag9EBz2ZEEsyuJrpmlg29KoqizWgM="}}, "signature": {"type": "<PERSON><PERSON><PERSON>", "data": "CJgNVEbXD2h23Nngj6A+IK2kZsK7KhDucFVkqJeZ464VJdS3BHUO8XBLtecLEuu5kwktOrriJIlnC5I97mzWAw=="}, "keyId": 1}, "registrationId": 78, "advSecretKey": "gpaiirGNI4C5yK+9NrQ9gCREUn7UI/1+kiPiTaOkf98=", "processedHistoryMessages": [{"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "6FE1115A4A36C25B3D632AD237011DAE"}, "messageTimestamp": **********}, {"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "6C85942E00E12DF95A90ABEF5A6A53F4"}, "messageTimestamp": **********}, {"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "2009FBB7991EB071DC58AF4243592C95"}, "messageTimestamp": **********}], "nextPreKeyId": 31, "firstUnuploadedPreKeyId": 31, "accountSyncCounter": 1, "accountSettings": {"unarchiveChats": false}, "registered": false, "account": {"details": "CM7Uv1MQkZCTxQYYAiAAKAA=", "accountSignatureKey": "YyxPEbnkC71HWCaIjJQOEH1BL4qVpNBSexoyLgcel1s=", "accountSignature": "Safl/VhmD6739w2+QXeeuCx7shTrwQG41jV9+oOHhHp94uKoHC6xy7luVH6P2oziYNROdgF15N/knKr7oYnDDA==", "deviceSignature": "9tBRT3eAPivqCQI2UQ6670EMHS0b1At+X933pipWfpIGkb2YKR3xMKPUvommN6udMawP/Xnxu2Rz+4zeUhBuAg=="}, "me": {"id": "************:<EMAIL>", "name": "<PERSON>", "lid": "**************:2@lid"}, "signalIdentities": [{"identifier": {"name": "************:<EMAIL>", "deviceId": 0}, "identifierKey": {"type": "<PERSON><PERSON><PERSON>", "data": "BWMsTxG55Au9R1gmiIyUDhB9QS+KlaTQUnsaMi4HHpdb"}}], "platform": "smba", "routingInfo": {"type": "<PERSON><PERSON><PERSON>", "data": "CAUIDQ=="}, "lastAccountSyncTimestamp": **********, "myAppStateKeyId": "AAAAANSv", "lastPropHash": "1Fcm9N"}