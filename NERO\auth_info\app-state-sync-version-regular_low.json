{"version": 3, "hash": {"type": "<PERSON><PERSON><PERSON>", "data": "aHXJgK3Wop6BaYmDBT1+rMuD6T9IsVogOZ7qPHewlxGPmqbH8VB7BB8+iBiZ07O3g14cqVh7HbKY+FYzcSgZGuSS08THcAQhwOR/kVz/Y/KbDoxO9t2+ETJkQjp3GxkvWDPXIkkZgxDbSQVUIaeQZ+Fw2wY7ztcP8lqJ8VJssXY="}, "indexValueMap": {"MPXmkgCBCywleBZaKsI5HzPdGxykLmaFABHgckEcB9g=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "C5S5x2gUbt2SGBRfLZ000y+cECca2K+C/mTUEqt2DS0="}}, "QKniZRO+5FxNMolPnAB7VvhWyp9OEtaO5FK6nUDNL6Q=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "VI29udw1s65Cvpe709YZHeu1+PCko/gaEjLaBE5USIk="}}, "S2iAT+yvmdVdgBEIYuvl5G2O2OsG+lQc28/5y66z5vo=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "rEKA3cmmkjQvXiD2JGwUlddVQZhId5PnYs0BtCqIbew="}}, "VnPejWQU5phJyf6RwYjBBIQFlkHWalpucXkTG1/mpEo=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "zsHJVDC6SaFGHEX7p5ZBSu3ZF1N+o2nLWnIqI3x07rI="}}, "gzOqsqdf7/W14A3atq4jJNtYs3f0sH6bdcHb+fxflE0=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "Vk4CUepSdYcfu4/IqMP2pECA8VDL0zv/zbXDKrKuflk="}}, "q3C637EoLZB55OnV/9KC6fpv4/YaKXKOASHorcNtbYk=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "HOkXd3vXbvyd0FtMhndk8t3L0SBPytTUf9wKhTi7Kcc="}}}}