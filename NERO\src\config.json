{"OwnerNumber": {"description": "Definir o número do dono oficial do bot, ou seja, o superior.", "value": "5521970998386"}, "BotNumber": {"description": "Definir o número do BOT usado em alguns comandos.", "value": "NUMERO_DO_BOT_AQUI"}, "Prefix": {"description": "Definir um prefixo para o BOT reconhecer como comando. Lembrando, não é afetado os comandos sem prefixos.", "value": "!"}, "nameOwner": {"description": "Definir o nome que você deseja ser reconhecido como dono na BOT, será presente em comandos informativos.", "value": "Cauã"}, "BotName": {"description": "Definir o nome do bot, pode incluir letras modificadas e símbolos.", "value": "𝐍𝐄𝐑𝐎"}, "botName": {"description": "Definir o nome do bot, pode incluir letras modificadas e símbolos.", "value": "𝐍𝐄𝐑𝐎"}, "autoOwnerSeal": {"description": "Ativar selo automático do dono nas mensagens (true/false)", "value": true}, "geminiAI": {"description": "Configurações do sistema de IA conversacional Gemini", "enabled": {"description": "Ativar/desativar modo conversacional por padrão", "value": false}, "autoEnable": {"description": "Ativar automaticamente na inicialização", "value": true}, "cacheEnabled": {"description": "Usar cache para respostas do Gemini", "value": true}, "maxRetries": {"description": "Número máximo de tentativas por requisição", "value": 3}, "requestTimeout": {"description": "Timeout para requisições em milissegundos", "value": 10000}}}