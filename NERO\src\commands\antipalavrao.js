/**
 * NERO Bot - Comando Anti-Palavrão
 * Proteção contra palavrões e conteúdo impróprio
 */

const protectionSystem = require('../utils/protectionSystem');
const { validatePermissions, PERMISSION_LEVELS } = require('../utils/permissionSystem');
const logger = require('../utils/logger');

module.exports = {
    name: 'antipalavrao',
    aliases: ['anti-palavrao', 'antiswear', 'filtro'],
    description: 'Ativa/desativa filtro de palavrões',
    usage: 'antipalavrao [on/off] [ação]',
    permissions: 'Admin',
    cooldown: 2,

    async execute({ sock, msg, args, reply }) {
        const groupId = msg.key.remoteJid;

        // Verificar se é um grupo
        if (!groupId.endsWith('@g.us')) {
            return await reply('❌ Este comando só pode ser usado em grupos!');
        }

        // Verificar permissões
        const hasPermission = await validatePermissions(sock, msg, PERMISSION_LEVELS.ADMIN);
        if (!hasPermission) {
            return await reply('❌ Você precisa ser admin para usar este comando!');
        }

        const action = args[0]?.toLowerCase();
        const actionType = args[1]?.toLowerCase();

        switch (action) {
            case 'on':
            case 'ativar':
            case 'enable':
                return await this.enableAntiPalavrao(reply, groupId, actionType);

            case 'off':
            case 'desativar':
            case 'disable':
                return await this.disableAntiPalavrao(reply, groupId);

            case 'config':
            case 'configurar':
                return await this.showConfig(reply, groupId);

            case 'words':
            case 'palavras':
                return await this.manageWords(reply, groupId, args.slice(1));

            case 'test':
            case 'teste':
                return await this.testFilter(reply, groupId, args.slice(1).join(' '));

            default:
                return await this.showStatus(reply, groupId);
        }
    },

    async enableAntiPalavrao(reply, groupId, actionType = 'warn') {
        try {
            const validActions = ['delete', 'warn', 'kick'];
            if (actionType && !validActions.includes(actionType)) {
                return await reply(`❌ Ação inválida! Use: ${validActions.join(', ')}`);
            }

            // Lista básica de palavrões (censurada para exemplo)
            const defaultWords = [
                'p***', 'c***', 'f***', 'm***', 'b***',
                // Adicione mais palavras conforme necessário
            ];

            const config = {
                action: actionType || 'warn',
                words: defaultWords
            };

            const result = protectionSystem.toggleProtection(groupId, 'antipalavrao', true, config);

            if (result.success) {
                let message = '🟢 **ANTI-PALAVRÃO ATIVADO**\n\n';
                message += `🤬 Palavrões serão filtrados automaticamente\n`;
                message += `⚡ **Ação:** ${result.config.action}\n`;
                message += `🔢 **Máx. Violações:** ${result.config.maxViolations}\n`;
                message += `📝 **Palavras Bloqueadas:** ${result.config.words.length} palavras\n\n`;
                
                message += '💡 **Comandos úteis:**\n';
                message += '• `!antipalavrao words add [palavra]` - Adicionar palavra\n';
                message += '• `!antipalavrao words list` - Ver lista de palavras\n';
                message += '• `!antipalavrao test [frase]` - Testar filtro';

                return await reply(message);
            } else {
                return await reply('❌ Erro ao ativar anti-palavrão. Tente novamente.');
            }
        } catch (error) {
            logger.error('Erro ao ativar anti-palavrão:', error);
            return await reply('❌ Erro interno. Tente novamente mais tarde.');
        }
    },

    async disableAntiPalavrao(reply, groupId) {
        try {
            const result = protectionSystem.toggleProtection(groupId, 'antipalavrao', false);

            if (result.success) {
                return await reply('🔴 **ANTI-PALAVRÃO DESATIVADO**\n\n✅ Filtro de palavrões foi desativado.');
            } else {
                return await reply('❌ Erro ao desativar anti-palavrão. Tente novamente.');
            }
        } catch (error) {
            logger.error('Erro ao desativar anti-palavrão:', error);
            return await reply('❌ Erro interno. Tente novamente mais tarde.');
        }
    },

    async showStatus(reply, groupId) {
        const config = protectionSystem.getGroupProtection(groupId, 'antipalavrao');
        
        let message = '🤬 **STATUS DO ANTI-PALAVRÃO**\n\n';
        message += `🔘 **Status:** ${config.enabled ? '🟢 Ativo' : '🔴 Inativo'}\n`;
        
        if (config.enabled) {
            message += `⚡ **Ação:** ${config.action}\n`;
            message += `🔢 **Máx. Violações:** ${config.maxViolations}\n`;
            message += `📝 **Palavras Bloqueadas:** ${config.words.length} palavras\n\n`;
            
            message += '⚙️ **Comandos:**\n';
            message += '• `!antipalavrao off` - Desativar\n';
            message += '• `!antipalavrao words` - Gerenciar palavras\n';
            message += '• `!antipalavrao test [frase]` - Testar filtro';
        } else {
            message += '\n💡 Use `!antipalavrao on` para ativar o filtro.';
        }

        return await reply(message);
    },

    async showConfig(reply, groupId) {
        const config = protectionSystem.getGroupProtection(groupId, 'antipalavrao');
        
        let message = '⚙️ **CONFIGURAÇÃO DO ANTI-PALAVRÃO**\n\n';
        message += `🔘 **Status:** ${config.enabled ? '🟢 Ativo' : '🔴 Inativo'}\n`;
        message += `⚡ **Ação:** ${config.action}\n`;
        message += `🔢 **Máx. Violações:** ${config.maxViolations}\n`;
        message += `📝 **Palavras Bloqueadas:** ${config.words.length} palavras\n\n`;
        
        message += '📋 **Descrição das Ações:**\n';
        message += '• `delete` - Apaga a mensagem automaticamente\n';
        message += '• `warn` - Avisa o usuário sobre a violação\n';
        message += '• `kick` - Remove o usuário do grupo\n\n';
        
        message += '💡 **Gerenciar palavras:** `!antipalavrao words`';

        return await reply(message);
    },

    async manageWords(reply, groupId, args) {
        const action = args[0]?.toLowerCase();
        const word = args.slice(1).join(' ').toLowerCase();

        const config = protectionSystem.getGroupProtection(groupId, 'antipalavrao');

        switch (action) {
            case 'add':
            case 'adicionar':
                if (!word) {
                    return await reply('❌ Especifique a palavra!\nUso: `!antipalavrao words add palavra`');
                }
                
                if (config.words.includes(word)) {
                    return await reply(`❌ A palavra já está na lista!`);
                }
                
                config.words.push(word);
                protectionSystem.saveProtections();
                
                return await reply(`✅ Palavra adicionada à lista de bloqueios!`);

            case 'remove':
            case 'remover':
                if (!word) {
                    return await reply('❌ Especifique a palavra!\nUso: `!antipalavrao words remove palavra`');
                }
                
                const index = config.words.indexOf(word);
                if (index === -1) {
                    return await reply(`❌ A palavra não está na lista!`);
                }
                
                config.words.splice(index, 1);
                protectionSystem.saveProtections();
                
                return await reply(`✅ Palavra removida da lista de bloqueios!`);

            case 'list':
            case 'listar':
                let message = '📝 **PALAVRAS BLOQUEADAS**\n\n';
                if (config.words.length > 0) {
                    message += `Total: ${config.words.length} palavras\n\n`;
                    message += '🔒 Por questões de privacidade, a lista não é exibida.\n';
                    message += 'Use `!antipalavrao test [frase]` para testar o filtro.';
                } else {
                    message += 'Nenhuma palavra na lista de bloqueios.';
                }
                return await reply(message);

            case 'clear':
            case 'limpar':
                config.words = [];
                protectionSystem.saveProtections();
                return await reply('✅ Lista de palavras limpa!');

            default:
                return await reply('❌ Ação inválida!\nUse: add, remove, list ou clear');
        }
    },

    async testFilter(reply, groupId, testText) {
        if (!testText) {
            return await reply('❌ Especifique um texto para testar!\nUso: `!antipalavrao test sua frase aqui`');
        }

        const config = protectionSystem.getGroupProtection(groupId, 'antipalavrao');
        
        if (!config.enabled) {
            return await reply('❌ Anti-palavrão está desativado! Ative primeiro com `!antipalavrao on`');
        }

        const lowerText = testText.toLowerCase();
        const foundWords = config.words.filter(word => lowerText.includes(word.toLowerCase()));

        let message = '🧪 **TESTE DO FILTRO DE PALAVRÕES**\n\n';
        message += `📝 **Texto Testado:** [CENSURADO]\n`;

        if (foundWords.length > 0) {
            message += `🔴 **Resultado:** BLOQUEADO\n`;
            message += `⚡ **Ação:** ${config.action}\n`;
            message += `🔍 **Palavras Detectadas:** ${foundWords.length} palavra(s)`;
        } else {
            message += `🟢 **Resultado:** PERMITIDO\n`;
            message += `💡 **Motivo:** Nenhuma palavra bloqueada detectada`;
        }

        return await reply(message);
    }
};
