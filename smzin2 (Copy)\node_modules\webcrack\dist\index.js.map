{"version": 3, "sources": ["../src/index.ts", "../src/ast-utils/ast.ts", "babel-import:@babel/generator", "../src/ast-utils/generator.ts", "babel-import:@babel/traverse", "../src/ast-utils/inline.ts", "../src/ast-utils/matcher.ts", "../src/ast-utils/rename.ts", "../src/ast-utils/transform.ts", "../src/deobfuscate/index.ts", "../src/unminify/transforms/merge-strings.ts", "../src/deobfuscate/array-rotator.ts", "../src/deobfuscate/control-flow-object.ts", "../src/deobfuscate/control-flow-switch.ts", "../src/deobfuscate/dead-code.ts", "../src/deobfuscate/decoder.ts", "../src/deobfuscate/inline-decoded-strings.ts", "../src/deobfuscate/inline-decoder-wrappers.ts", "../src/deobfuscate/inline-object-props.ts", "../src/deobfuscate/string-array.ts", "../src/deobfuscate/vm.ts", "../src/deobfuscate/debug-protection.ts", "../src/deobfuscate/evaluate-globals.ts", "../src/deobfuscate/merge-object-assignments.ts", "../src/deobfuscate/self-defending.ts", "../src/deobfuscate/var-functions.ts", "../src/transforms/jsx.ts", "../src/ast-utils/scope.ts", "../src/transforms/jsx-new.ts", "../src/transforms/mangle.ts", "../src/transpile/transforms/index.ts", "../src/transpile/transforms/default-parameters.ts", "../src/transpile/transforms/logical-assignments.ts", "../src/transpile/transforms/nullish-coalescing.ts", "../src/transpile/transforms/nullish-coalescing-assignment.ts", "../src/transpile/transforms/optional-chaining.ts", "../src/transpile/transforms/template-literals.ts", "../src/transpile/index.ts", "../src/unminify/transforms/index.ts", "../src/unminify/transforms/block-statements.ts", "../src/unminify/transforms/computed-properties.ts", "../src/unminify/transforms/for-to-while.ts", "../src/unminify/transforms/infinity.ts", "../src/unminify/transforms/invert-boolean-logic.ts", "../src/unminify/transforms/json-parse.ts", "../src/unminify/transforms/logical-to-if.ts", "../src/unminify/transforms/merge-else-if.ts", "../src/unminify/transforms/number-expressions.ts", "../src/unminify/transforms/raw-literals.ts", "../src/unminify/transforms/remove-double-not.ts", "../src/unminify/transforms/sequence.ts", "../src/unminify/transforms/split-for-loop-vars.ts", "../src/unminify/transforms/split-variable-declarations.ts", "../src/unminify/transforms/ternary-to-if.ts", "../src/unminify/transforms/typeof-undefined.ts", "../src/unminify/transforms/unary-expressions.ts", "../src/unminify/transforms/unminify-booleans.ts", "../src/unminify/transforms/void-to-undefined.ts", "../src/unminify/transforms/yoda.ts", "../src/unminify/index.ts", "../src/unpack/index.ts", "../src/unpack/browserify/index.ts", "../src/unpack/path.ts", "../src/unpack/bundle.ts", "../src/unpack/browserify/bundle.ts", "../src/unpack/module.ts", "../src/unpack/browserify/module.ts", "../src/unpack/webpack/index.ts", "../src/unpack/webpack/bundle.ts", "../src/unpack/webpack/esm.ts", "../src/unpack/webpack/getDefaultExport.ts", "../src/unpack/webpack/varInjection.ts", "../src/unpack/webpack/module.ts", "../src/utils/platform.ts"], "sourcesContent": ["import type { ParseResult } from '@babel/parser';\nimport { parse } from '@babel/parser';\nimport type * as t from '@babel/types';\nimport type Matchers from '@codemod/matchers';\nimport * as m from '@codemod/matchers';\nimport debug from 'debug';\nimport { join, normalize } from 'node:path';\nimport {\n  applyTransform,\n  applyTransformAsync,\n  applyTransforms,\n  generate,\n} from './ast-utils';\nimport type { Sandbox } from './deobfuscate';\nimport deobfuscate, {\n  createBrowserSandbox,\n  createNodeSandbox,\n} from './deobfuscate';\nimport debugProtection from './deobfuscate/debug-protection';\nimport evaluateGlobals from './deobfuscate/evaluate-globals';\nimport mergeObjectAssignments from './deobfuscate/merge-object-assignments';\nimport selfDefending from './deobfuscate/self-defending';\nimport varFunctions from './deobfuscate/var-functions';\nimport jsx from './transforms/jsx';\nimport jsxNew from './transforms/jsx-new';\nimport mangle from './transforms/mangle';\nimport transpile from './transpile';\nimport unminify from './unminify';\nimport {\n  blockStatements,\n  sequence,\n  splitVariableDeclarations,\n} from './unminify/transforms';\nimport type { Bundle } from './unpack';\nimport { unpackAST } from './unpack';\nimport { isBrowser } from './utils/platform';\n\nexport { type Sandbox } from './deobfuscate';\n\ntype Matchers = typeof m;\n\nexport interface WebcrackResult {\n  code: string;\n  bundle: Bundle | undefined;\n  /**\n   * Save the deobfuscated code and the extracted bundle to the given directory.\n   * @param path Output directory\n   */\n  save(path: string): Promise<void>;\n}\n\nexport interface Options {\n  /**\n   * Decompile react components to JSX.\n   * @default true\n   */\n  jsx?: boolean;\n  /**\n   * Extract modules from the bundle.\n   * @default true\n   */\n  unpack?: boolean;\n  /**\n   * Deobfuscate the code.\n   * @default true\n   */\n  deobfuscate?: boolean;\n  /**\n   * Unminify the code. Required for some of the deobfuscate/unpack/jsx transforms.\n   * @default true\n   */\n  unminify?: boolean;\n  /**\n   * Mangle variable names.\n   * @default false\n   */\n  mangle?: boolean | ((id: string) => boolean);\n  /**\n   * Assigns paths to modules based on the given matchers.\n   * This will also rewrite `require()` calls to use the new paths.\n   *\n   * @example\n   * ```js\n   * m => ({\n   *   './utils/color.js': m.regExpLiteral('^#([0-9a-f]{3}){1,2}$')\n   * })\n   * ```\n   */\n  mappings?: (m: Matchers) => Record<string, m.Matcher<unknown>>;\n  /**\n   * Function that executes a code expression and returns the result (typically from the obfuscator).\n   */\n  sandbox?: Sandbox;\n  /**\n   * @param progress Progress in percent (0-100)\n   */\n  onProgress?: (progress: number) => void;\n}\n\nfunction mergeOptions(options: Options): asserts options is Required<Options> {\n  const mergedOptions: Required<Options> = {\n    jsx: true,\n    unminify: true,\n    unpack: true,\n    deobfuscate: true,\n    mangle: false,\n    mappings: () => ({}),\n    onProgress: () => {},\n    sandbox: isBrowser() ? createBrowserSandbox() : createNodeSandbox(),\n    ...options,\n  };\n  Object.assign(options, mergedOptions);\n}\n\nexport async function webcrack(\n  code: string,\n  options: Options = {},\n): Promise<WebcrackResult> {\n  mergeOptions(options);\n  options.onProgress(0);\n\n  if (isBrowser()) {\n    debug.enable('webcrack:*');\n  }\n\n  const isBookmarklet = /^javascript:./.test(code);\n  if (isBookmarklet) {\n    code = code\n      .replace(/^javascript:/, '')\n      .split(/%(?![a-f\\d]{2})/i)\n      .map(decodeURIComponent)\n      .join('%');\n  }\n\n  let ast: ParseResult<t.File> = null!;\n  let outputCode = '';\n  let bundle: Bundle | undefined;\n\n  const stages = [\n    () => {\n      ast = parse(code, {\n        sourceType: 'unambiguous',\n        allowReturnOutsideFunction: true,\n        errorRecovery: true,\n        plugins: ['jsx'],\n      });\n      if (ast.errors.length) {\n        debug('webcrack:parse')('Errors', ast.errors);\n      }\n    },\n    () => {\n      applyTransforms(\n        ast,\n        [blockStatements, sequence, splitVariableDeclarations, varFunctions],\n        { name: 'prepare' },\n      );\n    },\n    options.deobfuscate &&\n      (() => applyTransformAsync(ast, deobfuscate, options.sandbox)),\n    options.unminify &&\n      (() => {\n        applyTransforms(ast, [transpile, unminify]);\n      }),\n    options.mangle &&\n      (() =>\n        applyTransform(\n          ast,\n          mangle,\n          typeof options.mangle === 'boolean' ? () => true : options.mangle,\n        )),\n    // TODO: Also merge unminify visitor (breaks selfDefending/debugProtection atm)\n    (options.deobfuscate || options.jsx) &&\n      (() => {\n        applyTransforms(\n          ast,\n          [\n            // Have to run this after unminify to properly detect it\n            options.deobfuscate ? [selfDefending, debugProtection] : [],\n            options.jsx ? [jsx, jsxNew] : [],\n          ].flat(),\n        );\n      }),\n    options.deobfuscate &&\n      (() => applyTransforms(ast, [mergeObjectAssignments, evaluateGlobals])),\n    () => (outputCode = generate(ast)),\n    // Unpacking modifies the same AST and may result in imports not at top level\n    // so the code has to be generated before\n    options.unpack && (() => (bundle = unpackAST(ast, options.mappings(m)))),\n  ].filter(Boolean) as (() => unknown)[];\n\n  for (let i = 0; i < stages.length; i++) {\n    await stages[i]();\n    options.onProgress((100 / stages.length) * (i + 1));\n  }\n\n  return {\n    code: outputCode,\n    bundle,\n    async save(path) {\n      const { mkdir, writeFile } = await import('node:fs/promises');\n      path = normalize(path);\n      await mkdir(path, { recursive: true });\n      await writeFile(join(path, 'deobfuscated.js'), outputCode, 'utf8');\n      await bundle?.save(path);\n    },\n  };\n}\n", "import * as t from '@babel/types';\n\nexport function getPropName(node: t.Node): string | undefined {\n  if (t.isIdentifier(node)) {\n    return node.name;\n  }\n  if (t.isStringLiteral(node)) {\n    return node.value;\n  }\n  if (t.isNumericLiteral(node)) {\n    return node.value.toString();\n  }\n}\n", "import module from '@babel/generator/lib/index.js';\n          export default module.default ?? module;\n          export * from '@babel/generator/lib/index.js';", "import type { GeneratorOptions } from '@babel/generator';\nimport babelGenerate from '@babel/generator';\nimport type * as t from '@babel/types';\n\nconst defaultOptions: GeneratorOptions = { jsescOption: { minimal: true } };\n\nexport function generate(\n  ast: t.Node,\n  options: GeneratorOptions = defaultOptions,\n): string {\n  return babelGenerate(ast, options).code;\n}\n\nexport function codePreview(node: t.Node): string {\n  const code = generate(node, {\n    minified: true,\n    shouldPrintComment: () => false,\n    ...defaultOptions,\n  });\n  if (code.length > 100) {\n    return code.slice(0, 70) + ' … ' + code.slice(-30);\n  }\n  return code;\n}\n", "import module from '@babel/traverse/lib/index.js';\n          export default module.default ?? module;\n          export * from '@babel/traverse/lib/index.js';", "import type { Binding, NodePath } from '@babel/traverse';\nimport traverse from '@babel/traverse';\nimport * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport { getPropName } from '.';\nimport { findParent } from './matcher';\n\n/**\n * Replace all references of a variable with the initializer.\n * Example:\n * `const a = 1; console.log(a);` -> `console.log(1);`\n *\n * Example with `unsafeAssignments` being `true`:\n * `let a; a = 2; console.log(a);` -> `console.log(2);`\n *\n * @param unsafeAssignments Also inline assignments to the variable (not guaranteed to be the final value)\n */\nexport function inlineVariable(\n  binding: Binding,\n  value = m.anyExpression(),\n  unsafeAssignments = false,\n) {\n  const varDeclarator = binding.path.node;\n  const varMatcher = m.variableDeclarator(\n    m.identifier(binding.identifier.name),\n    value,\n  );\n  const assignmentMatcher = m.assignmentExpression(\n    '=',\n    m.identifier(binding.identifier.name),\n    value,\n  );\n\n  if (binding.constant && varMatcher.match(varDeclarator)) {\n    binding.referencePaths.forEach((ref) => {\n      ref.replaceWith(varDeclarator.init!);\n    });\n    binding.path.remove();\n  } else if (unsafeAssignments && binding.constantViolations.length >= 1) {\n    const assignments = binding.constantViolations\n      .map((path) => path.node)\n      .filter((node) => assignmentMatcher.match(node));\n    if (!assignments.length) return;\n\n    function getNearestAssignment(location: number) {\n      return assignments.findLast((assignment) => assignment.start! < location);\n    }\n\n    for (const ref of binding.referencePaths) {\n      const assignment = getNearestAssignment(ref.node.start!);\n      if (assignment) ref.replaceWith(assignment.right);\n    }\n\n    for (const path of binding.constantViolations) {\n      if (path.parentPath?.isExpressionStatement()) {\n        path.remove();\n      } else if (path.isAssignmentExpression()) {\n        path.replaceWith(path.node.right);\n      }\n    }\n\n    binding.path.remove();\n  }\n}\n\n/**\n * Make sure the array is immutable and references are valid before using!\n *\n * Example:\n * `const arr = [\"foo\", \"bar\"]; console.log(arr[0]);` -> `console.log(\"foo\");`\n */\nexport function inlineArrayElements(\n  array: t.ArrayExpression,\n  references: NodePath[],\n): void {\n  for (const reference of references) {\n    const memberPath = reference.parentPath! as NodePath<t.MemberExpression>;\n    const property = memberPath.node.property as t.NumericLiteral;\n    const index = property.value;\n    const replacement = array.elements[index]!;\n    memberPath.replaceWith(t.cloneNode(replacement));\n  }\n}\n\nexport function inlineObjectProperties(\n  binding: Binding,\n  property = m.objectProperty(),\n): void {\n  const varDeclarator = binding.path.node;\n  const objectProperties = m.capture(m.arrayOf(property));\n  const varMatcher = m.variableDeclarator(\n    m.identifier(binding.identifier.name),\n    m.objectExpression(objectProperties),\n  );\n  if (!varMatcher.match(varDeclarator)) return;\n\n  const propertyMap = new Map(\n    objectProperties.current!.map((p) => [getPropName(p.key), p.value]),\n  );\n  if (\n    !binding.referencePaths.every((ref) => {\n      const member = ref.parent as t.MemberExpression;\n      const propName = getPropName(member.property)!;\n      return propertyMap.has(propName);\n    })\n  )\n    return;\n\n  binding.referencePaths.forEach((ref) => {\n    const memberPath = ref.parentPath as NodePath<t.MemberExpression>;\n    const propName = getPropName(memberPath.node.property)!;\n    const value = propertyMap.get(propName)!;\n\n    memberPath.replaceWith(value);\n  });\n\n  binding.path.remove();\n}\n\n/**\n * Inline function used in control flow flattening (that only returns an expression)\n * Example:\n * fn: `function (a, b) { return a(b) }`\n * caller: `fn(a, 1)`\n * ->\n * `a(1)`\n */\nexport function inlineFunction(\n  fn: t.FunctionExpression | t.FunctionDeclaration,\n  caller: NodePath<t.CallExpression>,\n): void {\n  if (t.isRestElement(fn.params[1])) {\n    caller.replaceWith(\n      t.callExpression(\n        caller.node.arguments[0] as t.Identifier,\n        caller.node.arguments.slice(1),\n      ),\n    );\n    return;\n  }\n\n  const returnedValue = (fn.body.body[0] as t.ReturnStatement).argument!;\n  const clone = t.cloneNode(returnedValue, true);\n\n  // Inline all arguments\n  traverse(clone, {\n    Identifier(path) {\n      const paramIndex = fn.params.findIndex(\n        (p) => (p as t.Identifier).name === path.node.name,\n      );\n      if (paramIndex !== -1) {\n        path.replaceWith(caller.node.arguments[paramIndex]);\n        path.skip();\n      }\n    },\n    noScope: true,\n  });\n\n  caller.replaceWith(clone);\n}\n\n/**\n * Example:\n * `function alias(a, b) { return decode(b - 938, a); } alias(1071, 1077);`\n * ->\n * `decode(1077 - 938, 1071)`\n */\nexport function inlineFunctionAliases(binding: Binding): { changes: number } {\n  const state = { changes: 0 };\n  const refs = [...binding.referencePaths];\n  for (const ref of refs) {\n    const fn = findParent(ref, m.functionDeclaration());\n\n    // E.g. alias\n    const fnName = m.capture(m.anyString());\n    // E.g. decode(b - 938, a)\n    const returnedCall = m.capture(\n      m.callExpression(\n        m.identifier(binding.identifier.name),\n        m.anyList(m.slice({ min: 2 })),\n      ),\n    );\n    const matcher = m.functionDeclaration(\n      m.identifier(fnName),\n      m.anyList(m.slice({ min: 2 })),\n      m.blockStatement([m.returnStatement(returnedCall)]),\n    );\n\n    if (fn && matcher.match(fn.node)) {\n      // Avoid false positives of functions that return a string\n      // It's only a wrapper if the function's params are used in the decode call\n      const paramUsedInDecodeCall = fn.node.params.some((param) => {\n        const binding = fn.scope.getBinding((param as t.Identifier).name);\n        return binding?.referencePaths.some((ref) =>\n          ref.findParent((p) => p.node === returnedCall.current),\n        );\n      });\n      if (!paramUsedInDecodeCall) continue;\n\n      const fnBinding = fn.scope.parent.getBinding(fnName.current!);\n      if (!fnBinding) continue;\n      // Check all further aliases (`function alias2(a, b) { return alias(a - 1, b + 3); }`)\n      const fnRefs = fnBinding.referencePaths;\n      refs.push(...fnRefs);\n\n      // E.g. [alias(1071, 1077), alias(1, 2)]\n      const callRefs = fnRefs\n        .filter(\n          (ref) =>\n            t.isCallExpression(ref.parent) &&\n            t.isIdentifier(ref.parent.callee, { name: fnName.current! }),\n        )\n        .map((ref) => ref.parentPath!) as NodePath<t.CallExpression>[];\n\n      for (const callRef of callRefs) {\n        inlineFunction(fn.node, callRef);\n        state.changes++;\n      }\n\n      fn.remove();\n      state.changes++;\n    }\n  }\n\n  // Have to crawl again because renaming messed up the references\n  binding.scope.crawl();\n  return state;\n}\n\n/**\n * Recursively renames all references to the binding.\n * Make sure the binding name isn't shadowed anywhere!\n *\n * Example: `var alias = decoder; alias(1);` -> `decoder(1);`\n */\n\nexport function inlineVariableAliases(\n  binding: Binding,\n  targetName = binding.identifier.name,\n): { changes: number } {\n  const state = { changes: 0 };\n  const refs = [...binding.referencePaths];\n  const varName = m.capture(m.anyString());\n  const matcher = m.or(\n    m.variableDeclarator(\n      m.identifier(varName),\n      m.identifier(binding.identifier.name),\n    ),\n    m.assignmentExpression(\n      '=',\n      m.identifier(varName),\n      m.identifier(binding.identifier.name),\n    ),\n  );\n\n  for (const ref of refs) {\n    if (matcher.match(ref.parent)) {\n      const varScope = ref.scope;\n      const varBinding = varScope.getBinding(varName.current!);\n      if (!varBinding) continue;\n      // Avoid infinite loop from `alias = alias;` (caused by dead code injection?)\n      if (ref.isIdentifier({ name: varBinding.identifier.name })) continue;\n\n      // Check all further aliases (`var alias2 = alias;`)\n      state.changes += inlineVariableAliases(varBinding, targetName).changes;\n\n      if (ref.parentPath?.isAssignmentExpression()) {\n        // Remove `var alias;` when the assignment happens separately\n        varBinding.path.remove();\n\n        if (t.isExpressionStatement(ref.parentPath.parent)) {\n          // Remove `alias = decoder;`\n          ref.parentPath.remove();\n        } else {\n          // Replace `(alias = decoder)(1);` with `decoder(1);`\n          ref.parentPath.replaceWith(t.identifier(targetName));\n        }\n      } else if (ref.parentPath?.isVariableDeclarator()) {\n        // Remove `alias = decoder;` of declarator\n        ref.parentPath.remove();\n      }\n      state.changes++;\n    } else {\n      // Rename the reference\n      ref.replaceWith(t.identifier(targetName));\n      state.changes++;\n    }\n  }\n\n  return state;\n}\n", "import type { Binding, NodePath } from '@babel/traverse';\nimport * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\n\n/**\n * Matches any literal except for template literals with expressions (that could have side effects)\n */\nexport const safeLiteral: m.Matcher<t.Literal> = m.matcher(\n  (node) =>\n    t.isLiteral(node) &&\n    (!t.isTemplateLiteral(node) || node.expressions.length === 0),\n);\n\nexport function infiniteLoop(\n  body?: m.Matcher<t.Statement>,\n): m.Matcher<t.ForStatement | t.WhileStatement> {\n  return m.or(\n    m.forStatement(undefined, null, undefined, body),\n    m.forStatement(undefined, truthyMatcher, undefined, body),\n    m.whileStatement(truthyMatcher, body),\n  );\n}\n\nexport function constKey(\n  name?: string | m.Matcher<string>,\n): m.Matcher<t.Identifier | t.StringLiteral> {\n  return m.or(m.identifier(name), m.stringLiteral(name));\n}\n\nexport function constObjectProperty(\n  value?: m.Matcher<t.Expression>,\n): m.Matcher<t.ObjectProperty> {\n  return m.or(\n    m.objectProperty(m.identifier(), value, false),\n    m.objectProperty(m.or(m.stringLiteral(), m.numericLiteral()), value),\n  );\n}\n\nexport function anonymousFunction(\n  params?:\n    | m.Matcher<(t.Identifier | t.RestElement | t.Pattern)[]>\n    | (\n        | m.Matcher<t.Identifier>\n        | m.Matcher<t.Pattern>\n        | m.Matcher<t.RestElement>\n      )[],\n  body?: m.Matcher<t.BlockStatement>,\n): m.Matcher<t.FunctionExpression | t.ArrowFunctionExpression> {\n  return m.or(\n    m.functionExpression(null, params, body, false),\n    m.arrowFunctionExpression(params, body),\n  );\n}\n\nexport function iife(\n  params?:\n    | m.Matcher<(t.Identifier | t.RestElement | t.Pattern)[]>\n    | (\n        | m.Matcher<t.Identifier>\n        | m.Matcher<t.Pattern>\n        | m.Matcher<t.RestElement>\n      )[],\n  body?: m.Matcher<t.BlockStatement>,\n): m.Matcher<t.CallExpression> {\n  return m.callExpression(anonymousFunction(params, body));\n}\n\n/**\n * Matches both identifier properties and string literal computed properties\n */\nexport function constMemberExpression(\n  object: string | m.Matcher<t.Expression>,\n  property?: string | m.Matcher<string>,\n): m.Matcher<t.MemberExpression> {\n  if (typeof object === 'string') object = m.identifier(object);\n  return m.or(\n    m.memberExpression(object, m.identifier(property), false),\n    m.memberExpression(object, m.stringLiteral(property), true),\n  );\n}\n\nexport const undefinedMatcher = m.or(\n  m.identifier('undefined'),\n  m.unaryExpression('void', m.numericLiteral(0)),\n);\n\nexport const trueMatcher = m.or(\n  m.booleanLiteral(true),\n  m.unaryExpression('!', m.numericLiteral(0)),\n  m.unaryExpression('!', m.unaryExpression('!', m.numericLiteral(1))),\n  m.unaryExpression('!', m.unaryExpression('!', m.arrayExpression([]))),\n);\n\nexport const falseMatcher = m.or(\n  m.booleanLiteral(false),\n  m.unaryExpression('!', m.arrayExpression([])),\n);\n\nexport const truthyMatcher = m.or(trueMatcher, m.arrayExpression([]));\n\n/**\n * Starting at the parent path of the current `NodePath` and going up the\n * tree, return the first `NodePath` that causes the provided `matcher`\n * to return true, or `null` if the `matcher` never returns true.\n */\nexport function findParent<T extends t.Node>(\n  path: NodePath,\n  matcher: m.Matcher<T>,\n): NodePath<T> | null {\n  return path.findParent((path) =>\n    matcher.match(path.node),\n  ) as NodePath<T> | null;\n}\n\n/**\n * Starting at current `NodePath` and going up the tree, return the first\n * `NodePath` that causes the provided `matcher` to return true,\n * or `null` if the `matcher` never returns true.\n */\nexport function findPath<T extends t.Node>(\n  path: NodePath,\n  matcher: m.Matcher<T>,\n): NodePath<T> | null {\n  return path.find((path) => matcher.match(path.node)) as NodePath<T> | null;\n}\n\n/**\n * Function expression matcher that captures the parameters\n * and allows them to be referenced in the body.\n */\nexport function createFunctionMatcher(\n  params: number,\n  body: (\n    ...captures: m.Matcher<t.Identifier>[]\n  ) => m.Matcher<t.Statement[]> | m.Matcher<t.Statement>[],\n): m.Matcher<t.FunctionExpression> {\n  const captures = Array.from({ length: params }, () =>\n    m.capture(m.anyString()),\n  );\n\n  return m.functionExpression(\n    undefined,\n    captures.map(m.identifier),\n    m.blockStatement(\n      body(...captures.map((c) => m.identifier(m.fromCapture(c)))),\n    ),\n  );\n}\n\n/**\n * Returns true if every reference is a member expression whose value is read\n */\nexport function isReadonlyObject(\n  binding: Binding,\n  memberAccess: m.Matcher<t.MemberExpression>,\n): boolean {\n  // Workaround because sometimes babel treats the VariableDeclarator/binding itself as a violation\n  if (!binding.constant && binding.constantViolations[0] !== binding.path)\n    return false;\n\n  function isPatternAssignment(member: NodePath<t.Node>) {\n    return (\n      // [obj.property] = [1];\n      member.parentPath?.isArrayPattern() ||\n      // ([obj.property = 1] = [])\n      member.parentPath?.isAssignmentPattern() ||\n      // ({ property: obj.property } = {})\n      member.parentPath?.parentPath?.isObjectPattern() ||\n      // ({ property: obj.property = 1 } = {})\n      member.parentPath?.isAssignmentPattern()\n    );\n  }\n\n  return binding.referencePaths.every(\n    (path) =>\n      // obj.property\n      memberAccess.match(path.parent) &&\n      // obj.property = 1\n      !path.parentPath?.parentPath?.isAssignmentExpression({\n        left: path.parent,\n      }) &&\n      // obj.property++\n      !path.parentPath?.parentPath?.isUpdateExpression({\n        argument: path.parent,\n      }) &&\n      // delete obj.property\n      !path.parentPath?.parentPath?.isUnaryExpression({\n        argument: path.parent,\n        operator: 'delete',\n      }) &&\n      !isPatternAssignment(path.parentPath!),\n  );\n}\n\n/**\n * Checks if the binding is a temporary variable that is only assigned\n * once and has limited references. Often created by transpilers.\n *\n * Example with 1 reference to `_tmp`:\n * ```js\n * var _tmp; x[_tmp = y] || (x[_tmp] = z);\n * ```\n */\nexport function isTemporaryVariable(\n  binding: Binding | undefined,\n  references: number,\n  kind: 'var' | 'param' = 'var',\n): binding is Binding {\n  return (\n    binding !== undefined &&\n    binding.references === references &&\n    binding.constantViolations.length === 1 &&\n    (kind === 'var'\n      ? binding.path.isVariableDeclarator() && binding.path.node.init === null\n      : binding.path.listKey === 'params' && binding.path.isIdentifier())\n  );\n}\n", "import type { Binding, NodePath } from '@babel/traverse';\nimport traverse from '@babel/traverse';\nimport * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport { codePreview } from './generator';\n\nexport function renameFast(binding: Binding, newName: string): void {\n  binding.referencePaths.forEach((ref) => {\n    if (ref.isExportDefaultDeclaration()) return;\n    if (!ref.isIdentifier()) {\n      throw new Error(\n        `Unexpected reference (${ref.type}): ${codePreview(ref.node)}`,\n      );\n    }\n\n    // To avoid conflicts with other bindings of the same name\n    if (ref.scope.hasBinding(newName)) ref.scope.rename(newName);\n    ref.node.name = newName;\n  });\n\n  // Also update assignments\n  const patternMatcher = m.assignmentExpression(\n    '=',\n    m.or(m.arrayPattern(), m.objectPattern()),\n  );\n  binding.constantViolations.forEach((ref) => {\n    // To avoid conflicts with other bindings of the same name\n    if (ref.scope.hasBinding(newName)) ref.scope.rename(newName);\n\n    if (ref.isAssignmentExpression() && t.isIdentifier(ref.node.left)) {\n      ref.node.left.name = newName;\n    } else if (ref.isUpdateExpression() && t.isIdentifier(ref.node.argument)) {\n      ref.node.argument.name = newName;\n    } else if (\n      ref.isUnaryExpression({ operator: 'delete' }) &&\n      t.isIdentifier(ref.node.argument)\n    ) {\n      ref.node.argument.name = newName;\n    } else if (ref.isVariableDeclarator() && t.isIdentifier(ref.node.id)) {\n      ref.node.id.name = newName;\n    } else if (ref.isVariableDeclarator() && t.isArrayPattern(ref.node.id)) {\n      const ids = ref.getBindingIdentifiers();\n      for (const id in ids) {\n        if (id === binding.identifier.name) {\n          ids[id].name = newName;\n        }\n      }\n    } else if (ref.isFor() || patternMatcher.match(ref.node)) {\n      traverse(ref.node, {\n        Identifier(path) {\n          if (path.scope !== ref.scope) return path.skip();\n          if (path.node.name === binding.identifier.name) {\n            path.node.name = newName;\n          }\n        },\n        noScope: true,\n      });\n    } else if (ref.isFunctionDeclaration() && t.isIdentifier(ref.node.id)) {\n      ref.node.id.name = newName;\n    } else {\n      throw new Error(\n        `Unexpected constant violation (${ref.type}): ${codePreview(ref.node)}`,\n      );\n    }\n  });\n\n  binding.scope.removeOwnBinding(binding.identifier.name);\n  binding.scope.bindings[newName] = binding;\n  binding.identifier.name = newName;\n}\n\nexport function renameParameters(\n  path: NodePath<t.Function>,\n  newNames: string[],\n): void {\n  const params = path.node.params as t.Identifier[];\n  for (let i = 0; i < Math.min(params.length, newNames.length); i++) {\n    const binding = path.scope.getBinding(params[i].name)!;\n    renameFast(binding, newNames[i]);\n  }\n}\n", "import type { Node, TraverseOptions, Visitor } from '@babel/traverse';\nimport traverse, { visitors } from '@babel/traverse';\nimport debug from 'debug';\n\nconst logger = debug('webcrack:transforms');\n\nexport async function applyTransformAsync<TOptions>(\n  ast: Node,\n  transform: AsyncTransform<TOptions>,\n  options?: TOptions,\n): Promise<TransformState> {\n  logger(`${transform.name}: started`);\n  const state: TransformState = { changes: 0 };\n\n  await transform.run?.(ast, state, options);\n  if (transform.visitor)\n    traverse(ast, transform.visitor(options), undefined, state);\n\n  logger(`${transform.name}: finished with ${state.changes} changes`);\n  return state;\n}\n\nexport function applyTransform<TOptions>(\n  ast: Node,\n  transform: Transform<TOptions>,\n  options?: TOptions,\n): TransformState {\n  logger(`${transform.name}: started`);\n  const state: TransformState = { changes: 0 };\n  transform.run?.(ast, state, options);\n\n  if (transform.visitor) {\n    const visitor = transform.visitor(\n      options,\n    ) as TraverseOptions<TransformState>;\n    visitor.noScope = !transform.scope;\n    traverse(ast, visitor, undefined, state);\n  }\n\n  logger(`${transform.name}: finished with ${state.changes} changes`);\n  return state;\n}\n\nexport function applyTransforms(\n  ast: Node,\n  transforms: Transform[],\n  options: { noScope?: boolean; name?: string; log?: boolean } = {},\n): TransformState {\n  options.log ??= true;\n  const name = options.name ?? transforms.map((t) => t.name).join(', ');\n  if (options.log) logger(`${name}: started`);\n  const state: TransformState = { changes: 0 };\n\n  for (const transform of transforms) {\n    transform.run?.(ast, state);\n  }\n\n  const traverseOptions = transforms.flatMap((t) => t.visitor?.() ?? []);\n  if (traverseOptions.length > 0) {\n    const visitor: TraverseOptions<TransformState> =\n      visitors.merge(traverseOptions);\n    visitor.noScope = options.noScope || transforms.every((t) => !t.scope);\n    traverse(ast, visitor, undefined, state);\n  }\n\n  if (options.log) logger(`${name}: finished with ${state.changes} changes`);\n  return state;\n}\n\nexport function mergeTransforms(options: {\n  name: string;\n  tags: Tag[];\n  transforms: Transform[];\n}): Transform {\n  return {\n    name: options.name,\n    tags: options.tags,\n    scope: options.transforms.some((t) => t.scope),\n    visitor() {\n      return visitors.merge(\n        options.transforms.flatMap((t) => t.visitor?.() ?? []),\n      );\n    },\n  };\n}\n\nexport interface TransformState {\n  changes: number;\n}\n\nexport interface Transform<TOptions = unknown> {\n  name: string;\n  tags: Tag[];\n  scope?: boolean;\n  run?: (ast: Node, state: TransformState, options?: TOptions) => void;\n  visitor?: (options?: TOptions) => Visitor<TransformState>;\n}\n\nexport interface AsyncTransform<TOptions = unknown>\n  extends Transform<TOptions> {\n  run?: (ast: Node, state: TransformState, options?: TOptions) => Promise<void>;\n}\n\nexport type Tag = 'safe' | 'unsafe';\n", "import debug from 'debug';\nimport type { AsyncTransform } from '../ast-utils';\nimport {\n  applyTransform,\n  applyTransformAsync,\n  applyTransforms,\n} from '../ast-utils';\nimport mergeStrings from '../unminify/transforms/merge-strings';\nimport { findArrayRotator } from './array-rotator';\nimport controlFlowObject from './control-flow-object';\nimport controlFlowSwitch from './control-flow-switch';\nimport deadCode from './dead-code';\nimport { findDecoders } from './decoder';\nimport inlineDecodedStrings from './inline-decoded-strings';\nimport inlineDecoderWrappers from './inline-decoder-wrappers';\nimport inlineObjectProps from './inline-object-props';\nimport { findStringArray } from './string-array';\nimport type { Sandbox } from './vm';\nimport { VMDecoder, createBrowserSandbox, createNodeSandbox } from './vm';\n\nexport { createBrowserSandbox, createNodeSandbox, type Sandbox };\n\n// https://astexplorer.net/#/gist/b1018df4a8daebfcb1daf9d61fe17557/4ff9ad0e9c40b9616956f17f59a2d9888cd62a4f\n\nexport default {\n  name: 'deobfuscate',\n  tags: ['unsafe'],\n  scope: true,\n  async run(ast, state, sandbox) {\n    if (!sandbox) return;\n\n    const logger = debug('webcrack:deobfuscate');\n    const stringArray = findStringArray(ast);\n    logger(\n      stringArray\n        ? `String Array: ${stringArray.originalName}, length ${stringArray.length}`\n        : 'String Array: no',\n    );\n    if (!stringArray) return;\n\n    const rotator = findArrayRotator(stringArray);\n    logger(`String Array Rotate: ${rotator ? 'yes' : 'no'}`);\n\n    const decoders = findDecoders(stringArray);\n    logger(\n      `String Array Decoders: ${decoders\n        .map((d) => d.originalName)\n        .join(', ')}`,\n    );\n\n    state.changes += applyTransform(ast, inlineObjectProps).changes;\n\n    for (const decoder of decoders) {\n      state.changes += applyTransform(\n        ast,\n        inlineDecoderWrappers,\n        decoder.path,\n      ).changes;\n    }\n\n    const vm = new VMDecoder(sandbox, stringArray, decoders, rotator);\n    state.changes += (\n      await applyTransformAsync(ast, inlineDecodedStrings, { vm })\n    ).changes;\n\n    if (decoders.length > 0) {\n      stringArray.path.remove();\n      rotator?.remove();\n      decoders.forEach((decoder) => decoder.path.remove());\n      state.changes += 2 + decoders.length;\n    }\n\n    state.changes += applyTransforms(\n      ast,\n      [mergeStrings, deadCode, controlFlowObject, controlFlowSwitch],\n      { noScope: true },\n    ).changes;\n  },\n} satisfies AsyncTransform<Sandbox>;\n", "import * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\n\n// \"a\" + \"b\" -> \"ab\"\n// (a + \"b\") + \"c\" -> a + \"bc\"\n//  left ^      ^ right (path)\nexport default {\n  name: 'merge-strings',\n  tags: ['safe'],\n  visitor() {\n    const left = m.capture(m.stringLiteral());\n    const right = m.capture(m.stringLiteral());\n\n    const matcher = m.binaryExpression(\n      '+',\n      m.or(left, m.binaryExpression('+', m.anything(), left)),\n      right,\n    );\n\n    return {\n      BinaryExpression: {\n        exit(path) {\n          if (!matcher.match(path.node)) return;\n          left.current!.value += right.current!.value;\n          right.current!.value = ''; // Otherwise it concatenates multiple times for some reason\n          path.replaceWith(path.node.left);\n          path.skip();\n          this.changes++;\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import type { NodePath } from '@babel/traverse';\nimport type * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport { callExpression } from '@codemod/matchers';\nimport {\n  constMemberExpression,\n  findParent,\n  iife,\n  infiniteLoop,\n} from '../ast-utils';\nimport type { StringArray } from './string-array';\n\nexport type ArrayRotator = NodePath<t.ExpressionStatement>;\n\n/**\n * Structure:\n * ```\n * iife (>= 2 parameters, called with 0 or 2 arguments)\n *  2 variable declarations (array and decoder)\n *  endless loop:\n *   try:\n *    if/break/parseInt/array.push(array.shift())\n *   catch:\n *    array.push(array.shift())\n * ```\n */\nexport function findArrayRotator(\n  stringArray: StringArray,\n): ArrayRotator | undefined {\n  // e.g. 'array'\n  const arrayIdentifier = m.capture(m.identifier());\n\n  // e.g. array.push(array.shift())\n  const pushShift = m.callExpression(\n    constMemberExpression(arrayIdentifier, 'push'),\n    [\n      m.callExpression(\n        constMemberExpression(m.fromCapture(arrayIdentifier), 'shift'),\n      ),\n    ],\n  );\n\n  const callMatcher = iife(\n    m.anything(),\n    m.blockStatement(\n      m.anyList(\n        m.zeroOrMore(),\n        infiniteLoop(\n          m.matcher((node) => {\n            return (\n              m\n                .containerOf(callExpression(m.identifier('parseInt')))\n                .match(node) &&\n              m\n                .blockStatement([\n                  m.tryStatement(\n                    m.containerOf(pushShift),\n                    m.containerOf(pushShift),\n                  ),\n                ])\n                .match(node)\n            );\n          }),\n        ),\n      ),\n    ),\n  );\n\n  const matcher = m.expressionStatement(\n    m.or(callMatcher, m.unaryExpression('!', callMatcher)),\n  );\n\n  for (const ref of stringArray.references) {\n    const rotator = findParent(ref, matcher);\n    if (rotator) {\n      return rotator;\n    }\n  }\n}\n", "import type { Binding, NodePath } from '@babel/traverse';\nimport type { FunctionExpression } from '@babel/types';\nimport * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../ast-utils';\nimport {\n  applyTransform,\n  constKey,\n  constMemberExpression,\n  createFunctionMatcher,\n  findParent,\n  getPropName,\n  inlineFunction,\n  isReadonlyObject,\n} from '../ast-utils';\nimport mergeStrings from '../unminify/transforms/merge-strings';\n\n/**\n * Explanation: https://excalidraw.com/#json=0vehUdrfSS635CNPEQBXl,hDOd-UO9ETfSDWT9MxVX-A\n */\n\nexport default {\n  name: 'control-flow-object',\n  tags: ['safe'],\n  scope: true,\n  visitor() {\n    const varId = m.capture(m.identifier());\n    const propertyName = m.matcher<string>((name) => /^[a-z]{5}$/i.test(name));\n    const propertyKey = constKey(propertyName);\n    const propertyValue = m.or(\n      // E.g. \"6|0|4|3|1|5|2\"\n      m.stringLiteral(),\n      // E.g. function (a, b) { return a + b }\n      createFunctionMatcher(2, (left, right) => [\n        m.returnStatement(\n          m.or(\n            m.binaryExpression(undefined, left, right),\n            m.logicalExpression(undefined, left, right),\n            m.binaryExpression(undefined, right, left),\n            m.logicalExpression(undefined, right, left),\n          ),\n        ),\n      ]),\n      // E.g. function (a, b, c) { return a(b, c) } with an arbitrary number of arguments\n      m.matcher<FunctionExpression>((node) => {\n        return (\n          t.isFunctionExpression(node) &&\n          createFunctionMatcher(node.params.length, (...params) => [\n            m.returnStatement(m.callExpression(params[0], params.slice(1))),\n          ]).match(node)\n        );\n      }),\n      // E.g. function (a, ...b) { return a(...b) }\n      (() => {\n        const fnName = m.capture(m.identifier());\n        const restName = m.capture(m.identifier());\n\n        return m.functionExpression(\n          undefined,\n          [fnName, m.restElement(restName)],\n          m.blockStatement([\n            m.returnStatement(\n              m.callExpression(m.fromCapture(fnName), [\n                m.spreadElement(m.fromCapture(restName)),\n              ]),\n            ),\n          ]),\n        );\n      })(),\n    );\n    // E.g. \"rLxJs\": \"6|0|4|3|1|5|2\"\n    const objectProperties = m.capture(\n      m.arrayOf(m.objectProperty(propertyKey, propertyValue)),\n    );\n    const aliasId = m.capture(m.identifier());\n    const aliasVar = m.variableDeclaration(m.anything(), [\n      m.variableDeclarator(aliasId, m.fromCapture(varId)),\n    ]);\n    // E.g. \"rLxJs\"\n    const assignedKey = m.capture(propertyName);\n    // E.g. \"6|0|4|3|1|5|2\"\n    const assignedValue = m.capture(propertyValue);\n    // E.g. obj.rLxJs = \"6|0|4|3|1|5|2\"\n    const assignment = m.expressionStatement(\n      m.assignmentExpression(\n        '=',\n        constMemberExpression(m.fromCapture(varId), assignedKey),\n        assignedValue,\n      ),\n    );\n    const looseAssignment = m.expressionStatement(\n      m.assignmentExpression(\n        '=',\n        constMemberExpression(m.fromCapture(varId), assignedKey),\n      ),\n    );\n    // E.g. obj.rLxJs\n    const memberAccess = constMemberExpression(\n      m.or(m.fromCapture(varId), m.fromCapture(aliasId)),\n      propertyName,\n    );\n    const varMatcher = m.variableDeclarator(\n      varId,\n      m.objectExpression(objectProperties),\n    );\n    // Example: { YhxvC: \"default\" }.YhxvC\n    const inlineMatcher = constMemberExpression(\n      m.objectExpression(objectProperties),\n      propertyName,\n    );\n\n    function isConstantBinding(binding: Binding) {\n      // Workaround because sometimes babel treats the VariableDeclarator/binding itself as a violation\n      return binding.constant || binding.constantViolations[0] === binding.path;\n    }\n\n    function transform(path: NodePath<t.VariableDeclarator>) {\n      let changes = 0;\n      if (varMatcher.match(path.node)) {\n        // Verify all references to make sure they match how the obfuscator\n        // would have generated the code (no reassignments, etc.)\n        const binding = path.scope.getBinding(varId.current!.name);\n        if (!binding) return changes;\n        if (!isConstantBinding(binding)) return changes;\n        if (!transformObjectKeys(binding)) return changes;\n        if (!isReadonlyObject(binding, memberAccess)) return changes;\n\n        const props = new Map(\n          objectProperties.current!.map((p) => [\n            getPropName(p.key),\n            p.value as t.FunctionExpression | t.StringLiteral,\n          ]),\n        );\n        if (!props.size) return changes;\n\n        const oldRefs = [...binding.referencePaths];\n\n        // Have to loop backwards because we might replace a node that\n        // contains another reference to the binding (https://github.com/babel/babel/issues/12943)\n        [...binding.referencePaths].reverse().forEach((ref) => {\n          const memberPath = ref.parentPath as NodePath<t.MemberExpression>;\n          const propName = getPropName(memberPath.node.property)!;\n          const value = props.get(propName);\n          if (!value) {\n            ref.addComment('leading', 'webcrack:control_flow_missing_prop');\n            return;\n          }\n\n          if (t.isStringLiteral(value)) {\n            memberPath.replaceWith(value);\n          } else {\n            inlineFunction(\n              value,\n              memberPath.parentPath as NodePath<t.CallExpression>,\n            );\n          }\n          changes++;\n        });\n\n        oldRefs.forEach((ref) => {\n          const varDeclarator = findParent(ref, m.variableDeclarator());\n          if (varDeclarator) changes += transform(varDeclarator);\n        });\n\n        path.remove();\n        changes++;\n      }\n      return changes;\n    }\n\n    /**\n     * When the `Transform Object Keys` option is enabled, the obfuscator generates an empty\n     * object, assigns the properties later and adds an alias variable to the object.\n     * This function undoes that by converting the assignments to inline object properties.\n     *\n     * In some forked versions of the obfuscator, some properties may be in the object\n     * and others are assigned later.\n     */\n    function transformObjectKeys(objBinding: Binding): boolean {\n      const container = objBinding.path.parentPath!.container as t.Statement[];\n      const startIndex = (objBinding.path.parentPath!.key as number) + 1;\n      const properties: t.ObjectProperty[] = [];\n\n      for (let i = startIndex; i < container.length; i++) {\n        const statement = container[i];\n\n        // Example: _0x29d709[\"kHAOU\"] = \"5|1|2\" + \"|4|3|\" + \"0|6\";\n        // For performance reasons, only traverse if it is a potential match (value doesn't matter)\n        if (looseAssignment.match(statement)) {\n          applyTransform(statement, mergeStrings);\n        }\n\n        if (assignment.match(statement)) {\n          properties.push(\n            t.objectProperty(\n              t.identifier(assignedKey.current!),\n              assignedValue.current!,\n            ),\n          );\n        } else {\n          break;\n        }\n      }\n\n      // If all properties are in the object then there typically won't be an alias variable\n      const aliasAssignment = container[startIndex + properties.length];\n      if (!aliasVar.match(aliasAssignment)) return true;\n\n      // Avoid false positives\n      if (objBinding.references !== properties.length + 1) return false;\n\n      const aliasBinding = objBinding.scope.getBinding(aliasId.current!.name)!;\n      if (!isReadonlyObject(aliasBinding, memberAccess)) return false;\n\n      objectProperties.current!.push(...properties);\n      container.splice(startIndex, properties.length);\n      objBinding.referencePaths = aliasBinding.referencePaths;\n      objBinding.references = aliasBinding.references;\n      objBinding.identifier.name = aliasBinding.identifier.name;\n      aliasBinding.path.remove();\n      return true;\n    }\n\n    return {\n      VariableDeclarator: {\n        exit(path) {\n          this.changes += transform(path);\n        },\n      },\n      MemberExpression: {\n        exit(path) {\n          if (!inlineMatcher.match(path.node)) return;\n\n          const propName = getPropName(path.node.property)!;\n          const value = objectProperties.current!.find(\n            (prop) => getPropName(prop.key) === propName,\n          )?.value as t.FunctionExpression | t.StringLiteral | undefined;\n          if (!value) return;\n\n          if (t.isStringLiteral(value)) {\n            path.replaceWith(value);\n          } else if (path.parentPath.isCallExpression()) {\n            inlineFunction(value, path.parentPath);\n          } else {\n            path.replaceWith(value);\n          }\n          this.changes++;\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../ast-utils';\nimport { constMemberExpression, infiniteLoop } from '../ast-utils';\n\nexport default {\n  name: 'control-flow-switch',\n  tags: ['safe'],\n  visitor() {\n    const sequenceName = m.capture(m.identifier());\n    const sequenceString = m.capture(\n      m.matcher<string>((s) => /^\\d+(\\|\\d+)*$/.test(s)),\n    );\n    const iterator = m.capture(m.identifier());\n\n    const cases = m.capture(\n      m.arrayOf(\n        m.switchCase(\n          m.stringLiteral(m.matcher((s) => /^\\d+$/.test(s))),\n          m.anyList(\n            m.zeroOrMore(),\n            m.or(m.continueStatement(), m.returnStatement()),\n          ),\n        ),\n      ),\n    );\n\n    const matcher = m.blockStatement(\n      m.anyList<t.Statement>(\n        // E.g. const sequence = \"2|4|3|0|1\".split(\"|\")\n        m.variableDeclaration(undefined, [\n          m.variableDeclarator(\n            sequenceName,\n            m.callExpression(\n              constMemberExpression(m.stringLiteral(sequenceString), 'split'),\n              [m.stringLiteral('|')],\n            ),\n          ),\n        ]),\n        // E.g. let iterator = 0 or -0x1a70 + 0x93d + 0x275 * 0x7\n        m.variableDeclaration(undefined, [m.variableDeclarator(iterator)]),\n        infiniteLoop(\n          m.blockStatement([\n            m.switchStatement(\n              // E.g. switch (sequence[iterator++]) {\n              m.memberExpression(\n                m.fromCapture(sequenceName),\n                m.updateExpression('++', m.fromCapture(iterator)),\n                true,\n              ),\n              cases,\n            ),\n            m.breakStatement(),\n          ]),\n        ),\n        m.zeroOrMore(),\n      ),\n    );\n\n    return {\n      BlockStatement: {\n        exit(path) {\n          if (!matcher.match(path.node)) return;\n\n          const caseStatements = new Map(\n            cases.current!.map((c) => [\n              (c.test as t.StringLiteral).value,\n              t.isContinueStatement(c.consequent.at(-1))\n                ? c.consequent.slice(0, -1)\n                : c.consequent,\n            ]),\n          );\n\n          const sequence = sequenceString.current!.split('|');\n          const newStatements = sequence.flatMap((s) => caseStatements.get(s)!);\n\n          path.node.body.splice(0, 3, ...newStatements);\n          this.changes += newStatements.length + 3;\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import type { NodePath } from '@babel/traverse';\nimport * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../ast-utils';\nimport { renameFast } from '../ast-utils';\n\nexport default {\n  name: 'dead-code',\n  tags: ['unsafe'],\n  scope: true,\n  visitor() {\n    const stringComparison = m.binaryExpression(\n      m.or('===', '==', '!==', '!='),\n      m.stringLiteral(),\n      m.stringLiteral(),\n    );\n    const testMatcher = m.or(\n      stringComparison,\n      m.unaryExpression('!', stringComparison),\n    );\n\n    return {\n      'IfStatement|ConditionalExpression': {\n        exit(_path) {\n          const path = _path as NodePath<\n            t.IfStatement | t.ConditionalExpression\n          >;\n\n          if (!testMatcher.match(path.node.test)) return;\n\n          if (path.get('test').evaluateTruthy()) {\n            replace(path, path.get('consequent'));\n          } else if (path.node.alternate) {\n            replace(path, path.get('alternate') as NodePath);\n          } else {\n            path.remove();\n          }\n\n          this.changes++;\n        },\n      },\n    };\n  },\n} satisfies Transform;\n\nfunction replace(path: NodePath<t.Conditional>, replacement: NodePath) {\n  if (t.isBlockStatement(replacement.node)) {\n    // If statements can contain variables that shadow variables in the parent scope.\n    // Since the block scope is merged with the parent scope, we need to rename those\n    // variables to avoid duplicate declarations.\n    const childBindings = replacement.scope.bindings;\n    for (const name in childBindings) {\n      const binding = childBindings[name];\n      if (path.scope.hasOwnBinding(name)) {\n        renameFast(binding, path.scope.generateUid(name));\n      }\n      binding.scope = path.scope;\n      path.scope.bindings[binding.identifier.name] = binding;\n    }\n    path.replaceWithMultiple(replacement.node.body);\n  } else {\n    path.replaceWith(replacement);\n  }\n}\n", "import { expression } from '@babel/template';\nimport type { NodePath } from '@babel/traverse';\nimport type * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport { findParent, inlineVariable, renameFast } from '../ast-utils';\nimport type { StringArray } from './string-array';\n\n/**\n * A function that is called with >= 1 numeric/string arguments\n * and returns a string from the string array. It may also decode\n * the string with Base64 or RC4.\n */\nexport class Decoder {\n  originalName: string;\n  name: string;\n  path: NodePath<t.FunctionDeclaration>;\n\n  constructor(\n    originalName: string,\n    name: string,\n    path: NodePath<t.FunctionDeclaration>,\n  ) {\n    this.originalName = originalName;\n    this.name = name;\n    this.path = path;\n  }\n\n  collectCalls(): NodePath<t.CallExpression>[] {\n    const calls: NodePath<t.CallExpression>[] = [];\n\n    const literalArgument: m.Matcher<t.Expression> = m.or(\n      m.binaryExpression(\n        m.anything(),\n        m.matcher((node) => literalArgument.match(node)),\n        m.matcher((node) => literalArgument.match(node)),\n      ),\n      m.unaryExpression(\n        '-',\n        m.matcher((node) => literalArgument.match(node)),\n      ),\n      m.numericLiteral(),\n      m.stringLiteral(),\n    );\n\n    const literalCall = m.callExpression(\n      m.identifier(this.name),\n      m.arrayOf(literalArgument),\n    );\n    const expressionCall = m.callExpression(\n      m.identifier(this.name),\n      m.arrayOf(m.anyExpression()),\n    );\n\n    const conditional = m.capture(m.conditionalExpression());\n    const conditionalCall = m.callExpression(m.identifier(this.name), [\n      conditional,\n    ]);\n\n    const buildExtractedConditional = expression`TEST ? CALLEE(CONSEQUENT) : CALLEE(ALTERNATE)`;\n\n    const binding = this.path.scope.getBinding(this.name)!;\n    for (const ref of binding.referencePaths) {\n      if (conditionalCall.match(ref.parent)) {\n        // decode(test ? 1 : 2) -> test ? decode(1) : decode(2)\n        const [replacement] = ref.parentPath!.replaceWith(\n          buildExtractedConditional({\n            TEST: conditional.current!.test,\n            CALLEE: ref.parent.callee,\n            CONSEQUENT: conditional.current!.consequent,\n            ALTERNATE: conditional.current!.alternate,\n          }),\n        );\n        // some of the scope information is somehow lost after replacing\n        replacement.scope.crawl();\n      } else if (literalCall.match(ref.parent)) {\n        calls.push(ref.parentPath as NodePath<t.CallExpression>);\n      } else if (expressionCall.match(ref.parent)) {\n        // var n = 1; decode(n); -> decode(1);\n        ref.parentPath!.traverse({\n          ReferencedIdentifier(path) {\n            const varBinding = path.scope.getBinding(path.node.name)!;\n            if (!varBinding) return;\n            inlineVariable(varBinding, literalArgument, true);\n          },\n        });\n        if (literalCall.match(ref.parent)) {\n          calls.push(ref.parentPath as NodePath<t.CallExpression>);\n        }\n      } else if (ref.parentPath?.isExpressionStatement()) {\n        // `decode;` may appear on it's own in some forked obfuscators\n        ref.parentPath.remove();\n      }\n    }\n\n    return calls;\n  }\n}\n\nexport function findDecoders(stringArray: StringArray): Decoder[] {\n  const decoders: Decoder[] = [];\n\n  const functionName = m.capture(m.anyString());\n  const arrayIdentifier = m.capture(m.identifier());\n  const matcher = m.functionDeclaration(\n    m.identifier(functionName),\n    m.anything(),\n    m.blockStatement(\n      m.anyList(\n        // var array = getStringArray();\n        m.variableDeclaration(undefined, [\n          m.variableDeclarator(\n            arrayIdentifier,\n            m.callExpression(m.identifier(stringArray.name)),\n          ),\n        ]),\n        m.zeroOrMore(),\n        // var h = array[e]; return h;\n        // or return array[e -= 254];\n        m.containerOf(\n          m.memberExpression(m.fromCapture(arrayIdentifier), undefined, true),\n        ),\n        m.zeroOrMore(),\n      ),\n    ),\n  );\n\n  for (const ref of stringArray.references) {\n    const decoderFn = findParent(ref, matcher);\n\n    if (decoderFn) {\n      const oldName = functionName.current!;\n      const newName = `__DECODE_${decoders.length}__`;\n      const binding = decoderFn.scope.getBinding(oldName)!;\n      renameFast(binding, newName);\n      decoders.push(new Decoder(oldName, newName, decoderFn));\n    }\n  }\n\n  return decoders;\n}\n", "import * as t from '@babel/types';\nimport type { AsyncTransform } from '../ast-utils';\nimport type { VMDecoder } from './vm';\n\n/**\n * Replaces calls to decoder functions with the decoded string.\n * E.g. `m(199)` -> `'log'`\n */\nexport default {\n  name: 'inline-decoded-strings',\n  tags: ['unsafe'],\n  scope: true,\n  async run(ast, state, options) {\n    if (!options) return;\n\n    const calls = options.vm.decoders.flatMap((decoder) =>\n      decoder.collectCalls(),\n    );\n    const decodedValues = await options.vm.decode(calls);\n\n    for (let i = 0; i < calls.length; i++) {\n      const call = calls[i];\n      const value = decodedValues[i];\n\n      call.replaceWith(t.valueToNode(value));\n      if (typeof value !== 'string')\n        call.addComment('leading', 'webcrack:decode_error');\n    }\n\n    state.changes += calls.length;\n  },\n} satisfies AsyncTransform<{ vm: VMDecoder }>;\n", "import type { NodePath } from '@babel/traverse';\nimport type * as t from '@babel/types';\nimport type { Transform } from '../ast-utils';\nimport { inlineFunctionAliases, inlineVariableAliases } from '../ast-utils';\n\n/**\n * Replaces all references to `var alias = decode;` with `decode`\n */\nexport default {\n  name: 'inline-decoder-wrappers',\n  tags: ['unsafe'],\n  scope: true,\n  run(ast, state, decoder) {\n    if (!decoder?.node.id) return;\n\n    const decoderName = decoder.node.id.name;\n    const decoderBinding = decoder.parentPath.scope.getBinding(decoderName);\n    if (decoderBinding) {\n      state.changes += inlineVariableAliases(decoderBinding).changes;\n      state.changes += inlineFunctionAliases(decoderBinding).changes;\n    }\n  },\n} satisfies Transform<NodePath<t.FunctionDeclaration>>;\n", "import * as m from '@codemod/matchers';\nimport type { Transform } from '../ast-utils';\nimport {\n  constKey,\n  constMemberExpression,\n  getPropName,\n  inlineObjectProperties,\n  isReadonlyObject,\n} from '../ast-utils';\n\n// TODO: move do decoder.ts collectCalls to avoid traversing the whole AST\n\n/**\n * Inline objects that only have string or numeric literal properties.\n * Used by the \"String Array Calls Transform\" option for moving the\n * decode call arguments into an object.\n * Example:\n * ```js\n * const obj = {\n *   c: 0x2f2,\n *   d: '0x396',\n * };\n * console.log(decode(obj.c, obj.d));\n * ```\n * ->\n * ```js\n * console.log(decode(0x2f2, '0x396'));\n * ```\n */\nexport default {\n  name: 'inline-object-props',\n  tags: ['safe'],\n  scope: true,\n  visitor() {\n    const varId = m.capture(m.identifier());\n    const propertyName = m.capture(\n      m.matcher<string>((name) => /^[\\w]+$/i.test(name)),\n    );\n    const propertyKey = constKey(propertyName);\n    // E.g. \"_0x51b74a\": 0x80\n    const objectProperties = m.capture(\n      m.arrayOf(\n        m.objectProperty(\n          propertyKey,\n          m.or(m.stringLiteral(), m.numericLiteral()),\n        ),\n      ),\n    );\n    // E.g. obj._0x51b74a\n    const memberAccess = constMemberExpression(\n      m.fromCapture(varId),\n      propertyName,\n    );\n    const varMatcher = m.variableDeclarator(\n      varId,\n      m.objectExpression(objectProperties),\n    );\n    // E.g. { e: 0x80 }.e\n    const literalMemberAccess = constMemberExpression(\n      m.objectExpression(objectProperties),\n      propertyName,\n    );\n\n    return {\n      MemberExpression(path) {\n        if (!literalMemberAccess.match(path.node)) return;\n        const property = objectProperties.current!.find(\n          (p) => getPropName(p.key) === propertyName.current,\n        );\n        if (!property) return;\n        path.replaceWith(property.value);\n        this.changes++;\n      },\n      VariableDeclarator(path) {\n        if (!varMatcher.match(path.node)) return;\n        if (objectProperties.current!.length === 0) return;\n\n        const binding = path.scope.getBinding(varId.current!.name);\n        if (!binding || !isReadonlyObject(binding, memberAccess)) return;\n\n        inlineObjectProperties(\n          binding,\n          m.objectProperty(\n            propertyKey,\n            m.or(m.stringLiteral(), m.numericLiteral()),\n          ),\n        );\n        this.changes++;\n      },\n    };\n  },\n} satisfies Transform;\n", "import type { NodePath } from '@babel/traverse';\nimport traverse from '@babel/traverse';\nimport type * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport {\n  inlineArrayElements,\n  isReadonlyObject,\n  renameFast,\n  undefinedMatcher,\n} from '../ast-utils';\n\nexport interface StringArray {\n  path: NodePath<t.FunctionDeclaration>;\n  references: NodePath[];\n  name: string;\n  originalName: string;\n  length: number;\n}\n\nexport function findStringArray(ast: t.Node): StringArray | undefined {\n  let result: StringArray | undefined;\n  const functionName = m.capture(m.anyString());\n  const arrayIdentifier = m.capture(m.identifier());\n  const arrayExpression = m.capture(\n    m.arrayExpression(m.arrayOf(m.or(m.stringLiteral(), undefinedMatcher))),\n  );\n  // getStringArray = function () { return array; };\n  const functionAssignment = m.assignmentExpression(\n    '=',\n    m.identifier(m.fromCapture(functionName)),\n    m.functionExpression(\n      undefined,\n      [],\n      m.blockStatement([m.returnStatement(m.fromCapture(arrayIdentifier))]),\n    ),\n  );\n  const variableDeclaration = m.variableDeclaration(undefined, [\n    m.variableDeclarator(arrayIdentifier, arrayExpression),\n  ]);\n  // function getStringArray() { ... }\n  const matcher = m.functionDeclaration(\n    m.identifier(functionName),\n    [],\n    m.or(\n      // var array = [\"hello\", \"world\"];\n      // return (getStringArray = function () { return array; })();\n      m.blockStatement([\n        variableDeclaration,\n        m.returnStatement(m.callExpression(functionAssignment)),\n      ]),\n      // var array = [\"hello\", \"world\"];\n      // getStringArray = function () { return array; });\n      // return getStringArray();\n      m.blockStatement([\n        variableDeclaration,\n        m.expressionStatement(functionAssignment),\n        m.returnStatement(m.callExpression(m.identifier(functionName))),\n      ]),\n    ),\n  );\n\n  traverse(ast, {\n    // Wrapped string array from later javascript-obfuscator versions\n    FunctionDeclaration(path) {\n      if (matcher.match(path.node)) {\n        const length = arrayExpression.current!.elements.length;\n        const name = functionName.current!;\n        const binding = path.scope.getBinding(name)!;\n        renameFast(binding, '__STRING_ARRAY__');\n\n        result = {\n          path,\n          references: binding.referencePaths,\n          originalName: name,\n          name: '__STRING_ARRAY__',\n          length,\n        };\n        path.stop();\n      }\n    },\n    // Simple string array inlining (only `array[0]`, `array[1]` etc references, no rotating/decoding).\n    // May be used by older or different obfuscators\n    VariableDeclaration(path) {\n      if (!variableDeclaration.match(path.node)) return;\n\n      const length = arrayExpression.current!.elements.length;\n      const binding = path.scope.getBinding(arrayIdentifier.current!.name)!;\n      const memberAccess = m.memberExpression(\n        m.fromCapture(arrayIdentifier),\n        m.numericLiteral(m.matcher((value) => value < length)),\n      );\n      if (!binding.referenced || !isReadonlyObject(binding, memberAccess))\n        return;\n\n      inlineArrayElements(arrayExpression.current!, binding.referencePaths);\n      path.remove();\n    },\n  });\n\n  return result;\n}\n", "import type { NodePath } from '@babel/traverse';\nimport type { CallExpression } from '@babel/types';\nimport debug from 'debug';\nimport { generate } from '../ast-utils';\nimport type { ArrayRotator } from './array-rotator';\nimport type { Decoder } from './decoder';\nimport type { StringArray } from './string-array';\n\nexport type Sandbox = (code: string) => Promise<unknown>;\n\nexport function createNodeSandbox(): Sandbox {\n  return async (code: string) => {\n    const {\n      default: { Isolate },\n    } = await import('isolated-vm');\n    const isolate = new Isolate();\n    const context = await isolate.createContext();\n    const result = (await context.eval(code, {\n      timeout: 10_000,\n      copy: true,\n      filename: 'file:///obfuscated.js',\n    })) as unknown;\n    context.release();\n    isolate.dispose();\n    return result;\n  };\n}\n\nexport function createBrowserSandbox(): Sandbox {\n  return () => {\n    // TODO: use sandybox (not available in web workers though)\n    throw new Error('Custom Sandbox implementation required.');\n  };\n}\n\nexport class VMDecoder {\n  decoders: Decoder[];\n  private setupCode: string;\n  private sandbox: Sandbox;\n\n  constructor(\n    sandbox: Sandbox,\n    stringArray: StringArray,\n    decoders: Decoder[],\n    rotator?: ArrayRotator,\n  ) {\n    this.sandbox = sandbox;\n    this.decoders = decoders;\n\n    // Generate as compact to bypass the self defense\n    // (which tests someFunction.toString against a regex)\n    const generateOptions = {\n      compact: true,\n      shouldPrintComment: () => false,\n    };\n    const stringArrayCode = generate(stringArray.path.node, generateOptions);\n    const rotatorCode = rotator ? generate(rotator.node, generateOptions) : '';\n    const decoderCode = decoders\n      .map((decoder) => generate(decoder.path.node, generateOptions))\n      .join(';\\n');\n\n    this.setupCode = [stringArrayCode, rotatorCode, decoderCode].join(';\\n');\n  }\n\n  async decode(calls: NodePath<CallExpression>[]): Promise<unknown[]> {\n    const code = `(() => {\n      ${this.setupCode}\n      return [${calls.join(',')}]\n    })()`;\n\n    try {\n      const result = await this.sandbox(code);\n      return result as unknown[];\n    } catch (error) {\n      debug('webcrack:deobfuscate')('vm code:', code);\n      if (\n        error instanceof Error &&\n        (error.message.includes('undefined symbol') ||\n          error.message.includes('Segmentation fault'))\n      ) {\n        throw new Error(\n          'isolated-vm version mismatch. Check https://webcrack.netlify.app/docs/guide/common-errors.html#isolated-vm',\n          { cause: error },\n        );\n      }\n      throw error;\n    }\n  }\n}\n", "import * as m from '@codemod/matchers';\nimport { ifStatement } from '@codemod/matchers';\nimport type { Transform } from '../ast-utils';\nimport { constMemberExpression, findParent, iife } from '../ast-utils';\n\n// https://github.com/javascript-obfuscator/javascript-obfuscator/blob/d7f73935557b2cd15a2f7cd0b01017d9cddbd015/src/custom-code-helpers/debug-protection/templates/debug-protection-function-interval/DebugProtectionFunctionIntervalTemplate.ts\n\n// https://github.com/javascript-obfuscator/javascript-obfuscator/blob/d7f73935557b2cd15a2f7cd0b01017d9cddbd015/src/custom-code-helpers/debug-protection/templates/debug-protection-function/DebugProtectionFunctionTemplate.ts\n\n// https://github.com/javascript-obfuscator/javascript-obfuscator/blob/d7f73935557b2cd15a2f7cd0b01017d9cddbd015/src/custom-code-helpers/debug-protection/templates/debug-protection-function/DebuggerTemplate.ts\n\n// https://github.com/javascript-obfuscator/javascript-obfuscator/blob/d7f73935557b2cd15a2f7cd0b01017d9cddbd015/src/custom-code-helpers/debug-protection/templates/debug-protection-function/DebuggerTemplateNoEval.ts\n\nexport default {\n  name: 'debug-protection',\n  tags: ['safe'],\n  scope: true,\n  visitor() {\n    const ret = m.capture(m.identifier());\n    const debugProtectionFunctionName = m.capture(m.anyString());\n    const debuggerProtection = m.capture(m.identifier());\n    const counter = m.capture(m.identifier());\n    const debuggerTemplate = m.ifStatement(\n      undefined,\n      undefined,\n      m.containerOf(\n        m.or(\n          m.debuggerStatement(),\n          m.callExpression(\n            constMemberExpression(m.anyExpression(), 'constructor'),\n            [m.stringLiteral('debugger')],\n          ),\n        ),\n      ),\n    );\n    // that.setInterval(debugProtectionFunctionName, 4000);\n    const intervalCall = m.callExpression(\n      constMemberExpression(m.anyExpression(), 'setInterval'),\n      [\n        m.identifier(m.fromCapture(debugProtectionFunctionName)),\n        m.numericLiteral(),\n      ],\n    );\n\n    // function debugProtectionFunctionName(ret) {\n    const matcher = m.functionDeclaration(\n      m.identifier(debugProtectionFunctionName),\n      [ret],\n      m.blockStatement([\n        // function debuggerProtection (counter) {\n        m.functionDeclaration(\n          debuggerProtection,\n          [counter],\n          m.blockStatement([\n            debuggerTemplate,\n            // debuggerProtection(++counter);\n            m.expressionStatement(\n              m.callExpression(m.fromCapture(debuggerProtection), [\n                m.updateExpression('++', m.fromCapture(counter), true),\n              ]),\n            ),\n          ]),\n        ),\n        m.tryStatement(\n          m.blockStatement([\n            // if (ret) {\n            ifStatement(\n              m.fromCapture(ret),\n              // return debuggerProtection;\n              m.blockStatement([\n                m.returnStatement(m.fromCapture(debuggerProtection)),\n              ]),\n              // } else { debuggerProtection(0); }\n              m.blockStatement([\n                m.expressionStatement(\n                  m.callExpression(m.fromCapture(debuggerProtection), [\n                    m.numericLiteral(0),\n                  ]),\n                ),\n              ]),\n            ),\n          ]),\n        ),\n      ]),\n    );\n\n    return {\n      FunctionDeclaration(path) {\n        if (!matcher.match(path.node)) return;\n\n        const binding = path.scope.getBinding(\n          debugProtectionFunctionName.current!,\n        );\n\n        binding?.referencePaths.forEach((ref) => {\n          if (intervalCall.match(ref.parent)) {\n            findParent(ref, iife())?.remove();\n          }\n        });\n\n        path.remove();\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../ast-utils';\n\nconst FUNCTIONS = {\n  atob,\n  unescape,\n  decodeURI,\n  decodeURIComponent,\n};\n\nexport default {\n  name: 'evaluate-globals',\n  tags: ['safe'],\n  scope: true,\n  visitor() {\n    const name = m.capture(\n      m.or(...(Object.keys(FUNCTIONS) as (keyof typeof FUNCTIONS)[])),\n    );\n    const arg = m.capture(m.anyString());\n    const matcher = m.callExpression(m.identifier(name), [\n      m.stringLiteral(arg),\n    ]);\n\n    return {\n      CallExpression: {\n        exit(path) {\n          if (!matcher.match(path.node)) return;\n          if (path.scope.hasBinding(name.current!, { noGlobals: true })) return;\n\n          try {\n            // Causes a \"TypeError: Illegal invocation\" without the globalThis receiver\n            const value = FUNCTIONS[name.current!].call(\n              globalThis,\n              arg.current!,\n            );\n            path.replaceWith(t.stringLiteral(value));\n            this.changes++;\n          } catch {\n            // ignore\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import type { Binding } from '@babel/traverse';\nimport * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../ast-utils';\nimport { constObjectProperty, safeLiteral } from '../ast-utils';\n\n/**\n * Merges object assignments into the object expression.\n * Example:\n * ```js\n * const obj = {};\n * obj.foo = 'bar';\n * ```\n * ->\n * ```js\n * const obj = { foo: 'bar' };\n * ```\n */\nexport default {\n  name: 'merge-object-assignments',\n  tags: ['safe'],\n  scope: true,\n  visitor: () => {\n    const id = m.capture(m.identifier());\n    const object = m.capture(m.objectExpression([]));\n    // Example: const obj = {};\n    const varMatcher = m.variableDeclaration(undefined, [\n      m.variableDeclarator(id, object),\n    ]);\n    const key = m.capture(m.anyExpression());\n    const computed = m.capture<boolean>(m.anything());\n    const value = m.capture(m.anyExpression());\n    // Example: obj.foo = 'bar';\n    const assignmentMatcher = m.expressionStatement(\n      m.assignmentExpression(\n        '=',\n        m.memberExpression(m.fromCapture(id), key, computed),\n        value,\n      ),\n    );\n\n    return {\n      Program(path) {\n        // No idea why this is needed, crashes otherwise.\n        path.scope.crawl();\n      },\n      VariableDeclaration: {\n        exit(path) {\n          if (!path.inList || !varMatcher.match(path.node)) return;\n\n          const binding = path.scope.getBinding(id.current!.name)!;\n          const container = path.container as t.Statement[];\n          const siblingIndex = (path.key as number) + 1;\n\n          while (siblingIndex < container.length) {\n            const sibling = path.getSibling(siblingIndex);\n            if (\n              !assignmentMatcher.match(sibling.node) ||\n              hasCircularReference(value.current!, binding)\n            )\n              return;\n\n            // { [1]: value, \"foo bar\": value } can be simplified to { 1: value, \"foo bar\": value }\n            const isComputed =\n              computed.current! &&\n              key.current!.type !== 'NumericLiteral' &&\n              key.current!.type !== 'StringLiteral';\n\n            // Example: const obj = { x: 1 }; obj.foo = 'bar'; -> const obj = { x: 1, foo: 'bar' };\n            object.current!.properties.push(\n              t.objectProperty(key.current!, value.current!, isComputed),\n            );\n\n            sibling.remove();\n            binding.dereference();\n            binding.referencePaths.shift();\n\n            // Example: const obj = { foo: 'bar' }; return obj; -> return { foo: 'bar' };\n            if (\n              binding.references === 1 &&\n              inlineableObject.match(object.current)\n            ) {\n              binding.referencePaths[0].replaceWith(object.current);\n              path.remove();\n              this.changes++;\n            }\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n\n/**\n * Used to avoid \"Cannot access 'obj' before initialization\" errors.\n */\nfunction hasCircularReference(node: t.Node, binding: Binding) {\n  return (\n    // obj.foo = obj;\n    binding.referencePaths.some((path) => path.find((p) => p.node === node)) ||\n    // obj.foo = fn(); where fn could reference the binding or not, for simplicity we assume it does.\n    m.containerOf(m.callExpression()).match(node)\n  );\n}\n\n/**\n * Only literals, arrays and objects are allowed because variable values\n * might be different in the place the object will be inlined.\n */\nconst inlineableObject: m.Matcher<t.Expression> = m.matcher((node) =>\n  m\n    .or(\n      safeLiteral,\n      m.arrayExpression(m.arrayOf(inlineableObject)),\n      m.objectExpression(m.arrayOf(constObjectProperty(inlineableObject))),\n    )\n    .match(node),\n);\n", "import type { NodePath } from '@babel/traverse';\nimport type * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../ast-utils';\nimport {\n  constMemberExpression,\n  falseMatcher,\n  findParent,\n  iife,\n  trueMatcher,\n} from '../ast-utils';\n\n// SingleCallController: https://github.com/javascript-obfuscator/javascript-obfuscator/blob/d7f73935557b2cd15a2f7cd0b01017d9cddbd015/src/custom-code-helpers/common/templates/SingleCallControllerTemplate.ts\n\n// Works for\n// self defending: https://github.com/javascript-obfuscator/javascript-obfuscator/blob/d7f73935557b2cd15a2f7cd0b01017d9cddbd015/src/custom-code-helpers/self-defending/templates/SelfDefendingTemplate.ts\n// domain lock: https://github.com/javascript-obfuscator/javascript-obfuscator/blob/d7f73935557b2cd15a2f7cd0b01017d9cddbd015/src/custom-code-helpers/domain-lock/templates/DomainLockTemplate.ts\n// console output: https://github.com/javascript-obfuscator/javascript-obfuscator/blob/d7f73935557b2cd15a2f7cd0b01017d9cddbd015/src/custom-code-helpers/console-output/templates/ConsoleOutputDisableTemplate.ts\n// debug protection function call: https://github.com/javascript-obfuscator/javascript-obfuscator/blob/d7f73935557b2cd15a2f7cd0b01017d9cddbd015/src/custom-code-helpers/debug-protection/templates/debug-protection-function-call/DebugProtectionFunctionCallTemplate.ts\n\nexport default {\n  name: 'self-defending',\n  tags: ['safe'],\n  scope: true,\n  visitor() {\n    const callController = m.capture(m.anyString());\n    const firstCall = m.capture(m.identifier());\n    const rfn = m.capture(m.identifier());\n    const context = m.capture(m.identifier());\n    const res = m.capture(m.identifier());\n    const fn = m.capture(m.identifier());\n\n    // const callControllerFunctionName = (function() { ... })();\n    const matcher = m.variableDeclarator(\n      m.identifier(callController),\n      iife(\n        [],\n        m.blockStatement([\n          // let firstCall = true;\n          m.variableDeclaration(undefined, [\n            m.variableDeclarator(firstCall, trueMatcher),\n          ]),\n          // return function (context, fn) {\n          m.returnStatement(\n            m.functionExpression(\n              null,\n              [context, fn],\n              m.blockStatement([\n                m.variableDeclaration(undefined, [\n                  // const rfn = firstCall ? function() {\n                  m.variableDeclarator(\n                    rfn,\n                    m.conditionalExpression(\n                      m.fromCapture(firstCall),\n                      m.functionExpression(\n                        null,\n                        [],\n                        m.blockStatement([\n                          // if (fn) {\n                          m.ifStatement(\n                            m.fromCapture(fn),\n                            m.blockStatement([\n                              // const res = fn.apply(context, arguments);\n                              m.variableDeclaration(undefined, [\n                                m.variableDeclarator(\n                                  res,\n                                  m.callExpression(\n                                    constMemberExpression(\n                                      m.fromCapture(fn),\n                                      'apply',\n                                    ),\n                                    [\n                                      m.fromCapture(context),\n                                      m.identifier('arguments'),\n                                    ],\n                                  ),\n                                ),\n                              ]),\n                              // fn = null;\n                              m.expressionStatement(\n                                m.assignmentExpression(\n                                  '=',\n                                  m.fromCapture(fn),\n                                  m.nullLiteral(),\n                                ),\n                              ),\n                              // return res;\n                              m.returnStatement(m.fromCapture(res)),\n                            ]),\n                          ),\n                        ]),\n                      ),\n                      // : function() {}\n                      m.functionExpression(null, [], m.blockStatement([])),\n                    ),\n                  ),\n                ]),\n                // firstCall = false;\n                m.expressionStatement(\n                  m.assignmentExpression(\n                    '=',\n                    m.fromCapture(firstCall),\n                    falseMatcher,\n                  ),\n                ),\n                // return rfn;\n                m.returnStatement(m.fromCapture(rfn)),\n              ]),\n            ),\n          ),\n        ]),\n      ),\n    );\n\n    const emptyIife = iife([], m.blockStatement([]));\n\n    return {\n      VariableDeclarator(path) {\n        if (!matcher.match(path.node)) return;\n        const binding = path.scope.getBinding(callController.current!);\n        if (!binding) return;\n        // const callControllerFunctionName = (function() { ... })();\n        //       ^ path/binding\n\n        binding.referencePaths\n          .filter((ref) => ref.parent.type === 'CallExpression')\n          .forEach((ref) => {\n            if (ref.parentPath?.parent.type === 'CallExpression') {\n              // callControllerFunctionName(this, function () { ... })();\n              // ^ ref\n              ref.parentPath.parentPath?.remove();\n            } else {\n              // const selfDefendingFunctionName = callControllerFunctionName(this, function () {\n              // selfDefendingFunctionName();      ^ ref\n              removeSelfDefendingRefs(ref as NodePath<t.Identifier>);\n            }\n\n            // leftover (function () {})() from debug protection function call\n            findParent(ref, emptyIife)?.remove();\n\n            this.changes++;\n          });\n\n        path.remove();\n        this.changes++;\n      },\n    };\n  },\n} satisfies Transform;\n\nfunction removeSelfDefendingRefs(path: NodePath<t.Identifier>) {\n  const varName = m.capture(m.anyString());\n  const varMatcher = m.variableDeclarator(\n    m.identifier(varName),\n    m.callExpression(m.identifier(path.node.name)),\n  );\n  const callMatcher = m.expressionStatement(\n    m.callExpression(m.identifier(m.fromCapture(varName)), []),\n  );\n  const varDecl = findParent(path, varMatcher);\n\n  if (varDecl) {\n    const binding = varDecl.scope.getBinding(varName.current!);\n\n    binding?.referencePaths.forEach((ref) => {\n      if (callMatcher.match(ref.parentPath?.parent))\n        ref.parentPath?.parentPath?.remove();\n    });\n    varDecl.remove();\n  }\n}\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../ast-utils';\n\n// Unsafe because variables may be used before they are declared, but functions are hoisted\n// Example: `console.log(a); var a = function() {}` logs `undefined`\n// `console.log(a); function a() {}` logs the function\nexport default {\n  name: 'var-functions',\n  tags: ['unsafe'],\n  visitor() {\n    const name = m.capture(m.identifier());\n    const fn = m.capture(m.functionExpression(null));\n    const matcher = m.variableDeclaration('var', [\n      m.variableDeclarator(name, fn),\n    ]);\n\n    return {\n      VariableDeclaration: {\n        exit(path) {\n          if (matcher.match(path.node) && path.key !== 'init') {\n            path.replaceWith(\n              t.functionDeclaration(\n                name.current,\n                fn.current!.params,\n                fn.current!.body,\n                fn.current!.generator,\n                fn.current!.async,\n              ),\n            );\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../ast-utils';\nimport { codePreview, constMemberExpression } from '../ast-utils';\nimport { generateUid } from '../ast-utils/scope';\n\nexport default {\n  name: 'jsx',\n  tags: ['unsafe'],\n  scope: true,\n  visitor: () => {\n    const deepIdentifierMemberExpression = m.memberExpression(\n      m.or(\n        m.identifier(),\n        m.matcher((node) => deepIdentifierMemberExpression.match(node)),\n      ),\n      m.identifier(),\n      false,\n    );\n\n    const type = m.capture(\n      m.or(\n        m.identifier(), // React.createElement(Component, ...)\n        m.stringLiteral(), // React.createElement('div', ...)\n        deepIdentifierMemberExpression, // React.createElement(Component.SubComponent, ...)\n      ),\n    );\n    const props = m.capture(m.or(m.objectExpression(), m.nullLiteral()));\n\n    // React.createElement(type, props, ...children)\n    const elementMatcher = m.callExpression(\n      constMemberExpression('React', 'createElement'),\n      m.anyList(\n        type,\n        props,\n        m.zeroOrMore(m.or(m.anyExpression(), m.spreadElement())),\n      ),\n    );\n\n    // React.createElement(React.Fragment, null, ...children)\n    const fragmentMatcher = m.callExpression(\n      constMemberExpression('React', 'createElement'),\n      m.anyList(\n        constMemberExpression('React', 'Fragment'),\n        m.nullLiteral(),\n        m.zeroOrMore(m.or(m.anyExpression(), m.spreadElement())),\n      ),\n    );\n\n    return {\n      CallExpression: {\n        exit(path) {\n          if (fragmentMatcher.match(path.node)) {\n            const children = convertChildren(\n              path.node.arguments.slice(2) as t.Expression[],\n            );\n            const opening = t.jsxOpeningFragment();\n            const closing = t.jsxClosingFragment();\n            const fragment = t.jsxFragment(opening, closing, children);\n            path.node.leadingComments = null;\n            path.replaceWith(fragment);\n            this.changes++;\n          }\n\n          if (elementMatcher.match(path.node)) {\n            let name = convertType(type.current!);\n\n            // rename component to avoid conflict with built-in html tags\n            // https://react.dev/reference/react/createElement#caveats\n            if (\n              t.isIdentifier(type.current) &&\n              /^[a-z]/.test(type.current.name)\n            ) {\n              const binding = path.scope.getBinding(type.current.name);\n              if (!binding) return;\n              name = t.jsxIdentifier(generateUid(path.scope, 'Component'));\n              path.scope.rename(type.current.name, name.name);\n            }\n\n            const attributes = t.isObjectExpression(props.current)\n              ? convertAttributes(props.current)\n              : [];\n            const children = convertChildren(\n              path.node.arguments.slice(2) as t.Expression[],\n            );\n            const selfClosing = children.length === 0;\n            const opening = t.jsxOpeningElement(name, attributes, selfClosing);\n            const closing = t.jsxClosingElement(name);\n            const element = t.jsxElement(opening, closing, children);\n            path.node.leadingComments = null;\n            path.replaceWith(element);\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n\n/**\n * - `Component` -> `Component`\n * - `Component.SubComponent` -> `Component.SubComponent`\n * - `'div'` -> `div`\n */\nfunction convertType(\n  type: t.Identifier | t.MemberExpression | t.StringLiteral,\n): t.JSXIdentifier | t.JSXMemberExpression {\n  if (t.isIdentifier(type)) {\n    return t.jsxIdentifier(type.name);\n  } else if (t.isStringLiteral(type)) {\n    return t.jsxIdentifier(type.value);\n  } else {\n    const object = convertType(\n      type.object as t.Identifier | t.MemberExpression,\n    );\n    const property = t.jsxIdentifier((type.property as t.Identifier).name);\n    return t.jsxMemberExpression(object, property);\n  }\n}\n\n/**\n * `{ className: 'foo', style: { display: 'block' } }`\n * ->\n * `className='foo' style={{ display: 'block' }}`\n */\nfunction convertAttributes(\n  object: t.ObjectExpression,\n): (t.JSXAttribute | t.JSXSpreadAttribute)[] {\n  const name = m.capture(m.anyString());\n  const value = m.capture(m.anyExpression());\n  const matcher = m.objectProperty(\n    m.or(m.identifier(name), m.stringLiteral(name)),\n    value,\n  );\n\n  return object.properties.map((property) => {\n    if (matcher.match(property)) {\n      const jsxName = t.jsxIdentifier(name.current!);\n      if (value.current!.type === 'StringLiteral') {\n        const hasSpecialChars = /[\"\\\\]/.test(value.current.value);\n        const jsxValue = hasSpecialChars\n          ? t.jsxExpressionContainer(value.current)\n          : value.current;\n        return t.jsxAttribute(jsxName, jsxValue);\n      }\n      const jsxValue = t.jsxExpressionContainer(value.current!);\n      return t.jsxAttribute(jsxName, jsxValue);\n    } else if (t.isSpreadElement(property)) {\n      return t.jsxSpreadAttribute(property.argument);\n    } else {\n      throw new Error(\n        `jsx: property type not implemented ${codePreview(object)}`,\n      );\n    }\n  });\n}\n\nfunction convertChildren(\n  children: (t.Expression | t.SpreadElement)[],\n): (t.JSXText | t.JSXElement | t.JSXSpreadChild | t.JSXExpressionContainer)[] {\n  return children.map((child) => {\n    if (t.isJSXElement(child)) {\n      return child;\n    } else if (t.isStringLiteral(child)) {\n      const hasSpecialChars = /[{}<>\\r\\n]/.test(child.value);\n      return hasSpecialChars\n        ? t.jsxExpressionContainer(child)\n        : t.jsxText(child.value);\n    } else if (t.isSpreadElement(child)) {\n      return t.jsxSpreadChild(child.argument);\n    } else {\n      return t.jsxExpressionContainer(child);\n    }\n  });\n}\n", "import type { Scope } from '@babel/traverse';\nimport { toIdentifier } from '@babel/types';\n\n/**\n * Like scope.generateUid from babel, but without the underscore prefix and name filters\n */\nexport function generateUid(scope: Scope, name: string = 'temp'): string {\n  let uid = '';\n  let i = 1;\n  do {\n    uid = toIdentifier(i > 1 ? `${name}${i}` : name);\n    i++;\n  } while (\n    scope.hasLabel(uid) ||\n    scope.hasBinding(uid) ||\n    scope.hasGlobal(uid) ||\n    scope.hasReference(uid)\n  );\n\n  const program = scope.getProgramParent();\n  program.references[uid] = true;\n  program.uids[uid] = true;\n  return uid;\n}\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../ast-utils';\nimport { codePreview, constMemberExpression } from '../ast-utils';\nimport { generateUid } from '../ast-utils/scope';\n\nconst DEFAULT_PRAGMA_CANDIDATES = [\n  'jsx',\n  'jsxs',\n  '_jsx',\n  '_jsxs',\n  'jsxDEV',\n  'jsxsDEV',\n] as const;\n\n/**\n * https://github.com/reactjs/rfcs/blob/createlement-rfc/text/0000-create-element-changes.md\n * https://new-jsx-transform.netlify.app/\n */\nexport default {\n  name: 'jsx-new',\n  tags: ['unsafe'],\n  scope: true,\n  visitor: () => {\n    const deepIdentifierMemberExpression = m.memberExpression(\n      m.or(\n        m.identifier(),\n        m.matcher((node) => deepIdentifierMemberExpression.match(node)),\n      ),\n      m.identifier(),\n      false,\n    );\n    const convertibleName = m.or(\n      m.identifier(), // jsx(Component, ...)\n      m.stringLiteral(), // jsx('div', ...)\n      deepIdentifierMemberExpression, // jsx(Component.SubComponent, ...)\n    );\n    const type = m.capture(m.anyExpression());\n    const fragmentType = constMemberExpression('React', 'Fragment');\n    const props = m.capture(m.objectExpression());\n    const key = m.capture(m.anyExpression());\n\n    const jsxFunction = m.capture(m.or(...DEFAULT_PRAGMA_CANDIDATES));\n    // jsx(type, props, key?)\n    const jsxMatcher = m.callExpression(\n      m.identifier(jsxFunction),\n      m.anyList(type, props, m.slice({ min: 0, max: 1, matcher: key })),\n    );\n\n    return {\n      CallExpression: {\n        exit(path) {\n          if (!jsxMatcher.match(path.node)) return;\n\n          let name: t.Node;\n          if (convertibleName.match(type.current!)) {\n            name = convertType(type.current);\n          } else {\n            name = t.jsxIdentifier(generateUid(path.scope, 'Component'));\n            const componentVar = t.variableDeclaration('const', [\n              t.variableDeclarator(t.identifier(name.name), type.current),\n            ]);\n            path.getStatementParent()?.insertBefore(componentVar);\n          }\n          const isFragment = fragmentType.match(type.current);\n\n          // rename component to avoid conflict with built-in html tags\n          // https://react.dev/reference/react/createElement#caveats\n          if (\n            t.isIdentifier(type.current) &&\n            /^[a-z]/.test(type.current.name)\n          ) {\n            const binding = path.scope.getBinding(type.current.name);\n            if (!binding) return;\n            name = t.jsxIdentifier(path.scope.generateUid('Component'));\n            path.scope.rename(type.current.name, name.name);\n          }\n\n          const attributes = convertAttributes(props.current!);\n          if (path.node.arguments.length === 3) {\n            attributes.push(\n              t.jsxAttribute(\n                t.jsxIdentifier('key'),\n                convertAttributeValue(key.current!),\n              ),\n            );\n          }\n          const children = convertChildren(\n            props.current!,\n            jsxFunction.current!,\n          );\n\n          if (isFragment && attributes.length === 0) {\n            const opening = t.jsxOpeningFragment();\n            const closing = t.jsxClosingFragment();\n            const fragment = t.jsxFragment(opening, closing, children);\n            path.node.leadingComments = null;\n            path.replaceWith(fragment);\n          } else {\n            const selfClosing = children.length === 0;\n            const opening = t.jsxOpeningElement(name, attributes, selfClosing);\n            const closing = t.jsxClosingElement(name);\n            const element = t.jsxElement(opening, closing, children);\n            path.node.leadingComments = null;\n            path.replaceWith(element);\n          }\n          this.changes++;\n        },\n      },\n    };\n  },\n} satisfies Transform;\n\n/**\n * - `Component` -> `Component`\n * - `Component.SubComponent` -> `Component.SubComponent`\n * - `'div'` -> `div`\n */\nfunction convertType(\n  type: t.Identifier | t.MemberExpression | t.StringLiteral,\n): t.JSXIdentifier | t.JSXMemberExpression {\n  if (t.isIdentifier(type)) {\n    return t.jsxIdentifier(type.name);\n  } else if (t.isStringLiteral(type)) {\n    return t.jsxIdentifier(type.value);\n  } else {\n    const object = convertType(\n      type.object as t.Identifier | t.MemberExpression,\n    );\n    const property = t.jsxIdentifier((type.property as t.Identifier).name);\n    return t.jsxMemberExpression(object, property);\n  }\n}\n\n/**\n * `{ className: 'foo', style: { display: 'block' } }`\n * ->\n * `className='foo' style={{ display: 'block' }}`\n */\nfunction convertAttributes(\n  object: t.ObjectExpression,\n): (t.JSXAttribute | t.JSXSpreadAttribute)[] {\n  const name = m.capture(m.anyString());\n  const value = m.capture(m.anyExpression());\n  const matcher = m.objectProperty(\n    m.or(m.identifier(name), m.stringLiteral(name)),\n    value,\n  );\n\n  return object.properties.flatMap((property) => {\n    if (matcher.match(property)) {\n      if (name.current === 'children') return [];\n\n      const jsxName = t.jsxIdentifier(name.current!);\n      const jsxValue = convertAttributeValue(value.current!);\n      return t.jsxAttribute(jsxName, jsxValue);\n    } else if (t.isSpreadElement(property)) {\n      return t.jsxSpreadAttribute(property.argument);\n    } else {\n      throw new Error(\n        `jsx: property type not implemented ${codePreview(object)}`,\n      );\n    }\n  });\n}\n\nfunction convertAttributeValue(\n  expression: t.Expression,\n): t.JSXExpressionContainer | t.StringLiteral {\n  if (expression.type === 'StringLiteral') {\n    const hasSpecialChars = /[\"\\\\]/.test(expression.value);\n    return hasSpecialChars ? t.jsxExpressionContainer(expression) : expression;\n  }\n  return t.jsxExpressionContainer(expression);\n}\n\nfunction convertChildren(\n  object: t.ObjectExpression,\n  pragma: string,\n): (t.JSXText | t.JSXElement | t.JSXExpressionContainer)[] {\n  const children = m.capture(m.anyExpression());\n  const matcher = m.objectProperty(\n    m.or(m.identifier('children'), m.stringLiteral('children')),\n    children,\n  );\n\n  const prop = object.properties.find((prop) => matcher.match(prop));\n  if (!prop) return [];\n\n  if (pragma.includes('jsxs') && t.isArrayExpression(children.current)) {\n    return children.current.elements.map((child) =>\n      convertChild(child as t.Expression),\n    );\n  }\n  return [convertChild(children.current!)];\n}\n\nfunction convertChild(\n  child: t.Expression,\n): t.JSXElement | t.JSXExpressionContainer | t.JSXText {\n  if (t.isJSXElement(child)) {\n    return child;\n  } else if (t.isStringLiteral(child)) {\n    const hasSpecialChars = /[{}<>\\r\\n]/.test(child.value);\n    return hasSpecialChars\n      ? t.jsxExpressionContainer(child)\n      : t.jsxText(child.value);\n  } else {\n    return t.jsxExpressionContainer(child);\n  }\n}\n", "import type { NodePath } from '@babel/traverse';\nimport type * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport { renameFast, type Transform } from '../ast-utils';\nimport { generateUid } from '../ast-utils/scope';\n\nexport default {\n  name: 'mangle',\n  tags: ['safe'],\n  scope: true,\n  visitor(match = () => true) {\n    return {\n      BindingIdentifier: {\n        exit(path) {\n          if (!path.isBindingIdentifier()) return;\n          if (path.parentPath.isImportSpecifier()) return;\n          if (path.parentPath.isObjectProperty()) return;\n          if (!match(path.node.name)) return;\n\n          const binding = path.scope.getBinding(path.node.name);\n          if (!binding) return;\n          if (\n            binding.referencePaths.some((ref) => ref.isExportNamedDeclaration())\n          )\n            return;\n\n          renameFast(binding, inferName(path));\n        },\n      },\n    };\n  },\n} satisfies Transform<(id: string) => boolean>;\n\nconst requireMatcher = m.variableDeclarator(\n  m.identifier(),\n  m.callExpression(m.identifier('require'), [m.stringLiteral()]),\n);\n\nfunction inferName(path: NodePath<t.Identifier>): string {\n  if (path.parentPath.isClass({ id: path.node })) {\n    return generateUid(path.scope, 'C');\n  } else if (path.parentPath.isFunction({ id: path.node })) {\n    return generateUid(path.scope, 'f');\n  } else if (\n    path.listKey === 'params' ||\n    (path.parentPath.isAssignmentPattern({ left: path.node }) &&\n      path.parentPath.listKey === 'params')\n  ) {\n    return generateUid(path.scope, 'p');\n  } else if (requireMatcher.match(path.parent)) {\n    return generateUid(\n      path.scope,\n      (path.parentPath.get('init.arguments.0') as NodePath<t.StringLiteral>)\n        .node.value,\n    );\n  } else if (path.parentPath.isVariableDeclarator({ id: path.node })) {\n    const init = path.parentPath.get('init');\n    const suffix = (init.isExpression() && generateExpressionName(init)) || '';\n    return generateUid(path.scope, 'v' + titleCase(suffix));\n  } else if (path.parentPath.isArrayPattern()) {\n    return generateUid(path.scope, 'v');\n  } else {\n    return path.node.name;\n  }\n}\n\nfunction generateExpressionName(\n  expression: NodePath<t.Expression>,\n): string | undefined {\n  if (expression.isIdentifier()) {\n    return expression.node.name;\n  } else if (expression.isFunctionExpression()) {\n    return expression.node.id?.name ?? 'f';\n  } else if (expression.isArrowFunctionExpression()) {\n    return 'f';\n  } else if (expression.isClassExpression()) {\n    return expression.node.id?.name ?? 'C';\n  } else if (expression.isCallExpression()) {\n    return generateExpressionName(\n      expression.get('callee') as NodePath<t.Expression>,\n    );\n  } else if (expression.isThisExpression()) {\n    return 'this';\n  } else {\n    return undefined;\n  }\n}\n\nfunction titleCase(str: string) {\n  return str.length > 0 ? str[0].toUpperCase() + str.slice(1) : str;\n}\n", "export { default as defaultParameters } from './default-parameters';\nexport { default as logicalAssignments } from './logical-assignments';\nexport { default as nullishCoalescing } from './nullish-coalescing';\nexport { default as nullishCoalescingAssignment } from './nullish-coalescing-assignment';\nexport { default as optionalChaining } from './optional-chaining';\nexport { default as templateLiterals } from './template-literals';\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport { constMemberExpression, type Transform } from '../../ast-utils';\n\nexport default {\n  name: 'default-parameters',\n  tags: ['safe'],\n  scope: true,\n  visitor() {\n    const defaultExpression = m.capture(m.anyExpression());\n    const index = m.capture(m.numericLiteral());\n    const varName = m.capture(m.identifier());\n    const varId = m.capture(\n      m.or(m.identifier(), m.arrayPattern(), m.objectPattern()),\n    );\n\n    // Example: arguments.length > 0 && arguments[0] !== undefined\n    const argumentCheckAnd = m.logicalExpression(\n      '&&',\n      m.binaryExpression(\n        '>',\n        constMemberExpression('arguments', 'length'),\n        index,\n      ),\n      m.binaryExpression(\n        '!==',\n        m.memberExpression(\n          m.identifier('arguments'),\n          m.fromCapture(index),\n          true,\n        ),\n        m.identifier('undefined'),\n      ),\n    );\n    // Example: arguments.length > 0 && arguments[0] !== undefined\n    const argumentCheckOr = m.logicalExpression(\n      '||',\n      m.binaryExpression(\n        '<=',\n        constMemberExpression('arguments', 'length'),\n        index,\n      ),\n      m.binaryExpression(\n        '===',\n        m.memberExpression(\n          m.identifier('arguments'),\n          m.fromCapture(index),\n          true,\n        ),\n        m.identifier('undefined'),\n      ),\n    );\n    // Example: arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1;\n    const defaultParam = m.variableDeclaration(undefined, [\n      m.variableDeclarator(\n        varId,\n        m.conditionalExpression(\n          argumentCheckAnd,\n          m.memberExpression(\n            m.identifier('arguments'),\n            m.fromCapture(index),\n            true,\n          ),\n          defaultExpression,\n        ),\n      ),\n    ]);\n    // Example: arguments.length > 0 && arguments[0] !== undefined && arguments[0];\n    const defaultFalseParam = m.variableDeclaration(undefined, [\n      m.variableDeclarator(\n        varId,\n        m.logicalExpression(\n          '&&',\n          argumentCheckAnd,\n          m.memberExpression(\n            m.identifier('arguments'),\n            m.fromCapture(index),\n            true,\n          ),\n        ),\n      ),\n    ]);\n    // Example: arguments.length <= 0 || arguments[0] === undefined || arguments[0]\n    const defaultTrueParam = m.variableDeclaration(undefined, [\n      m.variableDeclarator(\n        varId,\n        m.logicalExpression(\n          '||',\n          argumentCheckOr,\n          m.memberExpression(\n            m.identifier('arguments'),\n            m.fromCapture(index),\n            true,\n          ),\n        ),\n      ),\n    ]);\n\n    // Example: if (x === undefined) { x = 1; }\n    const defaultParamLoose = m.ifStatement(\n      m.binaryExpression('===', varName, m.identifier('undefined')),\n      m.blockStatement([\n        m.expressionStatement(\n          m.assignmentExpression(\n            '=',\n            m.fromCapture(varName),\n            defaultExpression,\n          ),\n        ),\n      ]),\n    );\n    // Example: var y = arguments.length > 1 ? arguments[1] : undefined;\n    const normalParam = m.variableDeclaration(undefined, [\n      m.variableDeclarator(\n        varId,\n        m.conditionalExpression(\n          m.binaryExpression(\n            '>',\n            constMemberExpression('arguments', 'length'),\n            index,\n          ),\n          m.memberExpression(\n            m.identifier('arguments'),\n            m.fromCapture(index),\n            true,\n          ),\n          m.identifier('undefined'),\n        ),\n      ),\n    ]);\n\n    return {\n      VariableDeclaration: {\n        exit(path) {\n          const fn = path.parentPath.parent;\n          if (!t.isFunction(fn) || path.key !== 0) return;\n\n          const newParam = defaultParam.match(path.node)\n            ? t.assignmentPattern(varId.current!, defaultExpression.current!)\n            : defaultFalseParam.match(path.node)\n              ? t.assignmentPattern(varId.current!, t.booleanLiteral(false))\n              : defaultTrueParam.match(path.node)\n                ? t.assignmentPattern(varId.current!, t.booleanLiteral(true))\n                : normalParam.match(path.node)\n                  ? varId.current!\n                  : null;\n          if (!newParam) return;\n\n          for (let i = fn.params.length; i < index.current!.value; i++) {\n            fn.params[i] = t.identifier(path.scope.generateUid('param'));\n          }\n          fn.params[index.current!.value] = newParam;\n          path.remove();\n          this.changes++;\n        },\n      },\n      IfStatement: {\n        exit(path) {\n          const fn = path.parentPath.parent;\n          if (!t.isFunction(fn) || path.key !== 0) return;\n          if (!defaultParamLoose.match(path.node)) return;\n\n          const binding = path.scope.getOwnBinding(varName.current!.name);\n          if (!binding) return;\n          const isFunctionParam =\n            binding.path.listKey === 'params' && binding.path.parent === fn;\n          if (!isFunctionParam) return;\n\n          binding.path.replaceWith(\n            t.assignmentPattern(varName.current!, defaultExpression.current!),\n          );\n          path.remove();\n          this.changes++;\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\nimport { isTemporaryVariable } from '../../ast-utils';\n\nexport default {\n  name: 'logical-assignments',\n  tags: ['safe'],\n  scope: true,\n  visitor() {\n    const operator = m.capture(m.or('||' as const, '&&' as const));\n\n    const left = m.capture(m.or(m.identifier(), m.memberExpression()));\n    const right = m.capture(m.anyExpression());\n    // Example: left || (left = right)\n    const idMatcher = m.logicalExpression(\n      operator,\n      left,\n      m.assignmentExpression('=', m.fromCapture(left), right),\n    );\n\n    const object = m.capture(m.anyExpression());\n    const property = m.capture(m.anyExpression());\n    const tmpVar = m.capture(m.identifier());\n    const member = m.capture(\n      m.memberExpression(m.fromCapture(tmpVar), m.fromCapture(property)),\n    );\n    // Example: var _tmp; (_tmp = x.y()).property || (_tmp.property = right);\n    const memberMatcher = m.logicalExpression(\n      operator,\n      m.memberExpression(m.assignmentExpression('=', tmpVar, object), property),\n      m.assignmentExpression('=', member, right),\n    );\n\n    // Example: var _tmp; x[_tmp = y()] || (x[_tmp] = z);\n    const computedMemberMatcher = m.logicalExpression(\n      operator,\n      m.memberExpression(\n        object,\n        m.assignmentExpression('=', tmpVar, property),\n        true,\n      ),\n      m.assignmentExpression(\n        '=',\n        m.memberExpression(m.fromCapture(object), m.fromCapture(tmpVar), true),\n        right,\n      ),\n    );\n\n    const tmpVar2 = m.capture(m.identifier());\n    // Example:  var _tmp, _tmp2; (_tmp = x)[_tmp2 = y] || (_tmp[_tmp2] = z);\n    const multiComputedMemberMatcher = m.logicalExpression(\n      operator,\n      m.memberExpression(\n        m.assignmentExpression('=', tmpVar, object),\n        m.assignmentExpression('=', tmpVar2, property),\n        true,\n      ),\n      m.assignmentExpression(\n        '=',\n        m.memberExpression(m.fromCapture(tmpVar), m.fromCapture(tmpVar2), true),\n        right,\n      ),\n    );\n\n    return {\n      LogicalExpression: {\n        exit(path) {\n          if (idMatcher.match(path.node)) {\n            path.replaceWith(\n              t.assignmentExpression(\n                operator.current! + '=',\n                left.current!,\n                right.current!,\n              ),\n            );\n            this.changes++;\n          } else if (memberMatcher.match(path.node)) {\n            const binding = path.scope.getBinding(tmpVar.current!.name);\n            if (!isTemporaryVariable(binding, 1)) return;\n\n            binding.path.remove();\n            member.current!.object = object.current!;\n            path.replaceWith(\n              t.assignmentExpression(\n                operator.current! + '=',\n                member.current!,\n                right.current!,\n              ),\n            );\n            this.changes++;\n          } else if (computedMemberMatcher.match(path.node)) {\n            const binding = path.scope.getBinding(tmpVar.current!.name);\n            if (!isTemporaryVariable(binding, 1)) return;\n\n            binding.path.remove();\n            path.replaceWith(\n              t.assignmentExpression(\n                operator.current! + '=',\n                t.memberExpression(object.current!, property.current!, true),\n                right.current!,\n              ),\n            );\n            this.changes++;\n          } else if (multiComputedMemberMatcher.match(path.node)) {\n            const binding = path.scope.getBinding(tmpVar.current!.name);\n            const binding2 = path.scope.getBinding(tmpVar2.current!.name);\n            if (\n              !isTemporaryVariable(binding, 1) ||\n              !isTemporaryVariable(binding2, 1)\n            )\n              return;\n\n            binding.path.remove();\n            binding2.path.remove();\n            path.replaceWith(\n              t.assignmentExpression(\n                operator.current! + '=',\n                t.memberExpression(object.current!, property.current!, true),\n                right.current!,\n              ),\n            );\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\nimport { isTemporaryVariable } from '../../ast-utils';\n\nexport default {\n  name: 'nullish-coalescing',\n  tags: ['safe'],\n  scope: true,\n  visitor() {\n    const tmpVar = m.capture(m.identifier());\n    const left = m.capture(m.anyExpression());\n    const right = m.capture(m.anyExpression());\n    // Example (Babel): var _tmp; (_tmp = left) !== null && _tmp !== undefined ? _tmp : right;\n    const idMatcher = m.conditionalExpression(\n      m.logicalExpression(\n        '&&',\n        m.binaryExpression(\n          '!==',\n          m.assignmentExpression('=', tmpVar, left),\n          m.nullLiteral(),\n        ),\n        m.binaryExpression(\n          '!==',\n          m.fromCapture(tmpVar),\n          m.identifier('undefined'),\n        ),\n      ),\n      m.fromCapture(tmpVar),\n      right,\n    );\n\n    const idLooseMatcher = m.conditionalExpression(\n      m.binaryExpression(\n        '!=',\n        m.assignmentExpression('=', tmpVar, left),\n        m.nullLiteral(),\n      ),\n      m.fromCapture(tmpVar),\n      right,\n    );\n\n    // Example (SWC/esbuild): left != null ? left : (left = right);\n    // Example (TS): left !== null && left !== undefined ? left : (left = right);\n    const simpleIdMatcher = m.conditionalExpression(\n      m.or(\n        m.logicalExpression(\n          '&&',\n          m.binaryExpression('!==', left, m.nullLiteral()),\n          m.binaryExpression(\n            '!==',\n            m.fromCapture(left),\n            m.identifier('undefined'),\n          ),\n        ),\n        m.binaryExpression('!=', left, m.nullLiteral()),\n      ),\n      m.fromCapture(left),\n      right,\n    );\n\n    const iifeMatcher = m.callExpression(\n      m.arrowFunctionExpression(\n        [m.fromCapture(tmpVar)],\n        m.anyExpression(),\n        false,\n      ),\n      [],\n    );\n\n    return {\n      ConditionalExpression: {\n        exit(path) {\n          if (idMatcher.match(path.node)) {\n            const binding = path.scope.getBinding(tmpVar.current!.name);\n\n            if (\n              iifeMatcher.match(path.parentPath.parent) &&\n              isTemporaryVariable(binding, 2, 'param')\n            ) {\n              path.parentPath.parentPath!.replaceWith(\n                t.logicalExpression('??', left.current!, right.current!),\n              );\n              this.changes++;\n            } else if (isTemporaryVariable(binding, 2, 'var')) {\n              binding.path.remove();\n              path.replaceWith(\n                t.logicalExpression('??', left.current!, right.current!),\n              );\n              this.changes++;\n            }\n          } else if (idLooseMatcher.match(path.node)) {\n            const binding = path.scope.getBinding(tmpVar.current!.name);\n            if (!isTemporaryVariable(binding, 1)) return;\n\n            binding.path.remove();\n            path.replaceWith(\n              t.logicalExpression('??', left.current!, right.current!),\n            );\n            this.changes++;\n          } else if (simpleIdMatcher.match(path.node)) {\n            path.replaceWith(\n              t.logicalExpression('??', left.current!, right.current!),\n            );\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\nimport { isTemporaryVariable } from '../../ast-utils';\n\nexport default {\n  name: 'nullish-coalescing-assignment',\n  tags: ['safe'],\n  scope: true,\n  visitor() {\n    const tmpVar = m.capture(m.identifier());\n    const leftId = m.capture(m.identifier());\n    const property = m.capture(m.identifier());\n    const right = m.capture(m.anyExpression());\n    const computed = m.capture<boolean>(m.anything());\n    // Example (Babel):  var tmp; (tmp = left).b ?? (tmp.b = c);\n    const memberMatcher = m.logicalExpression(\n      '??',\n      m.memberExpression(\n        m.assignmentExpression('=', tmpVar, leftId),\n        property,\n        computed,\n      ),\n      m.assignmentExpression(\n        '=',\n        m.memberExpression(\n          m.fromCapture(tmpVar),\n          m.fromCapture(property),\n          computed,\n        ),\n        right,\n      ),\n    );\n\n    // Example (Babel): left ?? (left = right);\n    const left = m.capture(m.or(m.identifier(), m.memberExpression()));\n    const simpleMatcher = m.logicalExpression(\n      '??',\n      left,\n      m.assignmentExpression('=', m.fromCapture(left), right),\n    );\n\n    return {\n      LogicalExpression: {\n        exit(path) {\n          if (memberMatcher.match(path.node)) {\n            const binding = path.scope.getBinding(tmpVar.current!.name);\n            if (!isTemporaryVariable(binding, 1)) return;\n\n            binding.path.remove();\n            path.replaceWith(\n              t.assignmentExpression(\n                '??=',\n                t.memberExpression(\n                  leftId.current!,\n                  property.current!,\n                  computed.current,\n                ),\n                right.current!,\n              ),\n            );\n            this.changes++;\n          } else if (simpleMatcher.match(path.node)) {\n            path.replaceWith(\n              t.assignmentExpression('??=', left.current!, right.current!),\n            );\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\nimport { isTemporaryVariable } from '../../ast-utils';\n\nexport default {\n  name: 'optional-chaining',\n  tags: ['safe'],\n  scope: true,\n  visitor() {\n    const object = m.capture(m.anyExpression());\n    const member = m.capture(m.memberExpression(m.fromCapture(object)));\n    // Example (TS): object === null || object === undefined ? undefined : object.property;\n    const simpleMatcher = m.conditionalExpression(\n      m.logicalExpression(\n        '||',\n        m.binaryExpression('===', object, m.nullLiteral()),\n        m.binaryExpression(\n          '===',\n          m.fromCapture(object),\n          m.identifier('undefined'),\n        ),\n      ),\n      m.identifier('undefined'),\n      member,\n    );\n\n    const tmpVar = m.capture(m.identifier());\n    const tmpMember = m.capture(m.memberExpression(m.fromCapture(tmpVar)));\n    // Example (Babel): var _tmp; (_tmp = object) === null || _tmp === undefined ? undefined : _tmp.property;\n    const tmpMatcher = m.conditionalExpression(\n      m.logicalExpression(\n        '||',\n        m.binaryExpression(\n          '===',\n          m.assignmentExpression('=', tmpVar, object),\n          m.nullLiteral(),\n        ),\n        m.binaryExpression(\n          '===',\n          m.fromCapture(tmpVar),\n          m.identifier('undefined'),\n        ),\n      ),\n      m.identifier('undefined'),\n      tmpMember,\n    );\n\n    return {\n      ConditionalExpression: {\n        exit(path) {\n          if (simpleMatcher.match(path.node)) {\n            member.current!.optional = true;\n            path.replaceWith(\n              t.optionalMemberExpression(\n                object.current!,\n                member.current!.property as t.Expression,\n                member.current!.computed,\n                true,\n              ),\n            );\n            this.changes++;\n          } else if (tmpMatcher.match(path.node)) {\n            const binding = path.scope.getBinding(tmpVar.current!.name);\n            if (!isTemporaryVariable(binding, 2)) return;\n\n            binding.path.remove();\n            tmpMember.current!.optional = true;\n            path.replaceWith(\n              t.optionalMemberExpression(\n                object.current!,\n                tmpMember.current!.property as t.Expression,\n                tmpMember.current!.computed,\n                true,\n              ),\n            );\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\nimport { constMemberExpression } from '../../ast-utils';\n\n// https://github.com/babel/babel/pull/5791\n// https://github.com/babel/babel/blob/cce807f1eb638ee3030112dc190cbee032760888/packages/babel-plugin-transform-template-literals/src/index.ts\n\n// TODO: option ignoreToPrimitiveHint (uses `+` instead of concat)\n\nfunction escape(str: string) {\n  return (\n    str\n      .replaceAll('\\\\', '\\\\\\\\')\n      .replaceAll('`', '\\\\`')\n      .replaceAll('$', '\\\\$')\n      .replaceAll('\\0', '\\\\0')\n      .replaceAll('\\b', '\\\\b')\n      .replaceAll('\\f', '\\\\f')\n      // .replaceAll('\\n', '\\\\n') // not escaped because multiline strings are preferred\n      .replaceAll('\\r', '\\\\r')\n      .replaceAll('\\t', '\\\\t')\n      .replaceAll('\\v', '\\\\v')\n  );\n}\n\nfunction push(template: t.TemplateLiteral, value: t.Expression) {\n  if (value.type === 'StringLiteral') {\n    const lastQuasi = template.quasis.at(-1)!;\n    lastQuasi.value.raw += escape(value.value);\n  } else if (value.type === 'TemplateLiteral') {\n    const lastQuasi = template.quasis.at(-1)!;\n    const firstQuasi = value.quasis[0];\n    lastQuasi.value.raw += firstQuasi.value.raw;\n    template.expressions.push(...value.expressions);\n    template.quasis.push(...value.quasis.slice(1));\n  } else {\n    template.expressions.push(value);\n    template.quasis.push(t.templateElement({ raw: '' }));\n  }\n}\n\nfunction unshift(template: t.TemplateLiteral, value: t.Expression) {\n  if (value.type === 'StringLiteral') {\n    const firstQuasi = template.quasis[0];\n    firstQuasi.value.raw = escape(value.value) + firstQuasi.value.raw;\n  } else {\n    template.expressions.unshift(value);\n    template.quasis.unshift(t.templateElement({ raw: '' }));\n  }\n}\n\nexport default {\n  name: 'template-literals',\n  tags: ['unsafe'],\n  visitor() {\n    const string = m.capture(m.or(m.stringLiteral(), m.templateLiteral()));\n    const concatMatcher = m.callExpression(\n      constMemberExpression(string, 'concat'),\n      m.arrayOf(m.anyExpression()),\n    );\n\n    return {\n      BinaryExpression: {\n        exit(path) {\n          if (path.node.operator !== '+') return;\n\n          if (t.isTemplateLiteral(path.node.left)) {\n            push(path.node.left, path.node.right);\n            path.replaceWith(path.node.left);\n            this.changes++;\n          } else if (\n            t.isTemplateLiteral(path.node.right) &&\n            t.isExpression(path.node.left)\n          ) {\n            unshift(path.node.right, path.node.left);\n            path.replaceWith(path.node.right);\n            this.changes++;\n          }\n        },\n      },\n      CallExpression: {\n        exit(path) {\n          if (concatMatcher.match(path.node)) {\n            const template = t.templateLiteral(\n              [t.templateElement({ raw: '' })],\n              [],\n            );\n            push(template, string.current!);\n\n            for (const arg of path.node.arguments) {\n              push(template, arg as t.Expression);\n            }\n\n            path.replaceWith(template);\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import { mergeTransforms } from '../ast-utils';\nimport * as transforms from './transforms';\n\nexport default mergeTransforms({\n  name: 'transpile',\n  tags: ['safe'],\n  transforms: Object.values(transforms),\n});\n", "export { default as blockStatements } from './block-statements';\nexport { default as computedProperties } from './computed-properties';\nexport { default as forToWhile } from './for-to-while';\nexport { default as infinity } from './infinity';\nexport { default as invertBooleanLogic } from './invert-boolean-logic';\nexport { default as jsonParse } from './json-parse';\nexport { default as logicalToIf } from './logical-to-if';\nexport { default as mergeElseIf } from './merge-else-if';\nexport { default as mergeStrings } from './merge-strings';\nexport { default as numberExpressions } from './number-expressions';\nexport { default as rawLiterals } from './raw-literals';\nexport { default as removeDoubleNot } from './remove-double-not';\nexport { default as sequence } from './sequence';\nexport { default as splitForLoopVars } from './split-for-loop-vars';\nexport { default as splitVariableDeclarations } from './split-variable-declarations';\nexport { default as ternaryToIf } from './ternary-to-if';\nexport { default as typeofUndefined } from './typeof-undefined';\nexport { default as unaryExpressions } from './unary-expressions';\nexport { default as unminifyBooleans } from './unminify-booleans';\nexport { default as voidToUndefined } from './void-to-undefined';\nexport { default as yoda } from './yoda';\n", "import * as t from '@babel/types';\nimport type { Transform } from '../../ast-utils';\n\nexport default {\n  name: 'block-statements',\n  tags: ['safe'],\n  visitor: () => ({\n    IfStatement: {\n      exit(path) {\n        if (\n          !t.isBlockStatement(path.node.consequent) &&\n          !t.isEmptyStatement(path.node.consequent)\n        ) {\n          path.node.consequent = t.blockStatement([path.node.consequent]);\n\n          this.changes++;\n        }\n        if (path.node.alternate && !t.isBlockStatement(path.node.alternate)) {\n          path.node.alternate = t.blockStatement([path.node.alternate]);\n          this.changes++;\n        }\n      },\n    },\n    Loop: {\n      exit(path) {\n        if (\n          !t.isBlockStatement(path.node.body) &&\n          !t.isEmptyStatement(path.node.body)\n        ) {\n          path.node.body = t.blockStatement([path.node.body]);\n\n          this.changes++;\n        }\n      },\n    },\n    ArrowFunctionExpression: {\n      exit(path) {\n        if (t.isSequenceExpression(path.node.body)) {\n          path.node.body = t.blockStatement([\n            t.returnStatement(path.node.body),\n          ]);\n\n          this.changes++;\n        }\n      },\n    },\n  }),\n} satisfies Transform;\n", "import { isIdentifierName } from '@babel/helper-validator-identifier';\nimport * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\n\nexport default {\n  name: 'computed-properties',\n  tags: ['safe'],\n  visitor() {\n    const stringMatcher = m.capture(\n      m.stringLiteral(m.matcher((value) => isIdentifierName(value))),\n    );\n    const propertyMatcher = m.or(\n      m.memberExpression(m.anything(), stringMatcher, true),\n      m.optionalMemberExpression(m.anything(), stringMatcher, true),\n    );\n    const keyMatcher = m.or(\n      m.objectProperty(stringMatcher),\n      m.classProperty(stringMatcher),\n      m.objectMethod(undefined, stringMatcher),\n      m.classMethod(undefined, stringMatcher),\n    );\n\n    return {\n      'MemberExpression|OptionalMemberExpression': {\n        exit(path) {\n          if (!propertyMatcher.match(path.node)) return;\n          path.node.computed = false;\n          path.node.property = t.identifier(stringMatcher.current!.value);\n          this.changes++;\n        },\n      },\n      'ObjectProperty|ClassProperty|ObjectMethod|ClassMethod': {\n        exit(path) {\n          if (!keyMatcher.match(path.node)) return;\n          if (\n            (path.type === 'ClassMethod' &&\n              stringMatcher.current!.value === 'constructor') ||\n            (path.type === 'ObjectProperty' &&\n              stringMatcher.current!.value === '__proto__')\n          )\n            return;\n\n          path.node.computed = false;\n          path.node.key = t.identifier(stringMatcher.current!.value);\n          this.changes++;\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport type { Transform } from '../../ast-utils';\n\nexport default {\n  name: 'for-to-while',\n  tags: ['safe'],\n  visitor() {\n    return {\n      ForStatement: {\n        exit(path) {\n          const { test, body, init, update } = path.node;\n          if (init || update) return;\n          path.replaceWith(\n            t.whileStatement(test ?? t.booleanLiteral(true), body),\n          );\n          this.changes++;\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\n\nexport default {\n  name: 'infinity',\n  tags: ['safe'],\n  scope: true,\n  visitor: () => {\n    const infinityMatcher = m.binaryExpression(\n      '/',\n      m.numericLiteral(1),\n      m.numericLiteral(0),\n    );\n    const negativeInfinityMatcher = m.binaryExpression(\n      '/',\n      m.unaryExpression('-', m.numericLiteral(1)),\n      m.numericLiteral(0),\n    );\n\n    return {\n      BinaryExpression: {\n        exit(path) {\n          if (path.scope.hasBinding('Infinity', { noGlobals: true })) return;\n\n          if (infinityMatcher.match(path.node)) {\n            path.replaceWith(t.identifier('Infinity'));\n            this.changes++;\n          } else if (negativeInfinityMatcher.match(path.node)) {\n            path.replaceWith(t.unaryExpression('-', t.identifier('Infinity')));\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\n\n// >, >=, <, <= are not invertible because NaN <= 0 is false and NaN > 0 is false\n// https://tc39.es/ecma262/multipage/abstract-operations.html#sec-islessthan\n\nconst INVERTED_BINARY_OPERATORS = {\n  '==': '!=',\n  '===': '!==',\n  '!=': '==',\n  '!==': '===',\n} as const;\n\nconst INVERTED_LOGICAL_OPERATORS = {\n  '||': '&&',\n  '&&': '||',\n} as const;\n\nexport default {\n  name: 'invert-boolean-logic',\n  tags: ['safe'],\n  visitor: () => {\n    const logicalExpression = m.logicalExpression(\n      m.or(...Object.values(INVERTED_LOGICAL_OPERATORS)),\n    );\n    const logicalMatcher = m.unaryExpression('!', logicalExpression);\n\n    const binaryExpression = m.capture(\n      m.binaryExpression(m.or(...Object.values(INVERTED_BINARY_OPERATORS))),\n    );\n    const binaryMatcher = m.unaryExpression('!', binaryExpression);\n\n    return {\n      UnaryExpression: {\n        exit(path) {\n          const { argument } = path.node;\n\n          if (binaryMatcher.match(path.node)) {\n            binaryExpression.current!.operator =\n              INVERTED_BINARY_OPERATORS[\n                binaryExpression.current!\n                  .operator as keyof typeof INVERTED_BINARY_OPERATORS\n              ];\n\n            path.replaceWith(binaryExpression.current!);\n            this.changes++;\n          } else if (logicalMatcher.match(path.node)) {\n            let current = argument;\n            while (logicalExpression.match(current)) {\n              current.operator =\n                INVERTED_LOGICAL_OPERATORS[\n                  current.operator as keyof typeof INVERTED_LOGICAL_OPERATORS\n                ];\n\n              current.right = t.unaryExpression('!', current.right);\n              if (!logicalExpression.match(current.left)) {\n                current.left = t.unaryExpression('!', current.left);\n              }\n              current = current.left;\n            }\n\n            path.replaceWith(argument);\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import { parseExpression } from '@babel/parser';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\nimport { constMemberExpression } from '../../ast-utils';\n\nexport default {\n  name: 'json-parse',\n  tags: ['safe'],\n  scope: true,\n  visitor: () => {\n    const string = m.capture(m.anyString());\n    const matcher = m.callExpression(constMemberExpression('JSON', 'parse'), [\n      m.stringLiteral(string),\n    ]);\n\n    return {\n      CallExpression: {\n        exit(path) {\n          if (\n            matcher.match(path.node) &&\n            !path.scope.hasBinding('JSON', { noGlobals: true })\n          ) {\n            try {\n              JSON.parse(string.current!);\n              const parsed = parseExpression(string.current!);\n              path.replaceWith(parsed);\n              this.changes++;\n            } catch {\n              // ignore\n            }\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import { statement } from '@babel/template';\nimport * as t from '@babel/types';\nimport type { Transform } from '../../ast-utils';\n\nexport default {\n  name: 'logical-to-if',\n  tags: ['safe'],\n  visitor: () => {\n    const buildIf = statement`if (TEST) { BODY; }`;\n    const buildIfNot = statement`if (!TEST) { BODY; }`;\n\n    return {\n      ExpressionStatement: {\n        exit(path) {\n          const expression = path.node.expression as t.LogicalExpression;\n          if (!t.isLogicalExpression(expression)) return;\n          if (expression.operator === '&&') {\n            path.replaceWith(\n              buildIf({\n                TEST: expression.left,\n                BODY: expression.right,\n              }),\n            );\n            this.changes++;\n          } else if (expression.operator === '||') {\n            path.replaceWith(\n              buildIfNot({\n                TEST: expression.left,\n                BODY: expression.right,\n              }),\n            );\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\n\nexport default {\n  name: 'merge-else-if',\n  tags: ['safe'],\n  visitor() {\n    const nestedIf = m.capture(m.ifStatement());\n    const matcher = m.ifStatement(\n      m.anything(),\n      m.anything(),\n      m.blockStatement([nestedIf]),\n    );\n\n    return {\n      IfStatement: {\n        exit(path) {\n          if (matcher.match(path.node)) {\n            path.node.alternate = nestedIf.current;\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\n\nexport default {\n  name: 'number-expressions',\n  tags: ['safe'],\n  visitor: () => ({\n    'BinaryExpression|UnaryExpression': {\n      exit(path) {\n        if (!matcher.match(path.node)) return;\n        const evaluated = path.evaluate();\n        if (\n          t.isBinaryExpression(path.node, { operator: '/' }) &&\n          !Number.isInteger(evaluated.value)\n        ) {\n          return;\n        }\n        path.replaceWith(t.valueToNode(evaluated.value));\n        path.skip();\n        this.changes++;\n      },\n    },\n  }),\n} satisfies Transform;\n\nconst matcher = m.or(\n  m.unaryExpression('-', m.or(m.stringLiteral(), m.numericLiteral())),\n  m.binaryExpression(\n    m.or('+', '-', '/', '%', '*', '**', '&', '|', '>>', '>>>', '<<', '^'),\n    m.or(\n      m.stringLiteral(),\n      m.numericLiteral(),\n      m.unaryExpression('-', m.numericLiteral()),\n    ),\n    m.or(\n      m.stringLiteral(),\n      m.numericLiteral(),\n      m.unaryExpression('-', m.numericLiteral()),\n    ),\n  ),\n);\n", "import type { Transform } from '../../ast-utils';\n\nexport default {\n  name: 'raw-literals',\n  tags: ['safe'],\n  visitor: () => ({\n    StringLiteral(path) {\n      if (path.node.extra) {\n        path.node.extra = undefined;\n        this.changes++;\n      }\n    },\n    NumericLiteral(path) {\n      if (path.node.extra) {\n        path.node.extra = undefined;\n        this.changes++;\n      }\n    },\n  }),\n} satisfies Transform;\n", "import type { NodePath } from '@babel/traverse';\nimport * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport { constMemberExpression, type Transform } from '../../ast-utils';\n\nexport default {\n  name: 'remove-double-not',\n  tags: ['safe'],\n  visitor() {\n    const expression = m.capture(m.anyExpression());\n    const doubleNot = m.unaryExpression(\n      '!',\n      m.unaryExpression('!', expression),\n    );\n    const tripleNot = m.unaryExpression('!', doubleNot);\n    const arrayCall = m.callExpression(\n      constMemberExpression(\n        m.arrayExpression(),\n        m.or(\n          'filter',\n          'find',\n          'findLast',\n          'findIndex',\n          'findLastIndex',\n          'some',\n          'every',\n        ),\n      ),\n      [m.arrowFunctionExpression(m.anything(), doubleNot)],\n    );\n\n    return {\n      Conditional: {\n        exit(path) {\n          if (doubleNot.match(path.node.test)) {\n            path.get('test').replaceWith(expression.current!);\n            this.changes++;\n          }\n        },\n      },\n      UnaryExpression: {\n        exit(path) {\n          if (tripleNot.match(path.node)) {\n            path.replaceWith(t.unaryExpression('!', expression.current!));\n            this.changes++;\n          }\n        },\n      },\n      CallExpression: {\n        exit(path) {\n          if (arrayCall.match(path.node)) {\n            (path.get('arguments.0.body') as NodePath).replaceWith(\n              expression.current!,\n            );\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport { safeLiteral, type Transform } from '../../ast-utils';\n\nexport default {\n  name: 'sequence',\n  tags: ['safe'],\n  visitor() {\n    // To retain the evaluation order of `<anything> = (x(), y());`, only identifiers and member expressions are allowed.\n    // `obj.foo.bar = (x(), y());` would trigger the getter for `obj.foo` before `x()` is evaluated.\n    const assignmentVariable = m.or(\n      m.identifier(),\n      m.memberExpression(m.identifier(), m.or(m.identifier(), safeLiteral)),\n    );\n    const assignedSequence = m.capture(m.sequenceExpression());\n    const assignmentMatcher = m.assignmentExpression(\n      // \"||=\", \"&&=\", and \"??=\" have short-circuiting behavior\n      m.or(\n        '=',\n        '+=',\n        '-=',\n        '*=',\n        '/=',\n        '%=',\n        '**=',\n        '<<=',\n        '>>=',\n        '>>>=',\n        '|=',\n        '^=',\n        '&=',\n      ),\n      assignmentVariable,\n      assignedSequence,\n    );\n\n    return {\n      AssignmentExpression: {\n        exit(path) {\n          if (!assignmentMatcher.match(path.node)) return;\n\n          const { expressions } = assignedSequence.current!;\n          path.node.right = expressions.pop()!;\n          const newNodes = path.parentPath.isExpressionStatement()\n            ? expressions.map(t.expressionStatement)\n            : expressions;\n          path.insertBefore(newNodes);\n          this.changes++;\n        },\n      },\n      ExpressionStatement: {\n        exit(path) {\n          if (!t.isSequenceExpression(path.node.expression)) return;\n\n          const statements = path.node.expression.expressions.map(\n            t.expressionStatement,\n          );\n          path.replaceWithMultiple(statements);\n          this.changes++;\n        },\n      },\n      ReturnStatement: {\n        exit(path) {\n          if (!t.isSequenceExpression(path.node.argument)) return;\n\n          const { expressions } = path.node.argument;\n          path.node.argument = expressions.pop();\n          const statements = expressions.map(t.expressionStatement);\n          path.insertBefore(statements);\n          this.changes++;\n        },\n      },\n      IfStatement: {\n        exit(path) {\n          if (!t.isSequenceExpression(path.node.test)) return;\n\n          const { expressions } = path.node.test;\n          path.node.test = expressions.pop()!;\n          const statements = expressions.map(t.expressionStatement);\n          path.insertBefore(statements);\n          this.changes++;\n        },\n      },\n      SwitchStatement: {\n        exit(path) {\n          if (!t.isSequenceExpression(path.node.discriminant)) return;\n\n          const { expressions } = path.node.discriminant;\n          path.node.discriminant = expressions.pop()!;\n          const statements = expressions.map(t.expressionStatement);\n          path.insertBefore(statements);\n          this.changes++;\n        },\n      },\n      ThrowStatement: {\n        exit(path) {\n          if (!t.isSequenceExpression(path.node.argument)) return;\n\n          const { expressions } = path.node.argument;\n          path.node.argument = expressions.pop()!;\n          const statements = expressions.map(t.expressionStatement);\n          path.insertBefore(statements);\n          this.changes++;\n        },\n      },\n      ForInStatement: {\n        exit(path) {\n          if (!t.isSequenceExpression(path.node.right)) return;\n\n          const { expressions } = path.node.right;\n          path.node.right = expressions.pop()!;\n          const statements = expressions.map(t.expressionStatement);\n          path.insertBefore(statements);\n          this.changes++;\n        },\n      },\n      ForOfStatement: {\n        exit(path) {\n          if (!t.isSequenceExpression(path.node.right)) return;\n\n          const { expressions } = path.node.right;\n          path.node.right = expressions.pop()!;\n          const statements = expressions.map(t.expressionStatement);\n          path.insertBefore(statements);\n          this.changes++;\n        },\n      },\n      ForStatement: {\n        exit(path) {\n          if (t.isSequenceExpression(path.node.init)) {\n            const statements = path.node.init.expressions.map(\n              t.expressionStatement,\n            );\n            path.node.init = null;\n            path.insertBefore(statements);\n            this.changes++;\n          }\n          if (\n            t.isSequenceExpression(path.node.update) &&\n            path.node.body.type === 'EmptyStatement'\n          ) {\n            const { expressions } = path.node.update;\n            path.node.update = expressions.pop()!;\n            const statements = expressions.map(t.expressionStatement);\n            path.node.body = t.blockStatement(statements);\n            this.changes++;\n          }\n        },\n      },\n      VariableDeclaration: {\n        exit(path) {\n          const sequence = m.capture(m.sequenceExpression());\n          const matcher = m.variableDeclaration(undefined, [\n            m.variableDeclarator(undefined, sequence),\n          ]);\n          if (!matcher.match(path.node)) return;\n\n          const { expressions } = sequence.current!;\n          path.node.declarations[0].init = expressions.pop();\n          const statements = expressions.map(t.expressionStatement);\n          path.getStatementParent()?.insertBefore(statements);\n          this.changes++;\n        },\n      },\n      SequenceExpression: {\n        exit(path) {\n          const { expressions } = path.node;\n          if (expressions.every((node) => safeLiteral.match(node))) {\n            path.replaceWith(expressions.at(-1)!);\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\n\nconst matcher = m.forStatement(\n  m.variableDeclaration('var', m.arrayOf(m.variableDeclarator(m.identifier()))),\n);\n\n// This contains some overlapping logic with the \"split-variable-declarations\" transform\n// but is done separately because it also works with a single variable and we want to avoid\n// accessing scope in the prepare stage for performance reasons.\nexport default {\n  name: 'split-for-loop-vars',\n  tags: ['safe'],\n  scope: true,\n  visitor: () => ({\n    ForStatement: {\n      exit(path) {\n        if (!matcher.match(path.node)) return;\n        const { init, test, update } = path.node;\n        const { declarations } = init as t.VariableDeclaration;\n\n        for (let i = 0; i < declarations.length; i++) {\n          const declarator = declarations[i];\n          const binding = path.scope.getBinding(\n            (declarator.id as t.Identifier).name,\n          );\n          if (!binding) break;\n\n          const isUsedInTestOrUpdate =\n            binding.constantViolations.some((reference) =>\n              reference.find((p) => p.node === test || p.node === update),\n            ) ||\n            binding.referencePaths.some((reference) =>\n              reference.find((p) => p.node === test || p.node === update),\n            );\n          if (isUsedInTestOrUpdate) break;\n\n          path.insertBefore(t.variableDeclaration('var', [declarator]));\n          declarations.shift();\n          i--;\n          this.changes++;\n        }\n\n        if (declarations.length === 0) path.get('init').remove();\n      },\n    },\n  }),\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport type { Transform } from '../../ast-utils';\n\nexport default {\n  name: 'split-variable-declarations',\n  tags: ['safe'],\n  visitor: () => ({\n    VariableDeclaration: {\n      exit(path) {\n        if (path.node.declarations.length > 1) {\n          // E.g. for (var i = 0, j = 1;;)\n          if (path.key === 'init' && path.parentPath.isForStatement()) {\n            if (\n              !path.parentPath.node.test &&\n              !path.parentPath.node.update &&\n              path.node.kind === 'var'\n            ) {\n              path.parentPath.insertBefore(\n                path.node.declarations.map((declaration) =>\n                  t.variableDeclaration(path.node.kind, [declaration]),\n                ),\n              );\n              path.remove();\n              this.changes++;\n            }\n          } else {\n            if (path.parentPath.isExportNamedDeclaration()) {\n              path.parentPath.replaceWithMultiple(\n                path.node.declarations.map((declaration) =>\n                  t.exportNamedDeclaration(\n                    t.variableDeclaration(path.node.kind, [declaration]),\n                  ),\n                ),\n              );\n            } else {\n              path.replaceWithMultiple(\n                path.node.declarations.map((declaration) =>\n                  t.variableDeclaration(path.node.kind, [declaration]),\n                ),\n              );\n            }\n            this.changes++;\n          }\n        }\n      },\n    },\n  }),\n} satisfies Transform;\n", "import { statement } from '@babel/template';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\n\nexport default {\n  name: 'ternary-to-if',\n  tags: ['safe'],\n  visitor() {\n    const test = m.capture(m.anyExpression());\n    const consequent = m.capture(m.anyExpression());\n    const alternate = m.capture(m.anyExpression());\n    const conditional = m.conditionalExpression(test, consequent, alternate);\n\n    const buildIf = statement`if (TEST) { CONSEQUENT; } else { ALTERNATE; }`;\n    const buildIfReturn = statement`if (TEST) { return CONSEQUENT; } else { return ALTERNATE; }`;\n\n    return {\n      ExpressionStatement: {\n        exit(path) {\n          if (conditional.match(path.node.expression)) {\n            path.replaceWith(\n              buildIf({\n                TEST: test.current,\n                CONSEQUENT: consequent.current,\n                ALTERNATE: alternate.current,\n              }),\n            );\n            this.changes++;\n          }\n        },\n      },\n      ReturnStatement: {\n        exit(path) {\n          if (conditional.match(path.node.argument)) {\n            path.replaceWith(\n              buildIfReturn({\n                TEST: test.current,\n                CONSEQUENT: consequent.current,\n                ALTERNATE: alternate.current,\n              }),\n            );\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\n\nconst OPERATOR_MAP = {\n  '>': '===',\n  '<': '!==',\n} as const;\n\nexport default {\n  name: 'typeof-undefined',\n  tags: ['safe'],\n  visitor() {\n    const operator = m.capture(m.or('>' as const, '<' as const));\n    const argument = m.capture(m.anyExpression());\n    const matcher = m.binaryExpression(\n      operator,\n      m.unaryExpression('typeof', argument),\n      m.stringLiteral('u'),\n    );\n    return {\n      BinaryExpression: {\n        exit(path) {\n          if (!matcher.match(path.node)) return;\n          path.replaceWith(\n            t.binaryExpression(\n              OPERATOR_MAP[operator.current!],\n              t.unaryExpression('typeof', argument.current!),\n              t.stringLiteral('undefined'),\n            ),\n          );\n          this.changes++;\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\n\nexport default {\n  name: 'unary-expressions',\n  tags: ['safe'],\n  visitor() {\n    const argument = m.capture(m.anyExpression());\n    const matcher = m.expressionStatement(\n      m.unaryExpression(m.or('void', '!', 'typeof'), argument),\n    );\n    const returnVoid = m.returnStatement(m.unaryExpression('void', argument));\n    return {\n      ExpressionStatement: {\n        exit(path) {\n          if (!matcher.match(path.node)) return;\n          path.replaceWith(argument.current!);\n          this.changes++;\n        },\n      },\n      ReturnStatement: {\n        exit(path) {\n          if (!returnVoid.match(path.node)) return;\n          path.replaceWith(argument.current!);\n          path.insertAfter(t.returnStatement());\n          this.changes++;\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\n\nexport default {\n  name: 'unminify-booleans',\n  tags: ['safe'],\n  visitor: () => ({\n    UnaryExpression(path) {\n      if (trueMatcher.match(path.node)) {\n        path.replaceWith(t.booleanLiteral(true));\n        this.changes++;\n      } else if (falseMatcher.match(path.node)) {\n        path.replaceWith(t.booleanLiteral(false));\n        this.changes++;\n      }\n    },\n  }),\n} satisfies Transform;\n\nconst trueMatcher = m.or(\n  m.unaryExpression('!', m.numericLiteral(0)),\n  m.unaryExpression('!', m.unaryExpression('!', m.numericLiteral(1))),\n  m.unaryExpression('!', m.unaryExpression('!', m.arrayExpression([]))),\n);\n\nconst falseMatcher = m.or(\n  m.unaryExpression('!', m.numericLiteral(1)),\n  m.unaryExpression('!', m.arrayExpression([])),\n);\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\n\nexport default {\n  name: 'void-to-undefined',\n  tags: ['safe'],\n  scope: true,\n  visitor: () => {\n    const matcher = m.unaryExpression('void', m.numericLiteral(0));\n    return {\n      UnaryExpression: {\n        exit(path) {\n          if (\n            matcher.match(path.node) &&\n            !path.scope.hasBinding('undefined', { noGlobals: true })\n          ) {\n            path.replaceWith(t.identifier('undefined'));\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\n\n// https://eslint.org/docs/latest/rules/yoda and https://babeljs.io/docs/en/babel-plugin-minify-flip-comparisons\n\nconst FLIPPED_OPERATORS = {\n  '==': '==',\n  '===': '===',\n  '!=': '!=',\n  '!==': '!==',\n  '>': '<',\n  '<': '>',\n  '>=': '<=',\n  '<=': '>=',\n  '*': '*',\n  '^': '^',\n  '&': '&',\n  '|': '|',\n} as const;\n\nexport default {\n  name: 'yoda',\n  tags: ['safe'],\n  visitor: () => {\n    const pureValue = m.or(\n      m.stringLiteral(),\n      m.numericLiteral(),\n      m.unaryExpression(\n        '-',\n        m.or(m.numericLiteral(), m.identifier('Infinity')),\n      ),\n      m.booleanLiteral(),\n      m.nullLiteral(),\n      m.identifier('undefined'),\n      m.identifier('NaN'),\n      m.identifier('Infinity'),\n    );\n    const matcher = m.binaryExpression(\n      m.or(...Object.values(FLIPPED_OPERATORS)),\n      pureValue,\n      m.matcher((node) => !pureValue.match(node)),\n    );\n\n    return {\n      BinaryExpression: {\n        exit(path) {\n          if (matcher.match(path.node)) {\n            path.replaceWith(\n              t.binaryExpression(\n                FLIPPED_OPERATORS[\n                  path.node.operator as keyof typeof FLIPPED_OPERATORS\n                ],\n                path.node.right,\n                path.node.left as t.Expression,\n              ),\n            );\n            this.changes++;\n          }\n        },\n      },\n    };\n  },\n} satisfies Transform;\n", "import { mergeTransforms } from '../ast-utils';\nimport * as transforms from './transforms';\n\nexport default mergeTransforms({\n  name: 'unminify',\n  tags: ['safe'],\n  transforms: Object.values(transforms),\n});\n", "import { parse } from '@babel/parser';\nimport traverse, { visitors } from '@babel/traverse';\nimport type * as t from '@babel/types';\nimport type * as m from '@codemod/matchers';\nimport { unpackBrowserify } from './browserify';\nimport type { Bundle } from './bundle';\nimport { unpackWebpack } from './webpack';\nimport debug from 'debug';\n\nexport { Bundle } from './bundle';\n\nexport function unpack(\n  code: string,\n  mappings: Record<string, m.Matcher<unknown>> = {},\n): Bundle | undefined {\n  const ast = parse(code, {\n    sourceType: 'unambiguous',\n    allowReturnOutsideFunction: true,\n    plugins: ['jsx'],\n  });\n  return unpackAST(ast, mappings);\n}\n\nexport function unpackAST(\n  ast: t.Node,\n  mappings: Record<string, m.Matcher<unknown>> = {},\n): Bundle | undefined {\n  const options: { bundle: Bundle | undefined } = { bundle: undefined };\n  const visitor = visitors.merge([\n    unpackWebpack.visitor(options),\n    unpackBrowserify.visitor(options),\n  ]);\n  traverse(ast, visitor, undefined, { changes: 0 });\n  // TODO: applyTransforms(ast, [unpackWebpack, unpackBrowserify]) instead\n  if (options.bundle) {\n    options.bundle.applyMappings(mappings);\n    options.bundle.applyTransforms();\n    debug('webcrack:unpack')('Bundle:', options.bundle.type);\n  }\n  return options.bundle;\n}\n", "import type { NodePath } from '@babel/traverse';\nimport * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\nimport { constKey, getPropName, iife, renameParameters } from '../../ast-utils';\nimport type { Bundle } from '../bundle';\nimport { resolveDependencyTree } from '../path';\nimport { BrowserifyBundle } from './bundle';\nimport { BrowserifyModule } from './module';\n\nexport const unpackBrowserify = {\n  name: 'unpack-browserify',\n  tags: ['unsafe'],\n  scope: true,\n  visitor(options) {\n    const modules = new Map<string, BrowserifyModule>();\n\n    const files = m.capture(\n      m.arrayOf(\n        m.objectProperty(\n          m.numericLiteral(),\n          m.arrayExpression([\n            // function(require, module, exports) {...}\n            m.functionExpression(),\n            // dependencies: { './add': 1, 'lib': 3 }\n            m.objectExpression(\n              m.arrayOf(\n                m.objectProperty(\n                  constKey(),\n                  m.or(\n                    m.numericLiteral(),\n                    m.identifier('undefined'),\n                    m.stringLiteral(),\n                  ),\n                ),\n              ),\n            ),\n          ]),\n        ),\n      ),\n    );\n    const entryIdMatcher = m.capture(m.numericLiteral());\n\n    const matcher = m.callExpression(\n      m.or(\n        // (function (files, cache, entryIds) {...})(...)\n        m.functionExpression(undefined, [\n          m.identifier(),\n          m.identifier(),\n          m.identifier(),\n        ]),\n        // (function () { function init(files, cache, entryIds) {...} return init; })()(...)\n        iife(\n          [],\n          m.blockStatement([\n            m.functionDeclaration(undefined, [\n              m.identifier(),\n              m.identifier(),\n              m.identifier(),\n            ]),\n            m.returnStatement(m.identifier()),\n          ]),\n        ),\n      ),\n      [\n        m.objectExpression(files),\n        m.objectExpression(),\n        m.arrayExpression([entryIdMatcher]),\n      ],\n    );\n\n    return {\n      CallExpression(path) {\n        if (!matcher.match(path.node)) return;\n        path.stop();\n\n        const entryId = entryIdMatcher.current!.value.toString();\n\n        const modulesPath = path.get(\n          files.currentKeys!.join('.'),\n        ) as NodePath<t.ObjectProperty>[];\n\n        const dependencyTree: Record<string, Record<string, string>> = {};\n\n        for (const moduleWrapper of modulesPath) {\n          const id = (\n            moduleWrapper.node.key as t.NumericLiteral\n          ).value.toString();\n          const fn = moduleWrapper.get(\n            'value.elements.0',\n          ) as NodePath<t.FunctionExpression>;\n\n          const dependencies: Record<string, string> = (dependencyTree[id] =\n            {});\n          const dependencyProperties = (\n            moduleWrapper.get(\n              'value.elements.1',\n            ) as NodePath<t.ObjectExpression>\n          ).node.properties as t.ObjectProperty[];\n\n          for (const dependency of dependencyProperties) {\n            // skip external dependencies like { vscode: undefined }\n            if (\n              dependency.value.type !== 'NumericLiteral' &&\n              dependency.value.type !== 'StringLiteral'\n            )\n              continue;\n\n            const filePath = getPropName(dependency.key)!;\n            const depId = dependency.value.value.toString();\n            dependencies[depId] = filePath;\n          }\n\n          renameParameters(fn, ['require', 'module', 'exports']);\n          const file = t.file(t.program(fn.node.body.body));\n          const module = new BrowserifyModule(\n            id,\n            file,\n            id === entryId,\n            dependencies,\n          );\n          modules.set(id.toString(), module);\n        }\n\n        const resolvedPaths = resolveDependencyTree(dependencyTree, entryId);\n\n        for (const module of modules.values()) {\n          module.path = resolvedPaths[module.id];\n        }\n\n        if (modules.size > 0) {\n          options!.bundle = new BrowserifyBundle(entryId, modules);\n        }\n      },\n    };\n  },\n} satisfies Transform<{ bundle: Bundle | undefined }>;\n", "import { posix } from 'node:path';\n\n// eslint-disable-next-line @typescript-eslint/unbound-method\nconst { dirname, join, relative } = posix;\n\nexport function relativePath(from: string, to: string): string {\n  if (to.startsWith('node_modules/')) return to.replace('node_modules/', '');\n  const relativePath = relative(dirname(from), to);\n  return relativePath.startsWith('.') ? relativePath : './' + relativePath;\n}\n\n/**\n * Resolve the path of each module of a browserify bundle\n * based on its dependencies.\n * @param tree module id -> dependencies (id -> path)\n * @param entry entry module id\n */\nexport function resolveDependencyTree(\n  tree: Record<string, Record<string, string>>,\n  entry: string,\n): Record<string, string> {\n  const paths = resolveTreePaths(tree, entry);\n  paths[entry] = './index.js';\n\n  const entryDepth = Object.values(paths).reduce(\n    (acc, path) => Math.max(acc, path.split('..').length),\n    0,\n  );\n  // If the entrypoint is in a subfolder, we need to make up a prefix to get rid of the `../`\n  const prefix = Array(entryDepth - 1)\n    .fill(0)\n    .map((_, i) => `tmp${i}`)\n    .join('/');\n\n  return Object.fromEntries(\n    Object.entries(paths).map(([id, path]) => {\n      const newPath = path.startsWith('node_modules/')\n        ? path\n        : join(prefix, path);\n      return [id, newPath];\n    }),\n  );\n}\n\n/**\n * Recursively resolve the paths of a dependency tree.\n */\nfunction resolveTreePaths(\n  graph: Record<string, Record<string, string>>,\n  entry: string,\n  cwd = '.',\n  paths: Record<string, string> = {},\n) {\n  const entries = Object.entries(graph[entry]);\n\n  for (const [id, name] of entries) {\n    const isCircular = Object.hasOwn(paths, id);\n    if (isCircular) continue;\n\n    let path: string;\n    if (name.startsWith('.')) {\n      path = join(cwd, name);\n      if (!path.endsWith('.js')) path += '.js';\n    } else {\n      path = join('node_modules', name, 'index.js');\n    }\n    paths[id] = path;\n\n    const newCwd = path.endsWith('.js') ? dirname(path) : path;\n    resolveTreePaths(graph, id, newCwd, paths);\n  }\n\n  return paths;\n}\n", "import traverse from '@babel/traverse';\nimport type * as m from '@codemod/matchers';\nimport { dirname, join, normalize, sep } from 'node:path';\nimport type { Module } from './module';\n\nexport class Bundle {\n  type: 'webpack' | 'browserify';\n  entryId: string;\n  modules: Map<string, Module>;\n\n  constructor(\n    type: 'webpack' | 'browserify',\n    entryId: string,\n    modules: Map<string, Module>,\n  ) {\n    this.type = type;\n    this.entryId = entryId;\n    this.modules = modules;\n  }\n\n  applyMappings(mappings: Record<string, m.Matcher<unknown>>): void {\n    const mappingPaths = Object.keys(mappings);\n    if (mappingPaths.length === 0) return;\n\n    const unusedMappings = new Set(mappingPaths);\n\n    for (const module of this.modules.values()) {\n      traverse(module.ast, {\n        enter(path) {\n          for (const mappingPath of mappingPaths) {\n            if (mappings[mappingPath].match(path.node)) {\n              if (unusedMappings.has(mappingPath)) {\n                unusedMappings.delete(mappingPath);\n              } else {\n                throw new Error(`Mapping ${mappingPath} is already used.`);\n              }\n              const resolvedPath = mappingPath.startsWith('./')\n                ? mappingPath\n                : `node_modules/${mappingPath}`;\n              module.path = resolvedPath;\n              path.stop();\n              break;\n            }\n          }\n        },\n        noScope: true,\n      });\n    }\n  }\n\n  /**\n   * Saves each module to a file and the bundle metadata to a JSON file.\n   * @param path Output directory\n   */\n  async save(path: string): Promise<void> {\n    const bundleJson = {\n      type: this.type,\n      entryId: this.entryId,\n      modules: Array.from(this.modules.values(), (module) => ({\n        id: module.id,\n        path: module.path,\n      })),\n    };\n\n    const { mkdir, writeFile } = await import('node:fs/promises');\n    await mkdir(path, { recursive: true });\n\n    await writeFile(\n      join(path, 'bundle.json'),\n      JSON.stringify(bundleJson, null, 2),\n      'utf8',\n    );\n\n    await Promise.all(\n      Array.from(this.modules.values(), async (module) => {\n        const modulePath = normalize(join(path, module.path));\n        if (!modulePath.startsWith(path + sep)) {\n          throw new Error(`detected path traversal: ${module.path}`);\n        }\n        await mkdir(dirname(modulePath), { recursive: true });\n        await writeFile(modulePath, module.code, 'utf8');\n      }),\n    );\n  }\n\n  applyTransforms(): void {}\n}\n", "import { Bundle } from '../bundle';\nimport type { BrowserifyModule } from './module';\n\nexport class BrowserifyBundle extends Bundle {\n  constructor(entryId: string, modules: Map<string, BrowserifyModule>) {\n    super('browserify', entryId, modules);\n  }\n}\n", "import type * as t from '@babel/types';\nimport { generate } from '../ast-utils';\n\nexport class Module {\n  id: string;\n  isEntry: boolean;\n  path: string;\n  /**\n   * @internal\n   */\n  ast: t.File;\n  #code: string | undefined;\n\n  constructor(id: string, ast: t.File, isEntry: boolean) {\n    this.id = id;\n    this.ast = ast;\n    this.isEntry = isEntry;\n    this.path = `./${isEntry ? 'index' : id}.js`;\n  }\n\n  /**\n   * @internal\n   */\n  regenerateCode(): string {\n    this.#code = generate(this.ast);\n    return this.#code;\n  }\n\n  get code(): string {\n    return this.#code ?? this.regenerateCode();\n  }\n\n  set code(code: string) {\n    this.#code = code;\n  }\n}\n", "import type * as t from '@babel/types';\nimport { Module } from '../module';\n\nexport class BrowserifyModule extends Module {\n  dependencies: Record<number, string>;\n\n  constructor(\n    id: string,\n    ast: t.File,\n    isEntry: boolean,\n    dependencies: Record<number, string>,\n  ) {\n    super(id, ast, isEntry);\n    this.dependencies = dependencies;\n  }\n}\n", "import type { NodePath } from '@babel/traverse';\nimport * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport type { Transform } from '../../ast-utils';\nimport {\n  constKey,\n  constMemberExpression,\n  getPropName,\n  renameParameters,\n} from '../../ast-utils';\nimport type { Bundle } from '../bundle';\nimport { WebpackBundle } from './bundle';\nimport { WebpackModule } from './module';\n\nexport const unpackWebpack = {\n  name: 'unpack-webpack',\n  tags: ['unsafe'],\n  scope: true,\n  visitor(options) {\n    const modules = new Map<string, WebpackModule>();\n\n    const entryIdMatcher = m.capture(m.numericLiteral());\n    const moduleFunctionsMatcher = m.capture(\n      m.or(\n        // E.g. [,,function (e, t, i) {...}, ...], index is the module ID\n        m.arrayExpression(\n          m.arrayOf(\n            m.or(m.functionExpression(), m.arrowFunctionExpression(), null),\n          ),\n        ),\n        // E.g. {0: function (e, t, i) {...}, ...}, key is the module ID\n        m.objectExpression(\n          m.arrayOf(\n            m.or(\n              m.objectProperty(\n                m.or(m.numericLiteral(), m.stringLiteral(), m.identifier()),\n                m.or(m.functionExpression(), m.arrowFunctionExpression()),\n              ),\n              // __webpack_public_path__ (c: \"\")\n              m.objectProperty(constKey('c'), m.stringLiteral()),\n            ),\n          ),\n        ),\n      ),\n    );\n\n    const webpack4Matcher = m.callExpression(\n      m.functionExpression(\n        undefined,\n        undefined,\n        m.blockStatement(\n          m.anyList<t.Statement>(\n            m.zeroOrMore(),\n            m.functionDeclaration(),\n            m.zeroOrMore(),\n            m.containerOf(\n              m.or(\n                // E.g. __webpack_require__.s = 2\n                m.assignmentExpression(\n                  '=',\n                  constMemberExpression(m.identifier(), 's'),\n                  entryIdMatcher,\n                ),\n                // E.g. return require(0);\n                m.callExpression(m.identifier(), [entryIdMatcher]),\n              ),\n            ),\n          ),\n        ),\n      ),\n      [moduleFunctionsMatcher],\n    );\n\n    const webpack5Matcher = m.callExpression(\n      m.arrowFunctionExpression(\n        undefined,\n        m.blockStatement(\n          m.anyList<t.Statement>(\n            m.zeroOrMore(),\n            m.variableDeclaration(undefined, [\n              m.variableDeclarator(undefined, moduleFunctionsMatcher),\n            ]),\n            // var installedModules = {};\n            m.variableDeclaration(),\n            m.zeroOrMore(),\n            m.or(\n              // __webpack_require__.s = 2\n              m.containerOf(\n                m.assignmentExpression(\n                  '=',\n                  constMemberExpression(m.identifier(), 's'),\n                  entryIdMatcher,\n                ),\n              ),\n              m.expressionStatement(\n                m.assignmentExpression(\n                  '=',\n                  constMemberExpression(\n                    m.identifier(),\n                    m.or('e', 'd', 'j', 'm', 'r'),\n                  ),\n                ),\n              ),\n            ),\n            m.zeroOrMore(),\n            // module.exports = entryModule\n            m.expressionStatement(\n              m.assignmentExpression(\n                '=',\n                constMemberExpression(m.identifier(), 'exports'),\n                m.identifier(),\n              ),\n            ),\n          ),\n        ),\n      ),\n    );\n\n    // Examples: self.webpackChunk_N_E, window.webpackJsonp, this.webpackJsonp\n    const jsonpGlobal = m.capture(\n      constMemberExpression(\n        m.or(\n          m.identifier(m.or('self', 'window', 'globalThis')),\n          m.thisExpression(),\n        ),\n        m.matcher((property) => property.startsWith('webpack')),\n      ),\n    );\n    // (window.webpackJsonp = window.webpackJsonp || []).push([[0], {...}])\n    const jsonpMatcher = m.callExpression(\n      constMemberExpression(\n        m.assignmentExpression(\n          '=',\n          jsonpGlobal,\n          m.logicalExpression(\n            '||',\n            m.fromCapture(jsonpGlobal),\n            m.arrayExpression([]),\n          ),\n        ),\n        'push',\n      ),\n      [\n        m.arrayExpression(\n          m.anyList(\n            m.arrayExpression(\n              m.arrayOf(m.or(m.numericLiteral(), m.stringLiteral())),\n            ), // chunkId\n            moduleFunctionsMatcher,\n            m.slice({ max: 1 }), // optional entry point like [[\"57iH\",19,24,25]] or a function\n          ),\n        ),\n      ],\n    );\n\n    return {\n      CallExpression(path) {\n        if (\n          !webpack4Matcher.match(path.node) &&\n          !webpack5Matcher.match(path.node) &&\n          !jsonpMatcher.match(path.node)\n        )\n          return;\n        path.stop();\n\n        const modulesPath = path.get(\n          moduleFunctionsMatcher.currentKeys!.join('.'),\n        ) as NodePath;\n\n        const moduleWrappers = modulesPath.isArrayExpression()\n          ? (modulesPath.get('elements') as NodePath<t.Node | null>[])\n          : (modulesPath.get('properties') as NodePath[]);\n\n        moduleWrappers.forEach((moduleWrapper, index) => {\n          let moduleId = index.toString();\n          if (t.isObjectProperty(moduleWrapper.node)) {\n            moduleId = getPropName(moduleWrapper.node.key)!;\n            moduleWrapper = moduleWrapper.get('value') as NodePath;\n          }\n\n          if (\n            moduleWrapper.isFunction() &&\n            moduleWrapper.node.body.type === 'BlockStatement'\n          ) {\n            renameParameters(moduleWrapper, ['module', 'exports', 'require']);\n            const file = t.file(t.program(moduleWrapper.node.body.body));\n\n            // Remove /***/ comments between modules (in webpack development builds)\n            const lastNode = file.program.body.at(-1);\n            if (\n              lastNode?.trailingComments?.length === 1 &&\n              lastNode.trailingComments[0].value === '*'\n            ) {\n              lastNode.trailingComments = null;\n            }\n\n            const module = new WebpackModule(\n              moduleId,\n              file,\n              moduleId === entryIdMatcher.current?.value.toString(),\n            );\n\n            modules.set(moduleId, module);\n          }\n        });\n\n        if (modules.size > 0) {\n          const entryId = entryIdMatcher.current?.value.toString() ?? '';\n          options!.bundle = new WebpackBundle(entryId, modules);\n        }\n      },\n    };\n  },\n} satisfies Transform<{ bundle: Bundle | undefined }>;\n", "import type { NodePath } from '@babel/traverse';\nimport traverse from '@babel/traverse';\nimport * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport { Bundle } from '../bundle';\nimport { relativePath } from '../path';\nimport { convertESM } from './esm';\nimport { convertDefaultRequire } from './getDefaultExport';\nimport type { WebpackModule } from './module';\nimport { inlineVarInjections } from './varInjection';\n\nexport class WebpackBundle extends Bundle {\n  constructor(entryId: string, modules: Map<string, WebpackModule>) {\n    super('webpack', entryId, modules);\n  }\n\n  /**\n   * Undoes some of the transformations that Webpack injected into the modules.\n   */\n  applyTransforms(): void {\n    this.modules.forEach(inlineVarInjections);\n    this.modules.forEach(convertESM);\n    convertDefaultRequire(this);\n    this.replaceRequirePaths();\n  }\n\n  /**\n   * Replaces `require(id)` calls with `require(\"./relative/path.js\")` calls.\n   */\n  private replaceRequirePaths() {\n    const requireId = m.capture(m.or(m.numericLiteral(), m.stringLiteral()));\n    const requireMatcher = m.or(\n      m.callExpression(m.identifier('require'), [requireId]),\n    );\n    const importId = m.capture(m.stringLiteral());\n    const importMatcher = m.importDeclaration(m.anything(), importId);\n\n    this.modules.forEach((module) => {\n      traverse(module.ast, {\n        'CallExpression|ImportDeclaration': (path) => {\n          let moduleId: string;\n          let arg: NodePath;\n\n          if (requireMatcher.match(path.node)) {\n            moduleId = requireId.current!.value.toString();\n            [arg] = path.get('arguments') as NodePath<t.Identifier>[];\n          } else if (importMatcher.match(path.node)) {\n            moduleId = importId.current!.value;\n            arg = path.get('source') as NodePath;\n          } else {\n            return;\n          }\n\n          const requiredModule = this.modules.get(moduleId);\n          arg.replaceWith(\n            t.stringLiteral(\n              relativePath(\n                module.path,\n                requiredModule?.path ?? `./${moduleId}.js`,\n              ),\n            ),\n          );\n          // For example if its stored in another chunk\n          if (!requiredModule) {\n            arg.addComment('leading', 'webcrack:missing');\n          }\n        },\n        noScope: true,\n      });\n    });\n  }\n}\n", "import { statement } from '@babel/template';\nimport type { NodePath } from '@babel/traverse';\nimport traverse from '@babel/traverse';\nimport * as t from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport { constMemberExpression, findPath, renameFast } from '../../ast-utils';\nimport type { WebpackModule } from './module';\n\nconst buildNamespaceImport = statement`import * as NAME from \"PATH\";`;\nconst buildNamedExportLet = statement`export let NAME = VALUE;`;\n\n/**\n * ```js\n * require.r(exports);\n * require.d(exports, 'counter', function () {\n *   return f;\n * });\n * let f = 1;\n * ```\n * ->\n * ```js\n * export let counter = 1;\n * ```\n */\nexport function convertESM(module: WebpackModule): void {\n  // E.g. require.r(exports);\n  const defineEsModuleMatcher = m.expressionStatement(\n    m.callExpression(constMemberExpression('require', 'r'), [m.identifier()]),\n  );\n\n  const exportsName = m.capture(m.identifier());\n  const exportedName = m.capture(m.anyString());\n  const returnedValue = m.capture(m.anyExpression());\n  // E.g. require.d(exports, \"counter\", function () { return f });\n  const defineExportMatcher = m.expressionStatement(\n    m.callExpression(constMemberExpression('require', 'd'), [\n      exportsName,\n      m.stringLiteral(exportedName),\n      m.functionExpression(\n        undefined,\n        [],\n        m.blockStatement([m.returnStatement(returnedValue)]),\n      ),\n    ]),\n  );\n\n  const emptyObjectVarMatcher = m.variableDeclarator(\n    m.fromCapture(exportsName),\n    m.objectExpression([]),\n  );\n\n  const properties = m.capture(\n    m.arrayOf(\n      m.objectProperty(\n        m.identifier(),\n        m.arrowFunctionExpression([], m.anyExpression()),\n      ),\n    ),\n  );\n  // E.g. require.d(exports, { foo: () => a, bar: () => b });\n  const defineExportsMatcher = m.expressionStatement(\n    m.callExpression(constMemberExpression('require', 'd'), [\n      exportsName,\n      m.objectExpression(properties),\n    ]),\n  );\n\n  // E.g. const lib = require(\"./lib.js\");\n  const requireVariable = m.capture(m.identifier());\n  const requiredModuleId = m.capture(m.anyNumber());\n  const requireMatcher = m.variableDeclaration(undefined, [\n    m.variableDeclarator(\n      requireVariable,\n      m.callExpression(m.identifier('require'), [\n        m.numericLiteral(requiredModuleId),\n      ]),\n    ),\n  ]);\n\n  // module = require.hmd(module);\n  const hmdMatcher = m.expressionStatement(\n    m.assignmentExpression(\n      '=',\n      m.identifier('module'),\n      m.callExpression(constMemberExpression('require', 'hmd')),\n    ),\n  );\n\n  traverse(module.ast, {\n    enter(path) {\n      // Only traverse the top-level\n      if (path.parentPath?.parentPath) return path.skip();\n\n      if (defineEsModuleMatcher.match(path.node)) {\n        module.ast.program.sourceType = 'module';\n        path.remove();\n      } else if (\n        module.ast.program.sourceType === 'module' &&\n        requireMatcher.match(path.node)\n      ) {\n        path.replaceWith(\n          buildNamespaceImport({\n            NAME: requireVariable.current,\n            PATH: String(requiredModuleId.current),\n          }),\n        );\n      } else if (defineExportsMatcher.match(path.node)) {\n        const exportsBinding = path.scope.getBinding(exportsName.current!.name);\n        const emptyObject = emptyObjectVarMatcher.match(\n          exportsBinding?.path.node,\n        )\n          ? (exportsBinding?.path.node.init as t.ObjectExpression)\n          : null;\n\n        for (const property of properties.current!) {\n          const exportedKey = property.key as t.Identifier;\n          const returnedValue = (property.value as t.ArrowFunctionExpression)\n            .body as t.Expression;\n          if (emptyObject) {\n            emptyObject.properties.push(\n              t.objectProperty(exportedKey, returnedValue),\n            );\n          } else {\n            exportVariable(path, returnedValue, exportedKey.name);\n          }\n        }\n\n        path.remove();\n      } else if (defineExportMatcher.match(path.node)) {\n        exportVariable(path, returnedValue.current!, exportedName.current!);\n        path.remove();\n      } else if (hmdMatcher.match(path.node)) {\n        path.remove();\n      }\n    },\n  });\n}\n\nfunction exportVariable(\n  requireDPath: NodePath,\n  value: t.Expression,\n  exportName: string,\n) {\n  if (value.type === 'Identifier') {\n    const binding = requireDPath.scope.getBinding(value.name);\n    if (!binding) return;\n\n    const declaration = findPath(\n      binding.path,\n      m.or(\n        m.variableDeclaration(),\n        m.classDeclaration(),\n        m.functionDeclaration(),\n      ),\n    );\n    if (!declaration) return;\n\n    if (exportName === 'default') {\n      // `let f = 1;` -> `export default 1;`\n      declaration.replaceWith(\n        t.exportDefaultDeclaration(\n          t.isVariableDeclaration(declaration.node)\n            ? declaration.node.declarations[0].init!\n            : declaration.node,\n        ),\n      );\n    } else {\n      // `let f = 1;` -> `export let counter = 1;`\n      renameFast(binding, exportName);\n      declaration.replaceWith(t.exportNamedDeclaration(declaration.node));\n    }\n  } else if (exportName === 'default') {\n    requireDPath.insertAfter(t.exportDefaultDeclaration(value));\n  } else {\n    requireDPath.insertAfter(\n      buildNamedExportLet({ NAME: t.identifier(exportName), VALUE: value }),\n    );\n  }\n}\n", "import { expression } from '@babel/template';\nimport type { NodePath } from '@babel/traverse';\nimport traverse from '@babel/traverse';\nimport * as m from '@codemod/matchers';\nimport { constMemberExpression } from '../../ast-utils';\nimport type { WebpackBundle } from './bundle';\n\n/*\n * webpack/runtime/compat get default export\n * getDefaultExport function for compatibility with non-harmony modules\n * ```js\n * __webpack_require__.n = (module) => {\n * \tvar getter = module && module.__esModule ?\n * \t\t() => (module['default']) :\n * \t\t() => (module);\n * \t__webpack_require__.d(getter, { a: getter });\n * \treturn getter;\n * };\n * ```\n */\n\n/**\n * Convert require.n calls to require the default export depending on the target module type\n * ```js\n * const m = require(1);\n * const getter = require.n(m);\n * console.log(getter.a.prop, getter().prop);\n * ```\n * ->\n * ```js\n * const m = require(1);\n * console.log(m.prop, m.prop);\n * ```\n */\nexport function convertDefaultRequire(bundle: WebpackBundle): void {\n  function getRequiredModule(path: NodePath) {\n    // The variable that's passed to require.n\n    const binding = path.scope.getBinding(moduleArg.current!.name);\n    const declarator = binding?.path.node;\n    if (declaratorMatcher.match(declarator)) {\n      return bundle.modules.get(requiredModuleId.current!.value.toString());\n    }\n  }\n\n  const requiredModuleId = m.capture(m.numericLiteral());\n  // E.g. const m = require(1);\n  const declaratorMatcher = m.variableDeclarator(\n    m.identifier(),\n    m.callExpression(m.identifier('require'), [requiredModuleId]),\n  );\n\n  // E.g. m\n  const moduleArg = m.capture(m.identifier());\n  // E.g. getter\n  const getterVarName = m.capture(m.identifier());\n  // E.g. require.n(m)\n  const requireN = m.callExpression(constMemberExpression('require', 'n'), [\n    moduleArg,\n  ]);\n  // E.g. const getter = require.n(m)\n  const defaultRequireMatcher = m.variableDeclarator(getterVarName, requireN);\n\n  // E.g. require.n(m).a or require.n(m)()\n  const defaultRequireMatcherAlternative = m.or(\n    constMemberExpression(requireN, 'a'),\n    m.callExpression(requireN, []),\n  );\n\n  const buildDefaultAccess = expression`OBJECT.default`;\n\n  bundle.modules.forEach((module) => {\n    traverse(module.ast, {\n      'CallExpression|MemberExpression'(path) {\n        if (defaultRequireMatcherAlternative.match(path.node)) {\n          // Replace require.n(m).a or require.n(m)() with m or m.default\n          const requiredModule = getRequiredModule(path);\n          if (requiredModule?.ast.program.sourceType === 'module') {\n            path.replaceWith(\n              buildDefaultAccess({ OBJECT: moduleArg.current! }),\n            );\n          } else {\n            path.replaceWith(moduleArg.current!);\n          }\n        }\n      },\n      VariableDeclarator(path) {\n        if (defaultRequireMatcher.match(path.node)) {\n          // Replace require.n(m); with m or m.default\n          const requiredModule = getRequiredModule(path);\n          const init = path.get('init');\n          if (requiredModule?.ast.program.sourceType === 'module') {\n            init.replaceWith(\n              buildDefaultAccess({ OBJECT: moduleArg.current! }),\n            );\n          } else {\n            init.replaceWith(moduleArg.current!);\n          }\n\n          // Replace getter.a.prop and getter().prop with getter.prop\n          const binding = path.scope.getOwnBinding(getterVarName.current!.name);\n          binding?.referencePaths.forEach((refPath) => {\n            if (\n              refPath.parentPath?.isCallExpression() ||\n              refPath.parentPath?.isMemberExpression()\n            ) {\n              refPath.parentPath.replaceWith(refPath);\n            }\n          });\n        }\n      },\n      noScope: true,\n    });\n  });\n}\n", "import { statement } from '@babel/template';\nimport type { Statement } from '@babel/types';\nimport * as m from '@codemod/matchers';\nimport { constMemberExpression } from '../../ast-utils';\nimport type { WebpackModule } from './module';\n\nconst buildVar = statement`var NAME = INIT;`;\n\n/**\n * ```js\n * (function(global) {\n *   // ...\n * }.call(exports, require(7)))\n * ```\n * ->\n * ```js\n * var global = require(7);\n * // ...\n * ```\n */\nexport function inlineVarInjections(module: WebpackModule): void {\n  const { program } = module.ast;\n  const newBody: Statement[] = [];\n\n  const body = m.capture(m.blockStatement());\n  const params = m.capture(m.arrayOf(m.identifier()));\n  const args = m.capture(\n    m.anyList(m.or(m.thisExpression(), m.identifier('exports')), m.oneOrMore()),\n  );\n  const matcher = m.expressionStatement(\n    m.callExpression(\n      constMemberExpression(\n        m.functionExpression(undefined, params, body),\n        'call',\n      ),\n      args,\n    ),\n  );\n\n  for (const node of program.body) {\n    if (matcher.match(node)) {\n      const vars = params.current!.map((param, i) =>\n        buildVar({ NAME: param, INIT: args.current![i + 1] }),\n      );\n      newBody.push(...vars);\n      newBody.push(...body.current!.body);\n      // We can skip replacing uses of `this` because it always refers to the exports\n    } else {\n      newBody.push(node);\n    }\n  }\n  program.body = newBody;\n}\n", "import { Module } from '../module';\n\nexport class WebpackModule extends Module {}\n", "export function isBrowser(): boolean {\n  return typeof window !== 'undefined' || typeof importScripts !== 'undefined';\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AACA,SAAS,SAAAA,cAAa;AAGtB,YAAYC,SAAO;AACnB,OAAOC,YAAW;AAClB,SAAS,QAAAC,OAAM,aAAAC,kBAAiB;;;ACNhC,YAAY,OAAO;AAEZ,SAAS,YAAY,MAAkC;AAC5D,MAAM,eAAa,IAAI,GAAG;AACxB,WAAO,KAAK;AAAA,EACd;AACA,MAAM,kBAAgB,IAAI,GAAG;AAC3B,WAAO,KAAK;AAAA,EACd;AACA,MAAM,mBAAiB,IAAI,GAAG;AAC5B,WAAO,KAAK,MAAM,SAAS;AAAA,EAC7B;AACF;;;ACZA;AAAA;AAAA;AAAA;AAEU;AAFV,OAAO,YAAY;AAET,0BAAc;AADd,IAAO,oBAAQ,OAAO,WAAW;;;ACG3C,IAAM,iBAAmC,EAAE,aAAa,EAAE,SAAS,KAAK,EAAE;AAEnE,SAAS,SACd,KACA,UAA4B,gBACpB;AACR,SAAO,kBAAc,KAAK,OAAO,EAAE;AACrC;AAEO,SAAS,YAAY,MAAsB;AAChD,QAAM,OAAO,SAAS,MAAM;AAAA,IAC1B,UAAU;AAAA,IACV,oBAAoB,MAAM;AAAA,IAC1B,GAAG;AAAA,EACL,CAAC;AACD,MAAI,KAAK,SAAS,KAAK;AACrB,WAAO,KAAK,MAAM,GAAG,EAAE,IAAI,aAAQ,KAAK,MAAM,GAAG;AAAA,EACnD;AACA,SAAO;AACT;;;ACvBA;AAAA;AAAA;AAAA;AAEU,6BAAAC;AAFV,OAAOC,aAAY;AAET,YAAAD,eAAc;AADd,IAAO,mBAAQC,QAAO,WAAWA;;;ACC3C,YAAYC,QAAO;AACnB,YAAYC,QAAO;;;ACFnB,YAAYC,QAAO;AACnB,YAAY,OAAO;AAKZ,IAAM,cAAsC;AAAA,EACjD,CAAC,SACG,aAAU,IAAI,MACf,CAAG,qBAAkB,IAAI,KAAK,KAAK,YAAY,WAAW;AAC/D;AAEO,SAAS,aACd,MAC8C;AAC9C,SAAS;AAAA,IACL,eAAa,QAAW,MAAM,QAAW,IAAI;AAAA,IAC7C,eAAa,QAAW,eAAe,QAAW,IAAI;AAAA,IACtD,iBAAe,eAAe,IAAI;AAAA,EACtC;AACF;AAEO,SAAS,SACd,MAC2C;AAC3C,SAAS,KAAK,aAAW,IAAI,GAAK,gBAAc,IAAI,CAAC;AACvD;AAEO,SAAS,oBACd,OAC6B;AAC7B,SAAS;AAAA,IACL,iBAAiB,aAAW,GAAG,OAAO,KAAK;AAAA,IAC3C,iBAAiB,KAAK,gBAAc,GAAK,iBAAe,CAAC,GAAG,KAAK;AAAA,EACrE;AACF;AAEO,SAAS,kBACd,QAOA,MAC6D;AAC7D,SAAS;AAAA,IACL,qBAAmB,MAAM,QAAQ,MAAM,KAAK;AAAA,IAC5C,0BAAwB,QAAQ,IAAI;AAAA,EACxC;AACF;AAEO,SAAS,KACd,QAOA,MAC6B;AAC7B,SAAS,iBAAe,kBAAkB,QAAQ,IAAI,CAAC;AACzD;AAKO,SAAS,sBACd,QACA,UAC+B;AAC/B,MAAI,OAAO,WAAW,SAAU,UAAW,aAAW,MAAM;AAC5D,SAAS;AAAA,IACL,mBAAiB,QAAU,aAAW,QAAQ,GAAG,KAAK;AAAA,IACtD,mBAAiB,QAAU,gBAAc,QAAQ,GAAG,IAAI;AAAA,EAC5D;AACF;AAEO,IAAM,mBAAqB;AAAA,EAC9B,aAAW,WAAW;AAAA,EACtB,kBAAgB,QAAU,iBAAe,CAAC,CAAC;AAC/C;AAEO,IAAM,cAAgB;AAAA,EACzB,iBAAe,IAAI;AAAA,EACnB,kBAAgB,KAAO,iBAAe,CAAC,CAAC;AAAA,EACxC,kBAAgB,KAAO,kBAAgB,KAAO,iBAAe,CAAC,CAAC,CAAC;AAAA,EAChE,kBAAgB,KAAO,kBAAgB,KAAO,kBAAgB,CAAC,CAAC,CAAC,CAAC;AACtE;AAEO,IAAM,eAAiB;AAAA,EAC1B,iBAAe,KAAK;AAAA,EACpB,kBAAgB,KAAO,kBAAgB,CAAC,CAAC,CAAC;AAC9C;AAEO,IAAM,gBAAkB,KAAG,aAAe,kBAAgB,CAAC,CAAC,CAAC;AAO7D,SAAS,WACd,MACAC,WACoB;AACpB,SAAO,KAAK;AAAA,IAAW,CAACC,UACtBD,UAAQ,MAAMC,MAAK,IAAI;AAAA,EACzB;AACF;AAOO,SAAS,SACd,MACAD,WACoB;AACpB,SAAO,KAAK,KAAK,CAACC,UAASD,UAAQ,MAAMC,MAAK,IAAI,CAAC;AACrD;AAMO,SAAS,sBACd,QACA,MAGiC;AACjC,QAAM,WAAW,MAAM;AAAA,IAAK,EAAE,QAAQ,OAAO;AAAA,IAAG,MAC5C,UAAU,YAAU,CAAC;AAAA,EACzB;AAEA,SAAS;AAAA,IACP;AAAA,IACA,SAAS,IAAM,YAAU;AAAA,IACvB;AAAA,MACA,KAAK,GAAG,SAAS,IAAI,CAAC,MAAQ,aAAa,cAAY,CAAC,CAAC,CAAC,CAAC;AAAA,IAC7D;AAAA,EACF;AACF;AAKO,SAAS,iBACd,SACA,cACS;AAET,MAAI,CAAC,QAAQ,YAAY,QAAQ,mBAAmB,CAAC,MAAM,QAAQ;AACjE,WAAO;AAET,WAAS,oBAAoB,QAA0B;AACrD;AAAA;AAAA,MAEE,OAAO,YAAY,eAAe;AAAA,MAElC,OAAO,YAAY,oBAAoB;AAAA,MAEvC,OAAO,YAAY,YAAY,gBAAgB;AAAA,MAE/C,OAAO,YAAY,oBAAoB;AAAA;AAAA,EAE3C;AAEA,SAAO,QAAQ,eAAe;AAAA,IAC5B,CAAC;AAAA;AAAA,MAEC,aAAa,MAAM,KAAK,MAAM;AAAA,MAE9B,CAAC,KAAK,YAAY,YAAY,uBAAuB;AAAA,QACnD,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,MAED,CAAC,KAAK,YAAY,YAAY,mBAAmB;AAAA,QAC/C,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,MAED,CAAC,KAAK,YAAY,YAAY,kBAAkB;AAAA,QAC9C,UAAU,KAAK;AAAA,QACf,UAAU;AAAA,MACZ,CAAC,KACD,CAAC,oBAAoB,KAAK,UAAW;AAAA;AAAA,EACzC;AACF;AAWO,SAAS,oBACd,SACA,YACA,OAAwB,OACJ;AACpB,SACE,YAAY,UACZ,QAAQ,eAAe,cACvB,QAAQ,mBAAmB,WAAW,MACrC,SAAS,QACN,QAAQ,KAAK,qBAAqB,KAAK,QAAQ,KAAK,KAAK,SAAS,OAClE,QAAQ,KAAK,YAAY,YAAY,QAAQ,KAAK,aAAa;AAEvE;;;ADvMO,SAAS,eACd,SACA,QAAU,iBAAc,GACxB,oBAAoB,OACpB;AACA,QAAM,gBAAgB,QAAQ,KAAK;AACnC,QAAM,aAAe;AAAA,IACjB,cAAW,QAAQ,WAAW,IAAI;AAAA,IACpC;AAAA,EACF;AACA,QAAM,oBAAsB;AAAA,IAC1B;AAAA,IACE,cAAW,QAAQ,WAAW,IAAI;AAAA,IACpC;AAAA,EACF;AAEA,MAAI,QAAQ,YAAY,WAAW,MAAM,aAAa,GAAG;AACvD,YAAQ,eAAe,QAAQ,CAAC,QAAQ;AACtC,UAAI,YAAY,cAAc,IAAK;AAAA,IACrC,CAAC;AACD,YAAQ,KAAK,OAAO;AAAA,EACtB,WAAW,qBAAqB,QAAQ,mBAAmB,UAAU,GAAG;AAMtE,QAASC,wBAAT,SAA8B,UAAkB;AAC9C,aAAO,YAAY,SAAS,CAAC,eAAe,WAAW,QAAS,QAAQ;AAAA,IAC1E;AAFS,+BAAAA;AALT,UAAM,cAAc,QAAQ,mBACzB,IAAI,CAAC,SAAS,KAAK,IAAI,EACvB,OAAO,CAAC,SAAS,kBAAkB,MAAM,IAAI,CAAC;AACjD,QAAI,CAAC,YAAY,OAAQ;AAMzB,eAAW,OAAO,QAAQ,gBAAgB;AACxC,YAAM,aAAaA,sBAAqB,IAAI,KAAK,KAAM;AACvD,UAAI,WAAY,KAAI,YAAY,WAAW,KAAK;AAAA,IAClD;AAEA,eAAW,QAAQ,QAAQ,oBAAoB;AAC7C,UAAI,KAAK,YAAY,sBAAsB,GAAG;AAC5C,aAAK,OAAO;AAAA,MACd,WAAW,KAAK,uBAAuB,GAAG;AACxC,aAAK,YAAY,KAAK,KAAK,KAAK;AAAA,MAClC;AAAA,IACF;AAEA,YAAQ,KAAK,OAAO;AAAA,EACtB;AACF;AAQO,SAAS,oBACd,OACA,YACM;AACN,aAAW,aAAa,YAAY;AAClC,UAAM,aAAa,UAAU;AAC7B,UAAM,WAAW,WAAW,KAAK;AACjC,UAAM,QAAQ,SAAS;AACvB,UAAM,cAAc,MAAM,SAAS,KAAK;AACxC,eAAW,YAAc,aAAU,WAAW,CAAC;AAAA,EACjD;AACF;AAEO,SAAS,uBACd,SACA,WAAa,kBAAe,GACtB;AACN,QAAM,gBAAgB,QAAQ,KAAK;AACnC,QAAM,mBAAqB,WAAU,WAAQ,QAAQ,CAAC;AACtD,QAAM,aAAe;AAAA,IACjB,cAAW,QAAQ,WAAW,IAAI;AAAA,IAClC,oBAAiB,gBAAgB;AAAA,EACrC;AACA,MAAI,CAAC,WAAW,MAAM,aAAa,EAAG;AAEtC,QAAM,cAAc,IAAI;AAAA,IACtB,iBAAiB,QAAS,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,GAAG,GAAG,EAAE,KAAK,CAAC;AAAA,EACpE;AACA,MACE,CAAC,QAAQ,eAAe,MAAM,CAAC,QAAQ;AACrC,UAAM,SAAS,IAAI;AACnB,UAAM,WAAW,YAAY,OAAO,QAAQ;AAC5C,WAAO,YAAY,IAAI,QAAQ;AAAA,EACjC,CAAC;AAED;AAEF,UAAQ,eAAe,QAAQ,CAAC,QAAQ;AACtC,UAAM,aAAa,IAAI;AACvB,UAAM,WAAW,YAAY,WAAW,KAAK,QAAQ;AACrD,UAAM,QAAQ,YAAY,IAAI,QAAQ;AAEtC,eAAW,YAAY,KAAK;AAAA,EAC9B,CAAC;AAED,UAAQ,KAAK,OAAO;AACtB;AAUO,SAAS,eACd,IACA,QACM;AACN,MAAM,iBAAc,GAAG,OAAO,CAAC,CAAC,GAAG;AACjC,WAAO;AAAA,MACH;AAAA,QACA,OAAO,KAAK,UAAU,CAAC;AAAA,QACvB,OAAO,KAAK,UAAU,MAAM,CAAC;AAAA,MAC/B;AAAA,IACF;AACA;AAAA,EACF;AAEA,QAAM,gBAAiB,GAAG,KAAK,KAAK,CAAC,EAAwB;AAC7D,QAAM,QAAU,aAAU,eAAe,IAAI;AAG7C,mBAAS,OAAO;AAAA,IACd,WAAW,MAAM;AACf,YAAM,aAAa,GAAG,OAAO;AAAA,QAC3B,CAAC,MAAO,EAAmB,SAAS,KAAK,KAAK;AAAA,MAChD;AACA,UAAI,eAAe,IAAI;AACrB,aAAK,YAAY,OAAO,KAAK,UAAU,UAAU,CAAC;AAClD,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAAA,IACA,SAAS;AAAA,EACX,CAAC;AAED,SAAO,YAAY,KAAK;AAC1B;AAQO,SAAS,sBAAsB,SAAuC;AAC3E,QAAM,QAAQ,EAAE,SAAS,EAAE;AAC3B,QAAM,OAAO,CAAC,GAAG,QAAQ,cAAc;AACvC,aAAW,OAAO,MAAM;AACtB,UAAM,KAAK,WAAW,KAAO,uBAAoB,CAAC;AAGlD,UAAM,SAAW,WAAU,aAAU,CAAC;AAEtC,UAAM,eAAiB;AAAA,MACnB;AAAA,QACE,cAAW,QAAQ,WAAW,IAAI;AAAA,QAClC,WAAU,SAAM,EAAE,KAAK,EAAE,CAAC,CAAC;AAAA,MAC/B;AAAA,IACF;AACA,UAAMC,YAAY;AAAA,MACd,cAAW,MAAM;AAAA,MACjB,WAAU,SAAM,EAAE,KAAK,EAAE,CAAC,CAAC;AAAA,MAC3B,kBAAe,CAAG,mBAAgB,YAAY,CAAC,CAAC;AAAA,IACpD;AAEA,QAAI,MAAMA,UAAQ,MAAM,GAAG,IAAI,GAAG;AAGhC,YAAM,wBAAwB,GAAG,KAAK,OAAO,KAAK,CAAC,UAAU;AAC3D,cAAMC,WAAU,GAAG,MAAM,WAAY,MAAuB,IAAI;AAChE,eAAOA,UAAS,eAAe;AAAA,UAAK,CAACC,SACnCA,KAAI,WAAW,CAAC,MAAM,EAAE,SAAS,aAAa,OAAO;AAAA,QACvD;AAAA,MACF,CAAC;AACD,UAAI,CAAC,sBAAuB;AAE5B,YAAM,YAAY,GAAG,MAAM,OAAO,WAAW,OAAO,OAAQ;AAC5D,UAAI,CAAC,UAAW;AAEhB,YAAM,SAAS,UAAU;AACzB,WAAK,KAAK,GAAG,MAAM;AAGnB,YAAM,WAAW,OACd;AAAA,QACC,CAACA,SACG,oBAAiBA,KAAI,MAAM,KAC3B,gBAAaA,KAAI,OAAO,QAAQ,EAAE,MAAM,OAAO,QAAS,CAAC;AAAA,MAC/D,EACC,IAAI,CAACA,SAAQA,KAAI,UAAW;AAE/B,iBAAW,WAAW,UAAU;AAC9B,uBAAe,GAAG,MAAM,OAAO;AAC/B,cAAM;AAAA,MACR;AAEA,SAAG,OAAO;AACV,YAAM;AAAA,IACR;AAAA,EACF;AAGA,UAAQ,MAAM,MAAM;AACpB,SAAO;AACT;AASO,SAAS,sBACd,SACA,aAAa,QAAQ,WAAW,MACX;AACrB,QAAM,QAAQ,EAAE,SAAS,EAAE;AAC3B,QAAM,OAAO,CAAC,GAAG,QAAQ,cAAc;AACvC,QAAM,UAAY,WAAU,aAAU,CAAC;AACvC,QAAMF,YAAY;AAAA,IACd;AAAA,MACE,cAAW,OAAO;AAAA,MAClB,cAAW,QAAQ,WAAW,IAAI;AAAA,IACtC;AAAA,IACE;AAAA,MACA;AAAA,MACE,cAAW,OAAO;AAAA,MAClB,cAAW,QAAQ,WAAW,IAAI;AAAA,IACtC;AAAA,EACF;AAEA,aAAW,OAAO,MAAM;AACtB,QAAIA,UAAQ,MAAM,IAAI,MAAM,GAAG;AAC7B,YAAM,WAAW,IAAI;AACrB,YAAM,aAAa,SAAS,WAAW,QAAQ,OAAQ;AACvD,UAAI,CAAC,WAAY;AAEjB,UAAI,IAAI,aAAa,EAAE,MAAM,WAAW,WAAW,KAAK,CAAC,EAAG;AAG5D,YAAM,WAAW,sBAAsB,YAAY,UAAU,EAAE;AAE/D,UAAI,IAAI,YAAY,uBAAuB,GAAG;AAE5C,mBAAW,KAAK,OAAO;AAEvB,YAAM,yBAAsB,IAAI,WAAW,MAAM,GAAG;AAElD,cAAI,WAAW,OAAO;AAAA,QACxB,OAAO;AAEL,cAAI,WAAW,YAAc,cAAW,UAAU,CAAC;AAAA,QACrD;AAAA,MACF,WAAW,IAAI,YAAY,qBAAqB,GAAG;AAEjD,YAAI,WAAW,OAAO;AAAA,MACxB;AACA,YAAM;AAAA,IACR,OAAO;AAEL,UAAI,YAAc,cAAW,UAAU,CAAC;AACxC,YAAM;AAAA,IACR;AAAA,EACF;AAEA,SAAO;AACT;;;AEhSA,YAAYG,QAAO;AACnB,YAAYC,QAAO;AAGZ,SAAS,WAAW,SAAkB,SAAuB;AAClE,UAAQ,eAAe,QAAQ,CAAC,QAAQ;AACtC,QAAI,IAAI,2BAA2B,EAAG;AACtC,QAAI,CAAC,IAAI,aAAa,GAAG;AACvB,YAAM,IAAI;AAAA,QACR,yBAAyB,IAAI,IAAI,MAAM,YAAY,IAAI,IAAI,CAAC;AAAA,MAC9D;AAAA,IACF;AAGA,QAAI,IAAI,MAAM,WAAW,OAAO,EAAG,KAAI,MAAM,OAAO,OAAO;AAC3D,QAAI,KAAK,OAAO;AAAA,EAClB,CAAC;AAGD,QAAM,iBAAmB;AAAA,IACvB;AAAA,IACE,MAAK,gBAAa,GAAK,iBAAc,CAAC;AAAA,EAC1C;AACA,UAAQ,mBAAmB,QAAQ,CAAC,QAAQ;AAE1C,QAAI,IAAI,MAAM,WAAW,OAAO,EAAG,KAAI,MAAM,OAAO,OAAO;AAE3D,QAAI,IAAI,uBAAuB,KAAO,gBAAa,IAAI,KAAK,IAAI,GAAG;AACjE,UAAI,KAAK,KAAK,OAAO;AAAA,IACvB,WAAW,IAAI,mBAAmB,KAAO,gBAAa,IAAI,KAAK,QAAQ,GAAG;AACxE,UAAI,KAAK,SAAS,OAAO;AAAA,IAC3B,WACE,IAAI,kBAAkB,EAAE,UAAU,SAAS,CAAC,KAC1C,gBAAa,IAAI,KAAK,QAAQ,GAChC;AACA,UAAI,KAAK,SAAS,OAAO;AAAA,IAC3B,WAAW,IAAI,qBAAqB,KAAO,gBAAa,IAAI,KAAK,EAAE,GAAG;AACpE,UAAI,KAAK,GAAG,OAAO;AAAA,IACrB,WAAW,IAAI,qBAAqB,KAAO,kBAAe,IAAI,KAAK,EAAE,GAAG;AACtE,YAAM,MAAM,IAAI,sBAAsB;AACtC,iBAAW,MAAM,KAAK;AACpB,YAAI,OAAO,QAAQ,WAAW,MAAM;AAClC,cAAI,EAAE,EAAE,OAAO;AAAA,QACjB;AAAA,MACF;AAAA,IACF,WAAW,IAAI,MAAM,KAAK,eAAe,MAAM,IAAI,IAAI,GAAG;AACxD,uBAAS,IAAI,MAAM;AAAA,QACjB,WAAW,MAAM;AACf,cAAI,KAAK,UAAU,IAAI,MAAO,QAAO,KAAK,KAAK;AAC/C,cAAI,KAAK,KAAK,SAAS,QAAQ,WAAW,MAAM;AAC9C,iBAAK,KAAK,OAAO;AAAA,UACnB;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AAAA,IACH,WAAW,IAAI,sBAAsB,KAAO,gBAAa,IAAI,KAAK,EAAE,GAAG;AACrE,UAAI,KAAK,GAAG,OAAO;AAAA,IACrB,OAAO;AACL,YAAM,IAAI;AAAA,QACR,kCAAkC,IAAI,IAAI,MAAM,YAAY,IAAI,IAAI,CAAC;AAAA,MACvE;AAAA,IACF;AAAA,EACF,CAAC;AAED,UAAQ,MAAM,iBAAiB,QAAQ,WAAW,IAAI;AACtD,UAAQ,MAAM,SAAS,OAAO,IAAI;AAClC,UAAQ,WAAW,OAAO;AAC5B;AAEO,SAAS,iBACd,MACA,UACM;AACN,QAAM,SAAS,KAAK,KAAK;AACzB,WAAS,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,QAAQ,SAAS,MAAM,GAAG,KAAK;AACjE,UAAM,UAAU,KAAK,MAAM,WAAW,OAAO,CAAC,EAAE,IAAI;AACpD,eAAW,SAAS,SAAS,CAAC,CAAC;AAAA,EACjC;AACF;;;AC9EA,OAAO,WAAW;AAElB,IAAM,SAAS,MAAM,qBAAqB;AAE1C,eAAsB,oBACpB,KACA,WACA,SACyB;AACzB,SAAO,GAAG,UAAU,IAAI,WAAW;AACnC,QAAM,QAAwB,EAAE,SAAS,EAAE;AAE3C,QAAM,UAAU,MAAM,KAAK,OAAO,OAAO;AACzC,MAAI,UAAU;AACZ,qBAAS,KAAK,UAAU,QAAQ,OAAO,GAAG,QAAW,KAAK;AAE5D,SAAO,GAAG,UAAU,IAAI,mBAAmB,MAAM,OAAO,UAAU;AAClE,SAAO;AACT;AAEO,SAAS,eACd,KACA,WACA,SACgB;AAChB,SAAO,GAAG,UAAU,IAAI,WAAW;AACnC,QAAM,QAAwB,EAAE,SAAS,EAAE;AAC3C,YAAU,MAAM,KAAK,OAAO,OAAO;AAEnC,MAAI,UAAU,SAAS;AACrB,UAAM,UAAU,UAAU;AAAA,MACxB;AAAA,IACF;AACA,YAAQ,UAAU,CAAC,UAAU;AAC7B,qBAAS,KAAK,SAAS,QAAW,KAAK;AAAA,EACzC;AAEA,SAAO,GAAG,UAAU,IAAI,mBAAmB,MAAM,OAAO,UAAU;AAClE,SAAO;AACT;AAEO,SAAS,gBACd,KACA,YACA,UAA+D,CAAC,GAChD;AAChB,UAAQ,QAAQ;AAChB,QAAM,OAAO,QAAQ,QAAQ,WAAW,IAAI,CAACC,QAAMA,IAAE,IAAI,EAAE,KAAK,IAAI;AACpE,MAAI,QAAQ,IAAK,QAAO,GAAG,IAAI,WAAW;AAC1C,QAAM,QAAwB,EAAE,SAAS,EAAE;AAE3C,aAAW,aAAa,YAAY;AAClC,cAAU,MAAM,KAAK,KAAK;AAAA,EAC5B;AAEA,QAAM,kBAAkB,WAAW,QAAQ,CAACA,QAAMA,IAAE,UAAU,KAAK,CAAC,CAAC;AACrE,MAAI,gBAAgB,SAAS,GAAG;AAC9B,UAAM,UACJ,0BAAS,MAAM,eAAe;AAChC,YAAQ,UAAU,QAAQ,WAAW,WAAW,MAAM,CAACA,QAAM,CAACA,IAAE,KAAK;AACrE,qBAAS,KAAK,SAAS,QAAW,KAAK;AAAA,EACzC;AAEA,MAAI,QAAQ,IAAK,QAAO,GAAG,IAAI,mBAAmB,MAAM,OAAO,UAAU;AACzE,SAAO;AACT;AAEO,SAAS,gBAAgB,SAIlB;AACZ,SAAO;AAAA,IACL,MAAM,QAAQ;AAAA,IACd,MAAM,QAAQ;AAAA,IACd,OAAO,QAAQ,WAAW,KAAK,CAACA,QAAMA,IAAE,KAAK;AAAA,IAC7C,UAAU;AACR,aAAO,0BAAS;AAAA,QACd,QAAQ,WAAW,QAAQ,CAACA,QAAMA,IAAE,UAAU,KAAK,CAAC,CAAC;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AACF;;;ACpFA,OAAOC,YAAW;;;ACAlB,YAAYC,QAAO;AAMnB,IAAO,wBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,UAAU;AACR,UAAM,OAAS,WAAU,iBAAc,CAAC;AACxC,UAAM,QAAU,WAAU,iBAAc,CAAC;AAEzC,UAAMC,YAAY;AAAA,MAChB;AAAA,MACE,MAAG,MAAQ,oBAAiB,KAAO,YAAS,GAAG,IAAI,CAAC;AAAA,MACtD;AAAA,IACF;AAEA,WAAO;AAAA,MACL,kBAAkB;AAAA,QAChB,KAAK,MAAM;AACT,cAAI,CAACA,UAAQ,MAAM,KAAK,IAAI,EAAG;AAC/B,eAAK,QAAS,SAAS,MAAM,QAAS;AACtC,gBAAM,QAAS,QAAQ;AACvB,eAAK,YAAY,KAAK,KAAK,IAAI;AAC/B,eAAK,KAAK;AACV,eAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC9BA,YAAYC,QAAO;AACnB,SAAS,kBAAAC,uBAAsB;AAuBxB,SAAS,iBACd,aAC0B;AAE1B,QAAM,kBAAoB,WAAU,cAAW,CAAC;AAGhD,QAAM,YAAc;AAAA,IAClB,sBAAsB,iBAAiB,MAAM;AAAA,IAC7C;AAAA,MACI;AAAA,QACA,sBAAwB,eAAY,eAAe,GAAG,OAAO;AAAA,MAC/D;AAAA,IACF;AAAA,EACF;AAEA,QAAM,cAAc;AAAA,IAChB,YAAS;AAAA,IACT;AAAA,MACE;AAAA,QACE,cAAW;AAAA,QACb;AAAA,UACI,WAAQ,CAAC,SAAS;AAClB,mBAEK,eAAYC,gBAAiB,cAAW,UAAU,CAAC,CAAC,EACpD,MAAM,IAAI,KAEV,kBAAe;AAAA,cACZ;AAAA,gBACE,eAAY,SAAS;AAAA,gBACrB,eAAY,SAAS;AAAA,cACzB;AAAA,YACF,CAAC,EACA,MAAM,IAAI;AAAA,UAEjB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAMC,YAAY;AAAA,IACd,MAAG,aAAe,mBAAgB,KAAK,WAAW,CAAC;AAAA,EACvD;AAEA,aAAW,OAAO,YAAY,YAAY;AACxC,UAAM,UAAU,WAAW,KAAKA,SAAO;AACvC,QAAI,SAAS;AACX,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;AC5EA,YAAYC,QAAO;AACnB,YAAYC,QAAO;AAkBnB,IAAO,8BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AACR,UAAM,QAAU,WAAU,cAAW,CAAC;AACtC,UAAM,eAAiB,WAAgB,CAAC,SAAS,cAAc,KAAK,IAAI,CAAC;AACzE,UAAM,cAAc,SAAS,YAAY;AACzC,UAAM,gBAAkB;AAAA;AAAA,MAEpB,iBAAc;AAAA;AAAA,MAEhB,sBAAsB,GAAG,CAAC,MAAM,UAAU;AAAA,QACtC;AAAA,UACE;AAAA,YACE,oBAAiB,QAAW,MAAM,KAAK;AAAA,YACvC,qBAAkB,QAAW,MAAM,KAAK;AAAA,YACxC,oBAAiB,QAAW,OAAO,IAAI;AAAA,YACvC,qBAAkB,QAAW,OAAO,IAAI;AAAA,UAC5C;AAAA,QACF;AAAA,MACF,CAAC;AAAA;AAAA,MAEC,WAA4B,CAAC,SAAS;AACtC,eACI,wBAAqB,IAAI,KAC3B,sBAAsB,KAAK,OAAO,QAAQ,IAAI,WAAW;AAAA,UACrD,mBAAkB,kBAAe,OAAO,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC,CAAC;AAAA,QAChE,CAAC,EAAE,MAAM,IAAI;AAAA,MAEjB,CAAC;AAAA;AAAA,OAEA,MAAM;AACL,cAAM,SAAW,WAAU,cAAW,CAAC;AACvC,cAAM,WAAa,WAAU,cAAW,CAAC;AAEzC,eAAS;AAAA,UACP;AAAA,UACA,CAAC,QAAU,eAAY,QAAQ,CAAC;AAAA,UAC9B,kBAAe;AAAA,YACb;AAAA,cACE,kBAAiB,eAAY,MAAM,GAAG;AAAA,gBACpC,iBAAgB,eAAY,QAAQ,CAAC;AAAA,cACzC,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,GAAG;AAAA,IACL;AAEA,UAAM,mBAAqB;AAAA,MACvB,WAAU,kBAAe,aAAa,aAAa,CAAC;AAAA,IACxD;AACA,UAAM,UAAY,WAAU,cAAW,CAAC;AACxC,UAAM,WAAa,uBAAsB,YAAS,GAAG;AAAA,MACjD,sBAAmB,SAAW,eAAY,KAAK,CAAC;AAAA,IACpD,CAAC;AAED,UAAM,cAAgB,WAAQ,YAAY;AAE1C,UAAM,gBAAkB,WAAQ,aAAa;AAE7C,UAAM,aAAe;AAAA,MACjB;AAAA,QACA;AAAA,QACA,sBAAwB,eAAY,KAAK,GAAG,WAAW;AAAA,QACvD;AAAA,MACF;AAAA,IACF;AACA,UAAM,kBAAoB;AAAA,MACtB;AAAA,QACA;AAAA,QACA,sBAAwB,eAAY,KAAK,GAAG,WAAW;AAAA,MACzD;AAAA,IACF;AAEA,UAAM,eAAe;AAAA,MACjB,MAAK,eAAY,KAAK,GAAK,eAAY,OAAO,CAAC;AAAA,MACjD;AAAA,IACF;AACA,UAAM,aAAe;AAAA,MACnB;AAAA,MACE,oBAAiB,gBAAgB;AAAA,IACrC;AAEA,UAAM,gBAAgB;AAAA,MAClB,oBAAiB,gBAAgB;AAAA,MACnC;AAAA,IACF;AAEA,aAAS,kBAAkB,SAAkB;AAE3C,aAAO,QAAQ,YAAY,QAAQ,mBAAmB,CAAC,MAAM,QAAQ;AAAA,IACvE;AAEA,aAAS,UAAU,MAAsC;AACvD,UAAI,UAAU;AACd,UAAI,WAAW,MAAM,KAAK,IAAI,GAAG;AAG/B,cAAM,UAAU,KAAK,MAAM,WAAW,MAAM,QAAS,IAAI;AACzD,YAAI,CAAC,QAAS,QAAO;AACrB,YAAI,CAAC,kBAAkB,OAAO,EAAG,QAAO;AACxC,YAAI,CAAC,oBAAoB,OAAO,EAAG,QAAO;AAC1C,YAAI,CAAC,iBAAiB,SAAS,YAAY,EAAG,QAAO;AAErD,cAAM,QAAQ,IAAI;AAAA,UAChB,iBAAiB,QAAS,IAAI,CAAC,MAAM;AAAA,YACnC,YAAY,EAAE,GAAG;AAAA,YACjB,EAAE;AAAA,UACJ,CAAC;AAAA,QACH;AACA,YAAI,CAAC,MAAM,KAAM,QAAO;AAExB,cAAM,UAAU,CAAC,GAAG,QAAQ,cAAc;AAI1C,SAAC,GAAG,QAAQ,cAAc,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ;AACrD,gBAAM,aAAa,IAAI;AACvB,gBAAM,WAAW,YAAY,WAAW,KAAK,QAAQ;AACrD,gBAAM,QAAQ,MAAM,IAAI,QAAQ;AAChC,cAAI,CAAC,OAAO;AACV,gBAAI,WAAW,WAAW,oCAAoC;AAC9D;AAAA,UACF;AAEA,cAAM,mBAAgB,KAAK,GAAG;AAC5B,uBAAW,YAAY,KAAK;AAAA,UAC9B,OAAO;AACL;AAAA,cACE;AAAA,cACA,WAAW;AAAA,YACb;AAAA,UACF;AACA;AAAA,QACF,CAAC;AAED,gBAAQ,QAAQ,CAAC,QAAQ;AACvB,gBAAM,gBAAgB,WAAW,KAAO,sBAAmB,CAAC;AAC5D,cAAI,cAAe,YAAW,UAAU,aAAa;AAAA,QACvD,CAAC;AAED,aAAK,OAAO;AACZ;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAUA,aAAS,oBAAoB,YAA8B;AACzD,YAAM,YAAY,WAAW,KAAK,WAAY;AAC9C,YAAM,aAAc,WAAW,KAAK,WAAY,MAAiB;AACjE,YAAM,aAAiC,CAAC;AAExC,eAAS,IAAI,YAAY,IAAI,UAAU,QAAQ,KAAK;AAClD,cAAMC,aAAY,UAAU,CAAC;AAI7B,YAAI,gBAAgB,MAAMA,UAAS,GAAG;AACpC,yBAAeA,YAAW,qBAAY;AAAA,QACxC;AAEA,YAAI,WAAW,MAAMA,UAAS,GAAG;AAC/B,qBAAW;AAAA,YACP;AAAA,cACE,cAAW,YAAY,OAAQ;AAAA,cACjC,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF;AAGA,YAAM,kBAAkB,UAAU,aAAa,WAAW,MAAM;AAChE,UAAI,CAAC,SAAS,MAAM,eAAe,EAAG,QAAO;AAG7C,UAAI,WAAW,eAAe,WAAW,SAAS,EAAG,QAAO;AAE5D,YAAM,eAAe,WAAW,MAAM,WAAW,QAAQ,QAAS,IAAI;AACtE,UAAI,CAAC,iBAAiB,cAAc,YAAY,EAAG,QAAO;AAE1D,uBAAiB,QAAS,KAAK,GAAG,UAAU;AAC5C,gBAAU,OAAO,YAAY,WAAW,MAAM;AAC9C,iBAAW,iBAAiB,aAAa;AACzC,iBAAW,aAAa,aAAa;AACrC,iBAAW,WAAW,OAAO,aAAa,WAAW;AACrD,mBAAa,KAAK,OAAO;AACzB,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,MACL,oBAAoB;AAAA,QAClB,KAAK,MAAM;AACT,eAAK,WAAW,UAAU,IAAI;AAAA,QAChC;AAAA,MACF;AAAA,MACA,kBAAkB;AAAA,QAChB,KAAK,MAAM;AACT,cAAI,CAAC,cAAc,MAAM,KAAK,IAAI,EAAG;AAErC,gBAAM,WAAW,YAAY,KAAK,KAAK,QAAQ;AAC/C,gBAAM,QAAQ,iBAAiB,QAAS;AAAA,YACtC,CAAC,SAAS,YAAY,KAAK,GAAG,MAAM;AAAA,UACtC,GAAG;AACH,cAAI,CAAC,MAAO;AAEZ,cAAM,mBAAgB,KAAK,GAAG;AAC5B,iBAAK,YAAY,KAAK;AAAA,UACxB,WAAW,KAAK,WAAW,iBAAiB,GAAG;AAC7C,2BAAe,OAAO,KAAK,UAAU;AAAA,UACvC,OAAO;AACL,iBAAK,YAAY,KAAK;AAAA,UACxB;AACA,eAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC3PA,YAAYC,QAAO;AACnB,YAAYC,QAAO;AAInB,IAAO,8BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,UAAU;AACR,UAAM,eAAiB,WAAU,cAAW,CAAC;AAC7C,UAAM,iBAAmB;AAAA,MACrB,WAAgB,CAAC,MAAM,gBAAgB,KAAK,CAAC,CAAC;AAAA,IAClD;AACA,UAAM,WAAa,WAAU,cAAW,CAAC;AAEzC,UAAM,QAAU;AAAA,MACZ;AAAA,QACE;AAAA,UACE,iBAAgB,WAAQ,CAAC,MAAM,QAAQ,KAAK,CAAC,CAAC,CAAC;AAAA,UAC/C;AAAA,YACE,cAAW;AAAA,YACX,MAAK,qBAAkB,GAAK,mBAAgB,CAAC;AAAA,UACjD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,UAAMC,YAAY;AAAA,MACd;AAAA;AAAA,QAEE,uBAAoB,QAAW;AAAA,UAC7B;AAAA,YACA;AAAA,YACE;AAAA,cACA,sBAAwB,iBAAc,cAAc,GAAG,OAAO;AAAA,cAC9D,CAAG,iBAAc,GAAG,CAAC;AAAA,YACvB;AAAA,UACF;AAAA,QACF,CAAC;AAAA;AAAA,QAEC,uBAAoB,QAAW,CAAG,sBAAmB,QAAQ,CAAC,CAAC;AAAA,QACjE;AAAA,UACI,kBAAe;AAAA,YACb;AAAA;AAAA,cAEE;AAAA,gBACE,eAAY,YAAY;AAAA,gBACxB,oBAAiB,MAAQ,eAAY,QAAQ,CAAC;AAAA,gBAChD;AAAA,cACF;AAAA,cACA;AAAA,YACF;AAAA,YACE,kBAAe;AAAA,UACnB,CAAC;AAAA,QACH;AAAA,QACE,cAAW;AAAA,MACf;AAAA,IACF;AAEA,WAAO;AAAA,MACL,gBAAgB;AAAA,QACd,KAAK,MAAM;AACT,cAAI,CAACA,UAAQ,MAAM,KAAK,IAAI,EAAG;AAE/B,gBAAM,iBAAiB,IAAI;AAAA,YACzB,MAAM,QAAS,IAAI,CAAC,MAAM;AAAA,cACvB,EAAE,KAAyB;AAAA,cAC1B,uBAAoB,EAAE,WAAW,GAAG,EAAE,CAAC,IACrC,EAAE,WAAW,MAAM,GAAG,EAAE,IACxB,EAAE;AAAA,YACR,CAAC;AAAA,UACH;AAEA,gBAAM,WAAW,eAAe,QAAS,MAAM,GAAG;AAClD,gBAAM,gBAAgB,SAAS,QAAQ,CAAC,MAAM,eAAe,IAAI,CAAC,CAAE;AAEpE,eAAK,KAAK,KAAK,OAAO,GAAG,GAAG,GAAG,aAAa;AAC5C,eAAK,WAAW,cAAc,SAAS;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACjFA,YAAYC,QAAO;AACnB,YAAYC,QAAO;AAInB,IAAO,oBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,QAAQ;AAAA,EACf,OAAO;AAAA,EACP,UAAU;AACR,UAAM,mBAAqB;AAAA,MACvB,MAAG,OAAO,MAAM,OAAO,IAAI;AAAA,MAC3B,iBAAc;AAAA,MACd,iBAAc;AAAA,IAClB;AACA,UAAM,cAAgB;AAAA,MACpB;AAAA,MACE,mBAAgB,KAAK,gBAAgB;AAAA,IACzC;AAEA,WAAO;AAAA,MACL,qCAAqC;AAAA,QACnC,KAAK,OAAO;AACV,gBAAM,OAAO;AAIb,cAAI,CAAC,YAAY,MAAM,KAAK,KAAK,IAAI,EAAG;AAExC,cAAI,KAAK,IAAI,MAAM,EAAE,eAAe,GAAG;AACrC,oBAAQ,MAAM,KAAK,IAAI,YAAY,CAAC;AAAA,UACtC,WAAW,KAAK,KAAK,WAAW;AAC9B,oBAAQ,MAAM,KAAK,IAAI,WAAW,CAAa;AAAA,UACjD,OAAO;AACL,iBAAK,OAAO;AAAA,UACd;AAEA,eAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,QAAQ,MAA+B,aAAuB;AACrE,MAAM,oBAAiB,YAAY,IAAI,GAAG;AAIxC,UAAM,gBAAgB,YAAY,MAAM;AACxC,eAAW,QAAQ,eAAe;AAChC,YAAM,UAAU,cAAc,IAAI;AAClC,UAAI,KAAK,MAAM,cAAc,IAAI,GAAG;AAClC,mBAAW,SAAS,KAAK,MAAM,YAAY,IAAI,CAAC;AAAA,MAClD;AACA,cAAQ,QAAQ,KAAK;AACrB,WAAK,MAAM,SAAS,QAAQ,WAAW,IAAI,IAAI;AAAA,IACjD;AACA,SAAK,oBAAoB,YAAY,KAAK,IAAI;AAAA,EAChD,OAAO;AACL,SAAK,YAAY,WAAW;AAAA,EAC9B;AACF;;;AC/DA,SAAS,kBAAkB;AAG3B,YAAYC,QAAO;AASZ,IAAM,UAAN,MAAc;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EAEA,YACE,cACA,MACA,MACA;AACA,SAAK,eAAe;AACpB,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,eAA6C;AAC3C,UAAM,QAAsC,CAAC;AAE7C,UAAM,kBAA6C;AAAA,MAC/C;AAAA,QACE,YAAS;AAAA,QACT,WAAQ,CAAC,SAAS,gBAAgB,MAAM,IAAI,CAAC;AAAA,QAC7C,WAAQ,CAAC,SAAS,gBAAgB,MAAM,IAAI,CAAC;AAAA,MACjD;AAAA,MACE;AAAA,QACA;AAAA,QACE,WAAQ,CAAC,SAAS,gBAAgB,MAAM,IAAI,CAAC;AAAA,MACjD;AAAA,MACE,kBAAe;AAAA,MACf,iBAAc;AAAA,IAClB;AAEA,UAAM,cAAgB;AAAA,MAClB,cAAW,KAAK,IAAI;AAAA,MACpB,WAAQ,eAAe;AAAA,IAC3B;AACA,UAAM,iBAAmB;AAAA,MACrB,cAAW,KAAK,IAAI;AAAA,MACpB,WAAU,iBAAc,CAAC;AAAA,IAC7B;AAEA,UAAM,cAAgB,WAAU,yBAAsB,CAAC;AACvD,UAAM,kBAAoB,kBAAiB,cAAW,KAAK,IAAI,GAAG;AAAA,MAChE;AAAA,IACF,CAAC;AAED,UAAM,4BAA4B;AAElC,UAAM,UAAU,KAAK,KAAK,MAAM,WAAW,KAAK,IAAI;AACpD,eAAW,OAAO,QAAQ,gBAAgB;AACxC,UAAI,gBAAgB,MAAM,IAAI,MAAM,GAAG;AAErC,cAAM,CAAC,WAAW,IAAI,IAAI,WAAY;AAAA,UACpC,0BAA0B;AAAA,YACxB,MAAM,YAAY,QAAS;AAAA,YAC3B,QAAQ,IAAI,OAAO;AAAA,YACnB,YAAY,YAAY,QAAS;AAAA,YACjC,WAAW,YAAY,QAAS;AAAA,UAClC,CAAC;AAAA,QACH;AAEA,oBAAY,MAAM,MAAM;AAAA,MAC1B,WAAW,YAAY,MAAM,IAAI,MAAM,GAAG;AACxC,cAAM,KAAK,IAAI,UAAwC;AAAA,MACzD,WAAW,eAAe,MAAM,IAAI,MAAM,GAAG;AAE3C,YAAI,WAAY,SAAS;AAAA,UACvB,qBAAqB,MAAM;AACzB,kBAAM,aAAa,KAAK,MAAM,WAAW,KAAK,KAAK,IAAI;AACvD,gBAAI,CAAC,WAAY;AACjB,2BAAe,YAAY,iBAAiB,IAAI;AAAA,UAClD;AAAA,QACF,CAAC;AACD,YAAI,YAAY,MAAM,IAAI,MAAM,GAAG;AACjC,gBAAM,KAAK,IAAI,UAAwC;AAAA,QACzD;AAAA,MACF,WAAW,IAAI,YAAY,sBAAsB,GAAG;AAElD,YAAI,WAAW,OAAO;AAAA,MACxB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAEO,SAAS,aAAa,aAAqC;AAChE,QAAM,WAAsB,CAAC;AAE7B,QAAM,eAAiB,WAAU,aAAU,CAAC;AAC5C,QAAM,kBAAoB,WAAU,cAAW,CAAC;AAChD,QAAMC,YAAY;AAAA,IACd,cAAW,YAAY;AAAA,IACvB,YAAS;AAAA,IACT;AAAA,MACE;AAAA;AAAA,QAEE,uBAAoB,QAAW;AAAA,UAC7B;AAAA,YACA;AAAA,YACE,kBAAiB,cAAW,YAAY,IAAI,CAAC;AAAA,UACjD;AAAA,QACF,CAAC;AAAA,QACC,cAAW;AAAA;AAAA;AAAA,QAGX;AAAA,UACE,oBAAmB,eAAY,eAAe,GAAG,QAAW,IAAI;AAAA,QACpE;AAAA,QACE,cAAW;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAEA,aAAW,OAAO,YAAY,YAAY;AACxC,UAAM,YAAY,WAAW,KAAKA,SAAO;AAEzC,QAAI,WAAW;AACb,YAAM,UAAU,aAAa;AAC7B,YAAM,UAAU,YAAY,SAAS,MAAM;AAC3C,YAAM,UAAU,UAAU,MAAM,WAAW,OAAO;AAClD,iBAAW,SAAS,OAAO;AAC3B,eAAS,KAAK,IAAI,QAAQ,SAAS,SAAS,SAAS,CAAC;AAAA,IACxD;AAAA,EACF;AAEA,SAAO;AACT;;;AC3IA,YAAYC,QAAO;AAQnB,IAAO,iCAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,QAAQ;AAAA,EACf,OAAO;AAAA,EACP,MAAM,IAAI,KAAK,OAAO,SAAS;AAC7B,QAAI,CAAC,QAAS;AAEd,UAAM,QAAQ,QAAQ,GAAG,SAAS;AAAA,MAAQ,CAAC,YACzC,QAAQ,aAAa;AAAA,IACvB;AACA,UAAM,gBAAgB,MAAM,QAAQ,GAAG,OAAO,KAAK;AAEnD,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,OAAO,MAAM,CAAC;AACpB,YAAM,QAAQ,cAAc,CAAC;AAE7B,WAAK,YAAc,eAAY,KAAK,CAAC;AACrC,UAAI,OAAO,UAAU;AACnB,aAAK,WAAW,WAAW,uBAAuB;AAAA,IACtD;AAEA,UAAM,WAAW,MAAM;AAAA,EACzB;AACF;;;ACvBA,IAAO,kCAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,QAAQ;AAAA,EACf,OAAO;AAAA,EACP,IAAI,KAAK,OAAO,SAAS;AACvB,QAAI,CAAC,SAAS,KAAK,GAAI;AAEvB,UAAM,cAAc,QAAQ,KAAK,GAAG;AACpC,UAAM,iBAAiB,QAAQ,WAAW,MAAM,WAAW,WAAW;AACtE,QAAI,gBAAgB;AAClB,YAAM,WAAW,sBAAsB,cAAc,EAAE;AACvD,YAAM,WAAW,sBAAsB,cAAc,EAAE;AAAA,IACzD;AAAA,EACF;AACF;;;ACtBA,YAAYC,SAAO;AA6BnB,IAAO,8BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AACR,UAAM,QAAU,YAAU,eAAW,CAAC;AACtC,UAAM,eAAiB;AAAA,MACnB,YAAgB,CAAC,SAAS,WAAW,KAAK,IAAI,CAAC;AAAA,IACnD;AACA,UAAM,cAAc,SAAS,YAAY;AAEzC,UAAM,mBAAqB;AAAA,MACvB;AAAA,QACE;AAAA,UACA;AAAA,UACE,OAAK,kBAAc,GAAK,mBAAe,CAAC;AAAA,QAC5C;AAAA,MACF;AAAA,IACF;AAEA,UAAM,eAAe;AAAA,MACjB,gBAAY,KAAK;AAAA,MACnB;AAAA,IACF;AACA,UAAM,aAAe;AAAA,MACnB;AAAA,MACE,qBAAiB,gBAAgB;AAAA,IACrC;AAEA,UAAM,sBAAsB;AAAA,MACxB,qBAAiB,gBAAgB;AAAA,MACnC;AAAA,IACF;AAEA,WAAO;AAAA,MACL,iBAAiB,MAAM;AACrB,YAAI,CAAC,oBAAoB,MAAM,KAAK,IAAI,EAAG;AAC3C,cAAM,WAAW,iBAAiB,QAAS;AAAA,UACzC,CAAC,MAAM,YAAY,EAAE,GAAG,MAAM,aAAa;AAAA,QAC7C;AACA,YAAI,CAAC,SAAU;AACf,aAAK,YAAY,SAAS,KAAK;AAC/B,aAAK;AAAA,MACP;AAAA,MACA,mBAAmB,MAAM;AACvB,YAAI,CAAC,WAAW,MAAM,KAAK,IAAI,EAAG;AAClC,YAAI,iBAAiB,QAAS,WAAW,EAAG;AAE5C,cAAM,UAAU,KAAK,MAAM,WAAW,MAAM,QAAS,IAAI;AACzD,YAAI,CAAC,WAAW,CAAC,iBAAiB,SAAS,YAAY,EAAG;AAE1D;AAAA,UACE;AAAA,UACE;AAAA,YACA;AAAA,YACE,OAAK,kBAAc,GAAK,mBAAe,CAAC;AAAA,UAC5C;AAAA,QACF;AACA,aAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACF;;;ACxFA,YAAYC,SAAO;AAgBZ,SAAS,gBAAgB,KAAsC;AACpE,MAAI;AACJ,QAAM,eAAiB,YAAU,cAAU,CAAC;AAC5C,QAAM,kBAAoB,YAAU,eAAW,CAAC;AAChD,QAAMC,mBAAoB;AAAA,IACtB,oBAAkB,YAAU,OAAK,kBAAc,GAAG,gBAAgB,CAAC,CAAC;AAAA,EACxE;AAEA,QAAM,qBAAuB;AAAA,IAC3B;AAAA,IACE,eAAa,gBAAY,YAAY,CAAC;AAAA,IACtC;AAAA,MACA;AAAA,MACA,CAAC;AAAA,MACC,mBAAe,CAAG,oBAAkB,gBAAY,eAAe,CAAC,CAAC,CAAC;AAAA,IACtE;AAAA,EACF;AACA,QAAMC,wBAAwB,wBAAoB,QAAW;AAAA,IACzD,uBAAmB,iBAAiBD,gBAAe;AAAA,EACvD,CAAC;AAED,QAAME,YAAY;AAAA,IACd,eAAW,YAAY;AAAA,IACzB,CAAC;AAAA,IACC;AAAA;AAAA;AAAA,MAGE,mBAAe;AAAA,QACfD;AAAA,QACE,oBAAkB,mBAAe,kBAAkB,CAAC;AAAA,MACxD,CAAC;AAAA;AAAA;AAAA;AAAA,MAIC,mBAAe;AAAA,QACfA;AAAA,QACE,wBAAoB,kBAAkB;AAAA,QACtC,oBAAkB,mBAAiB,eAAW,YAAY,CAAC,CAAC;AAAA,MAChE,CAAC;AAAA,IACH;AAAA,EACF;AAEA,mBAAS,KAAK;AAAA;AAAA,IAEZ,oBAAoB,MAAM;AACxB,UAAIC,UAAQ,MAAM,KAAK,IAAI,GAAG;AAC5B,cAAM,SAASF,iBAAgB,QAAS,SAAS;AACjD,cAAM,OAAO,aAAa;AAC1B,cAAM,UAAU,KAAK,MAAM,WAAW,IAAI;AAC1C,mBAAW,SAAS,kBAAkB;AAEtC,iBAAS;AAAA,UACP;AAAA,UACA,YAAY,QAAQ;AAAA,UACpB,cAAc;AAAA,UACd,MAAM;AAAA,UACN;AAAA,QACF;AACA,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAAA;AAAA;AAAA,IAGA,oBAAoB,MAAM;AACxB,UAAI,CAACC,sBAAoB,MAAM,KAAK,IAAI,EAAG;AAE3C,YAAM,SAASD,iBAAgB,QAAS,SAAS;AACjD,YAAM,UAAU,KAAK,MAAM,WAAW,gBAAgB,QAAS,IAAI;AACnE,YAAM,eAAiB;AAAA,QACnB,gBAAY,eAAe;AAAA,QAC3B,mBAAiB,YAAQ,CAAC,UAAU,QAAQ,MAAM,CAAC;AAAA,MACvD;AACA,UAAI,CAAC,QAAQ,cAAc,CAAC,iBAAiB,SAAS,YAAY;AAChE;AAEF,0BAAoBA,iBAAgB,SAAU,QAAQ,cAAc;AACpE,WAAK,OAAO;AAAA,IACd;AAAA,EACF,CAAC;AAED,SAAO;AACT;;;AClGA,OAAOG,YAAW;AAQX,SAAS,oBAA6B;AAC3C,SAAO,OAAO,SAAiB;AAC7B,UAAM;AAAA,MACJ,SAAS,EAAE,QAAQ;AAAA,IACrB,IAAI,MAAM,OAAO,aAAa;AAC9B,UAAM,UAAU,IAAI,QAAQ;AAC5B,UAAM,UAAU,MAAM,QAAQ,cAAc;AAC5C,UAAM,SAAU,MAAM,QAAQ,KAAK,MAAM;AAAA,MACvC,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,CAAC;AACD,YAAQ,QAAQ;AAChB,YAAQ,QAAQ;AAChB,WAAO;AAAA,EACT;AACF;AAEO,SAAS,uBAAgC;AAC9C,SAAO,MAAM;AAEX,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC3D;AACF;AAEO,IAAM,YAAN,MAAgB;AAAA,EACrB;AAAA,EACQ;AAAA,EACA;AAAA,EAER,YACE,SACA,aACA,UACA,SACA;AACA,SAAK,UAAU;AACf,SAAK,WAAW;AAIhB,UAAM,kBAAkB;AAAA,MACtB,SAAS;AAAA,MACT,oBAAoB,MAAM;AAAA,IAC5B;AACA,UAAM,kBAAkB,SAAS,YAAY,KAAK,MAAM,eAAe;AACvE,UAAM,cAAc,UAAU,SAAS,QAAQ,MAAM,eAAe,IAAI;AACxE,UAAM,cAAc,SACjB,IAAI,CAAC,YAAY,SAAS,QAAQ,KAAK,MAAM,eAAe,CAAC,EAC7D,KAAK,KAAK;AAEb,SAAK,YAAY,CAAC,iBAAiB,aAAa,WAAW,EAAE,KAAK,KAAK;AAAA,EACzE;AAAA,EAEA,MAAM,OAAO,OAAuD;AAClE,UAAM,OAAO;AAAA,QACT,KAAK,SAAS;AAAA,gBACN,MAAM,KAAK,GAAG,CAAC;AAAA;AAG3B,QAAI;AACF,YAAM,SAAS,MAAM,KAAK,QAAQ,IAAI;AACtC,aAAO;AAAA,IACT,SAAS,OAAO;AACd,MAAAC,OAAM,sBAAsB,EAAE,YAAY,IAAI;AAC9C,UACE,iBAAiB,UAChB,MAAM,QAAQ,SAAS,kBAAkB,KACxC,MAAM,QAAQ,SAAS,oBAAoB,IAC7C;AACA,cAAM,IAAI;AAAA,UACR;AAAA,UACA,EAAE,OAAO,MAAM;AAAA,QACjB;AAAA,MACF;AACA,YAAM;AAAA,IACR;AAAA,EACF;AACF;;;AXhEA,IAAO,sBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,QAAQ;AAAA,EACf,OAAO;AAAA,EACP,MAAM,IAAI,KAAK,OAAO,SAAS;AAC7B,QAAI,CAAC,QAAS;AAEd,UAAMC,UAASC,OAAM,sBAAsB;AAC3C,UAAM,cAAc,gBAAgB,GAAG;AACvC,IAAAD;AAAA,MACE,cACI,iBAAiB,YAAY,YAAY,YAAY,YAAY,MAAM,KACvE;AAAA,IACN;AACA,QAAI,CAAC,YAAa;AAElB,UAAM,UAAU,iBAAiB,WAAW;AAC5C,IAAAA,QAAO,wBAAwB,UAAU,QAAQ,IAAI,EAAE;AAEvD,UAAM,WAAW,aAAa,WAAW;AACzC,IAAAA;AAAA,MACE,0BAA0B,SACvB,IAAI,CAAC,MAAM,EAAE,YAAY,EACzB,KAAK,IAAI,CAAC;AAAA,IACf;AAEA,UAAM,WAAW,eAAe,KAAK,2BAAiB,EAAE;AAExD,eAAW,WAAW,UAAU;AAC9B,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,MACV,EAAE;AAAA,IACJ;AAEA,UAAM,KAAK,IAAI,UAAU,SAAS,aAAa,UAAU,OAAO;AAChE,UAAM,YACJ,MAAM,oBAAoB,KAAK,gCAAsB,EAAE,GAAG,CAAC,GAC3D;AAEF,QAAI,SAAS,SAAS,GAAG;AACvB,kBAAY,KAAK,OAAO;AACxB,eAAS,OAAO;AAChB,eAAS,QAAQ,CAAC,YAAY,QAAQ,KAAK,OAAO,CAAC;AACnD,YAAM,WAAW,IAAI,SAAS;AAAA,IAChC;AAEA,UAAM,WAAW;AAAA,MACf;AAAA,MACA,CAAC,uBAAc,mBAAU,6BAAmB,2BAAiB;AAAA,MAC7D,EAAE,SAAS,KAAK;AAAA,IAClB,EAAE;AAAA,EACJ;AACF;;;AY9EA,YAAYE,SAAO;AACnB,SAAS,eAAAC,oBAAmB;AAY5B,IAAO,2BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AACR,UAAM,MAAQ,YAAU,eAAW,CAAC;AACpC,UAAM,8BAAgC,YAAU,cAAU,CAAC;AAC3D,UAAM,qBAAuB,YAAU,eAAW,CAAC;AACnD,UAAM,UAAY,YAAU,eAAW,CAAC;AACxC,UAAM,mBAAqB;AAAA,MACzB;AAAA,MACA;AAAA,MACE;AAAA,QACE;AAAA,UACE,sBAAkB;AAAA,UAClB;AAAA,YACA,sBAAwB,kBAAc,GAAG,aAAa;AAAA,YACtD,CAAG,kBAAc,UAAU,CAAC;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,UAAM,eAAiB;AAAA,MACrB,sBAAwB,kBAAc,GAAG,aAAa;AAAA,MACtD;AAAA,QACI,eAAa,gBAAY,2BAA2B,CAAC;AAAA,QACrD,mBAAe;AAAA,MACnB;AAAA,IACF;AAGA,UAAMC,YAAY;AAAA,MACd,eAAW,2BAA2B;AAAA,MACxC,CAAC,GAAG;AAAA,MACF,mBAAe;AAAA;AAAA,QAEb;AAAA,UACA;AAAA,UACA,CAAC,OAAO;AAAA,UACN,mBAAe;AAAA,YACf;AAAA;AAAA,YAEE;AAAA,cACE,mBAAiB,gBAAY,kBAAkB,GAAG;AAAA,gBAChD,qBAAiB,MAAQ,gBAAY,OAAO,GAAG,IAAI;AAAA,cACvD,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACE;AAAA,UACE,mBAAe;AAAA;AAAA,YAEfC;AAAA,cACI,gBAAY,GAAG;AAAA;AAAA,cAEf,mBAAe;AAAA,gBACb,oBAAkB,gBAAY,kBAAkB,CAAC;AAAA,cACrD,CAAC;AAAA;AAAA,cAEC,mBAAe;AAAA,gBACb;AAAA,kBACE,mBAAiB,gBAAY,kBAAkB,GAAG;AAAA,oBAChD,mBAAe,CAAC;AAAA,kBACpB,CAAC;AAAA,gBACH;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,MACL,oBAAoB,MAAM;AACxB,YAAI,CAACD,UAAQ,MAAM,KAAK,IAAI,EAAG;AAE/B,cAAM,UAAU,KAAK,MAAM;AAAA,UACzB,4BAA4B;AAAA,QAC9B;AAEA,iBAAS,eAAe,QAAQ,CAAC,QAAQ;AACvC,cAAI,aAAa,MAAM,IAAI,MAAM,GAAG;AAClC,uBAAW,KAAK,KAAK,CAAC,GAAG,OAAO;AAAA,UAClC;AAAA,QACF,CAAC;AAED,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACF;;;ACxGA,YAAYE,QAAO;AACnB,YAAYC,SAAO;AAGnB,IAAM,YAAY;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAO,2BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AACR,UAAM,OAAS;AAAA,MACX,OAAG,GAAI,OAAO,KAAK,SAAS,CAAgC;AAAA,IAChE;AACA,UAAM,MAAQ,YAAU,cAAU,CAAC;AACnC,UAAMC,YAAY,mBAAiB,eAAW,IAAI,GAAG;AAAA,MACjD,kBAAc,GAAG;AAAA,IACrB,CAAC;AAED,WAAO;AAAA,MACL,gBAAgB;AAAA,QACd,KAAK,MAAM;AACT,cAAI,CAACA,UAAQ,MAAM,KAAK,IAAI,EAAG;AAC/B,cAAI,KAAK,MAAM,WAAW,KAAK,SAAU,EAAE,WAAW,KAAK,CAAC,EAAG;AAE/D,cAAI;AAEF,kBAAM,QAAQ,UAAU,KAAK,OAAQ,EAAE;AAAA,cACrC;AAAA,cACA,IAAI;AAAA,YACN;AACA,iBAAK,YAAc,iBAAc,KAAK,CAAC;AACvC,iBAAK;AAAA,UACP,QAAQ;AAAA,UAER;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC5CA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAgBnB,IAAO,mCAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,SAAS,MAAM;AACb,UAAM,KAAO,YAAU,eAAW,CAAC;AACnC,UAAM,SAAW,YAAU,qBAAiB,CAAC,CAAC,CAAC;AAE/C,UAAM,aAAe,wBAAoB,QAAW;AAAA,MAChD,uBAAmB,IAAI,MAAM;AAAA,IACjC,CAAC;AACD,UAAM,MAAQ,YAAU,kBAAc,CAAC;AACvC,UAAM,WAAa,YAAmB,aAAS,CAAC;AAChD,UAAM,QAAU,YAAU,kBAAc,CAAC;AAEzC,UAAM,oBAAsB;AAAA,MACxB;AAAA,QACA;AAAA,QACE,qBAAmB,gBAAY,EAAE,GAAG,KAAK,QAAQ;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,QAAQ,MAAM;AAEZ,aAAK,MAAM,MAAM;AAAA,MACnB;AAAA,MACA,qBAAqB;AAAA,QACnB,KAAK,MAAM;AACT,cAAI,CAAC,KAAK,UAAU,CAAC,WAAW,MAAM,KAAK,IAAI,EAAG;AAElD,gBAAM,UAAU,KAAK,MAAM,WAAW,GAAG,QAAS,IAAI;AACtD,gBAAM,YAAY,KAAK;AACvB,gBAAM,eAAgB,KAAK,MAAiB;AAE5C,iBAAO,eAAe,UAAU,QAAQ;AACtC,kBAAM,UAAU,KAAK,WAAW,YAAY;AAC5C,gBACE,CAAC,kBAAkB,MAAM,QAAQ,IAAI,KACrC,qBAAqB,MAAM,SAAU,OAAO;AAE5C;AAGF,kBAAM,aACJ,SAAS,WACT,IAAI,QAAS,SAAS,oBACtB,IAAI,QAAS,SAAS;AAGxB,mBAAO,QAAS,WAAW;AAAA,cACvB,mBAAe,IAAI,SAAU,MAAM,SAAU,UAAU;AAAA,YAC3D;AAEA,oBAAQ,OAAO;AACf,oBAAQ,YAAY;AACpB,oBAAQ,eAAe,MAAM;AAG7B,gBACE,QAAQ,eAAe,KACvB,iBAAiB,MAAM,OAAO,OAAO,GACrC;AACA,sBAAQ,eAAe,CAAC,EAAE,YAAY,OAAO,OAAO;AACpD,mBAAK,OAAO;AACZ,mBAAK;AAAA,YACP;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAKA,SAAS,qBAAqB,MAAc,SAAkB;AAC5D;AAAA;AAAA,IAEE,QAAQ,eAAe,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC;AAAA,IAErE,gBAAc,mBAAe,CAAC,EAAE,MAAM,IAAI;AAAA;AAEhD;AAMA,IAAM,mBAA8C;AAAA,EAAQ,CAAC,SAExD;AAAA,IACC;AAAA,IACE,oBAAkB,YAAQ,gBAAgB,CAAC;AAAA,IAC3C,qBAAmB,YAAQ,oBAAoB,gBAAgB,CAAC,CAAC;AAAA,EACrE,EACC,MAAM,IAAI;AACf;;;ACnHA,YAAYC,SAAO;AAkBnB,IAAO,yBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AACR,UAAM,iBAAmB,YAAU,cAAU,CAAC;AAC9C,UAAM,YAAc,YAAU,eAAW,CAAC;AAC1C,UAAM,MAAQ,YAAU,eAAW,CAAC;AACpC,UAAM,UAAY,YAAU,eAAW,CAAC;AACxC,UAAM,MAAQ,YAAU,eAAW,CAAC;AACpC,UAAM,KAAO,YAAU,eAAW,CAAC;AAGnC,UAAMC,YAAY;AAAA,MACd,eAAW,cAAc;AAAA,MAC3B;AAAA,QACE,CAAC;AAAA,QACC,mBAAe;AAAA;AAAA,UAEb,wBAAoB,QAAW;AAAA,YAC7B,uBAAmB,WAAW,WAAW;AAAA,UAC7C,CAAC;AAAA;AAAA,UAEC;AAAA,YACE;AAAA,cACA;AAAA,cACA,CAAC,SAAS,EAAE;AAAA,cACV,mBAAe;AAAA,gBACb,wBAAoB,QAAW;AAAA;AAAA,kBAE7B;AAAA,oBACA;AAAA,oBACE;AAAA,sBACE,gBAAY,SAAS;AAAA,sBACrB;AAAA,wBACA;AAAA,wBACA,CAAC;AAAA,wBACC,mBAAe;AAAA;AAAA,0BAEb;AAAA,4BACE,gBAAY,EAAE;AAAA,4BACd,mBAAe;AAAA;AAAA,8BAEb,wBAAoB,QAAW;AAAA,gCAC7B;AAAA,kCACA;AAAA,kCACE;AAAA,oCACA;AAAA,sCACI,gBAAY,EAAE;AAAA,sCAChB;AAAA,oCACF;AAAA,oCACA;AAAA,sCACI,gBAAY,OAAO;AAAA,sCACnB,eAAW,WAAW;AAAA,oCAC1B;AAAA,kCACF;AAAA,gCACF;AAAA,8BACF,CAAC;AAAA;AAAA,8BAEC;AAAA,gCACE;AAAA,kCACA;AAAA,kCACE,gBAAY,EAAE;AAAA,kCACd,gBAAY;AAAA,gCAChB;AAAA,8BACF;AAAA;AAAA,8BAEE,oBAAkB,gBAAY,GAAG,CAAC;AAAA,4BACtC,CAAC;AAAA,0BACH;AAAA,wBACF,CAAC;AAAA,sBACH;AAAA;AAAA,sBAEE,uBAAmB,MAAM,CAAC,GAAK,mBAAe,CAAC,CAAC,CAAC;AAAA,oBACrD;AAAA,kBACF;AAAA,gBACF,CAAC;AAAA;AAAA,gBAEC;AAAA,kBACE;AAAA,oBACA;AAAA,oBACE,gBAAY,SAAS;AAAA,oBACvB;AAAA,kBACF;AAAA,gBACF;AAAA;AAAA,gBAEE,oBAAkB,gBAAY,GAAG,CAAC;AAAA,cACtC,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,UAAM,YAAY,KAAK,CAAC,GAAK,mBAAe,CAAC,CAAC,CAAC;AAE/C,WAAO;AAAA,MACL,mBAAmB,MAAM;AACvB,YAAI,CAACA,UAAQ,MAAM,KAAK,IAAI,EAAG;AAC/B,cAAM,UAAU,KAAK,MAAM,WAAW,eAAe,OAAQ;AAC7D,YAAI,CAAC,QAAS;AAId,gBAAQ,eACL,OAAO,CAAC,QAAQ,IAAI,OAAO,SAAS,gBAAgB,EACpD,QAAQ,CAAC,QAAQ;AAChB,cAAI,IAAI,YAAY,OAAO,SAAS,kBAAkB;AAGpD,gBAAI,WAAW,YAAY,OAAO;AAAA,UACpC,OAAO;AAGL,oCAAwB,GAA6B;AAAA,UACvD;AAGA,qBAAW,KAAK,SAAS,GAAG,OAAO;AAEnC,eAAK;AAAA,QACP,CAAC;AAEH,aAAK,OAAO;AACZ,aAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,wBAAwB,MAA8B;AAC7D,QAAM,UAAY,YAAU,cAAU,CAAC;AACvC,QAAM,aAAe;AAAA,IACjB,eAAW,OAAO;AAAA,IAClB,mBAAiB,eAAW,KAAK,KAAK,IAAI,CAAC;AAAA,EAC/C;AACA,QAAM,cAAgB;AAAA,IAClB,mBAAiB,eAAa,gBAAY,OAAO,CAAC,GAAG,CAAC,CAAC;AAAA,EAC3D;AACA,QAAM,UAAU,WAAW,MAAM,UAAU;AAE3C,MAAI,SAAS;AACX,UAAM,UAAU,QAAQ,MAAM,WAAW,QAAQ,OAAQ;AAEzD,aAAS,eAAe,QAAQ,CAAC,QAAQ;AACvC,UAAI,YAAY,MAAM,IAAI,YAAY,MAAM;AAC1C,YAAI,YAAY,YAAY,OAAO;AAAA,IACvC,CAAC;AACD,YAAQ,OAAO;AAAA,EACjB;AACF;;;AC1KA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAMnB,IAAO,wBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,QAAQ;AAAA,EACf,UAAU;AACR,UAAM,OAAS,YAAU,eAAW,CAAC;AACrC,UAAM,KAAO,YAAU,uBAAmB,IAAI,CAAC;AAC/C,UAAMC,YAAY,wBAAoB,OAAO;AAAA,MACzC,uBAAmB,MAAM,EAAE;AAAA,IAC/B,CAAC;AAED,WAAO;AAAA,MACL,qBAAqB;AAAA,QACnB,KAAK,MAAM;AACT,cAAIA,UAAQ,MAAM,KAAK,IAAI,KAAK,KAAK,QAAQ,QAAQ;AACnD,iBAAK;AAAA,cACD;AAAA,gBACA,KAAK;AAAA,gBACL,GAAG,QAAS;AAAA,gBACZ,GAAG,QAAS;AAAA,gBACZ,GAAG,QAAS;AAAA,gBACZ,GAAG,QAAS;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACnCA,YAAYC,SAAO;AACnB,YAAYC,SAAO;;;ACAnB,SAAS,oBAAoB;AAKtB,SAAS,YAAY,OAAc,OAAe,QAAgB;AACvE,MAAI,MAAM;AACV,MAAI,IAAI;AACR,KAAG;AACD,UAAM,aAAa,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,KAAK,IAAI;AAC/C;AAAA,EACF,SACE,MAAM,SAAS,GAAG,KAClB,MAAM,WAAW,GAAG,KACpB,MAAM,UAAU,GAAG,KACnB,MAAM,aAAa,GAAG;AAGxB,QAAMC,WAAU,MAAM,iBAAiB;AACvC,EAAAA,SAAQ,WAAW,GAAG,IAAI;AAC1B,EAAAA,SAAQ,KAAK,GAAG,IAAI;AACpB,SAAO;AACT;;;ADjBA,IAAO,cAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,QAAQ;AAAA,EACf,OAAO;AAAA,EACP,SAAS,MAAM;AACb,UAAM,iCAAmC;AAAA,MACrC;AAAA,QACE,eAAW;AAAA,QACX,YAAQ,CAAC,SAAS,+BAA+B,MAAM,IAAI,CAAC;AAAA,MAChE;AAAA,MACE,eAAW;AAAA,MACb;AAAA,IACF;AAEA,UAAM,OAAS;AAAA,MACX;AAAA,QACE,eAAW;AAAA;AAAA,QACX,kBAAc;AAAA;AAAA,QAChB;AAAA;AAAA,MACF;AAAA,IACF;AACA,UAAM,QAAU,YAAU,OAAK,qBAAiB,GAAK,gBAAY,CAAC,CAAC;AAGnE,UAAM,iBAAmB;AAAA,MACvB,sBAAsB,SAAS,eAAe;AAAA,MAC5C;AAAA,QACA;AAAA,QACA;AAAA,QACE,eAAa,OAAK,kBAAc,GAAK,kBAAc,CAAC,CAAC;AAAA,MACzD;AAAA,IACF;AAGA,UAAM,kBAAoB;AAAA,MACxB,sBAAsB,SAAS,eAAe;AAAA,MAC5C;AAAA,QACA,sBAAsB,SAAS,UAAU;AAAA,QACvC,gBAAY;AAAA,QACZ,eAAa,OAAK,kBAAc,GAAK,kBAAc,CAAC,CAAC;AAAA,MACzD;AAAA,IACF;AAEA,WAAO;AAAA,MACL,gBAAgB;AAAA,QACd,KAAK,MAAM;AACT,cAAI,gBAAgB,MAAM,KAAK,IAAI,GAAG;AACpC,kBAAM,WAAW;AAAA,cACf,KAAK,KAAK,UAAU,MAAM,CAAC;AAAA,YAC7B;AACA,kBAAM,UAAY,uBAAmB;AACrC,kBAAM,UAAY,uBAAmB;AACrC,kBAAM,WAAa,gBAAY,SAAS,SAAS,QAAQ;AACzD,iBAAK,KAAK,kBAAkB;AAC5B,iBAAK,YAAY,QAAQ;AACzB,iBAAK;AAAA,UACP;AAEA,cAAI,eAAe,MAAM,KAAK,IAAI,GAAG;AACnC,gBAAI,OAAO,YAAY,KAAK,OAAQ;AAIpC,gBACI,iBAAa,KAAK,OAAO,KAC3B,SAAS,KAAK,KAAK,QAAQ,IAAI,GAC/B;AACA,oBAAM,UAAU,KAAK,MAAM,WAAW,KAAK,QAAQ,IAAI;AACvD,kBAAI,CAAC,QAAS;AACd,qBAAS,kBAAc,YAAY,KAAK,OAAO,WAAW,CAAC;AAC3D,mBAAK,MAAM,OAAO,KAAK,QAAQ,MAAM,KAAK,IAAI;AAAA,YAChD;AAEA,kBAAM,aAAe,uBAAmB,MAAM,OAAO,IACjD,kBAAkB,MAAM,OAAO,IAC/B,CAAC;AACL,kBAAM,WAAW;AAAA,cACf,KAAK,KAAK,UAAU,MAAM,CAAC;AAAA,YAC7B;AACA,kBAAM,cAAc,SAAS,WAAW;AACxC,kBAAM,UAAY,sBAAkB,MAAM,YAAY,WAAW;AACjE,kBAAM,UAAY,sBAAkB,IAAI;AACxC,kBAAM,UAAY,eAAW,SAAS,SAAS,QAAQ;AACvD,iBAAK,KAAK,kBAAkB;AAC5B,iBAAK,YAAY,OAAO;AACxB,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAOA,SAAS,YACP,MACyC;AACzC,MAAM,iBAAa,IAAI,GAAG;AACxB,WAAS,kBAAc,KAAK,IAAI;AAAA,EAClC,WAAa,oBAAgB,IAAI,GAAG;AAClC,WAAS,kBAAc,KAAK,KAAK;AAAA,EACnC,OAAO;AACL,UAAM,SAAS;AAAA,MACb,KAAK;AAAA,IACP;AACA,UAAM,WAAa,kBAAe,KAAK,SAA0B,IAAI;AACrE,WAAS,wBAAoB,QAAQ,QAAQ;AAAA,EAC/C;AACF;AAOA,SAAS,kBACP,QAC2C;AAC3C,QAAM,OAAS,YAAU,cAAU,CAAC;AACpC,QAAM,QAAU,YAAU,kBAAc,CAAC;AACzC,QAAMC,YAAY;AAAA,IACd,OAAK,eAAW,IAAI,GAAK,kBAAc,IAAI,CAAC;AAAA,IAC9C;AAAA,EACF;AAEA,SAAO,OAAO,WAAW,IAAI,CAAC,aAAa;AACzC,QAAIA,UAAQ,MAAM,QAAQ,GAAG;AAC3B,YAAM,UAAY,kBAAc,KAAK,OAAQ;AAC7C,UAAI,MAAM,QAAS,SAAS,iBAAiB;AAC3C,cAAM,kBAAkB,QAAQ,KAAK,MAAM,QAAQ,KAAK;AACxD,cAAMC,YAAW,kBACX,2BAAuB,MAAM,OAAO,IACtC,MAAM;AACV,eAAS,iBAAa,SAASA,SAAQ;AAAA,MACzC;AACA,YAAM,WAAa,2BAAuB,MAAM,OAAQ;AACxD,aAAS,iBAAa,SAAS,QAAQ;AAAA,IACzC,WAAa,oBAAgB,QAAQ,GAAG;AACtC,aAAS,uBAAmB,SAAS,QAAQ;AAAA,IAC/C,OAAO;AACL,YAAM,IAAI;AAAA,QACR,sCAAsC,YAAY,MAAM,CAAC;AAAA,MAC3D;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,gBACP,UAC4E;AAC5E,SAAO,SAAS,IAAI,CAAC,UAAU;AAC7B,QAAM,iBAAa,KAAK,GAAG;AACzB,aAAO;AAAA,IACT,WAAa,oBAAgB,KAAK,GAAG;AACnC,YAAM,kBAAkB,aAAa,KAAK,MAAM,KAAK;AACrD,aAAO,kBACD,2BAAuB,KAAK,IAC5B,YAAQ,MAAM,KAAK;AAAA,IAC3B,WAAa,oBAAgB,KAAK,GAAG;AACnC,aAAS,mBAAe,MAAM,QAAQ;AAAA,IACxC,OAAO;AACL,aAAS,2BAAuB,KAAK;AAAA,IACvC;AAAA,EACF,CAAC;AACH;;;AE9KA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAKnB,IAAM,4BAA4B;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAMA,IAAO,kBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,QAAQ;AAAA,EACf,OAAO;AAAA,EACP,SAAS,MAAM;AACb,UAAM,iCAAmC;AAAA,MACrC;AAAA,QACE,eAAW;AAAA,QACX,YAAQ,CAAC,SAAS,+BAA+B,MAAM,IAAI,CAAC;AAAA,MAChE;AAAA,MACE,eAAW;AAAA,MACb;AAAA,IACF;AACA,UAAM,kBAAoB;AAAA,MACtB,eAAW;AAAA;AAAA,MACX,kBAAc;AAAA;AAAA,MAChB;AAAA;AAAA,IACF;AACA,UAAM,OAAS,YAAU,kBAAc,CAAC;AACxC,UAAM,eAAe,sBAAsB,SAAS,UAAU;AAC9D,UAAM,QAAU,YAAU,qBAAiB,CAAC;AAC5C,UAAM,MAAQ,YAAU,kBAAc,CAAC;AAEvC,UAAM,cAAgB,YAAU,OAAG,GAAG,yBAAyB,CAAC;AAEhE,UAAM,aAAe;AAAA,MACjB,eAAW,WAAW;AAAA,MACtB,YAAQ,MAAM,OAAS,UAAM,EAAE,KAAK,GAAG,KAAK,GAAG,SAAS,IAAI,CAAC,CAAC;AAAA,IAClE;AAEA,WAAO;AAAA,MACL,gBAAgB;AAAA,QACd,KAAK,MAAM;AACT,cAAI,CAAC,WAAW,MAAM,KAAK,IAAI,EAAG;AAElC,cAAI;AACJ,cAAI,gBAAgB,MAAM,KAAK,OAAQ,GAAG;AACxC,mBAAOC,aAAY,KAAK,OAAO;AAAA,UACjC,OAAO;AACL,mBAAS,kBAAc,YAAY,KAAK,OAAO,WAAW,CAAC;AAC3D,kBAAM,eAAiB,wBAAoB,SAAS;AAAA,cAChD,uBAAqB,eAAW,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,YAC5D,CAAC;AACD,iBAAK,mBAAmB,GAAG,aAAa,YAAY;AAAA,UACtD;AACA,gBAAM,aAAa,aAAa,MAAM,KAAK,OAAO;AAIlD,cACI,iBAAa,KAAK,OAAO,KAC3B,SAAS,KAAK,KAAK,QAAQ,IAAI,GAC/B;AACA,kBAAM,UAAU,KAAK,MAAM,WAAW,KAAK,QAAQ,IAAI;AACvD,gBAAI,CAAC,QAAS;AACd,mBAAS,kBAAc,KAAK,MAAM,YAAY,WAAW,CAAC;AAC1D,iBAAK,MAAM,OAAO,KAAK,QAAQ,MAAM,KAAK,IAAI;AAAA,UAChD;AAEA,gBAAM,aAAaC,mBAAkB,MAAM,OAAQ;AACnD,cAAI,KAAK,KAAK,UAAU,WAAW,GAAG;AACpC,uBAAW;AAAA,cACP;AAAA,gBACE,kBAAc,KAAK;AAAA,gBACrB,sBAAsB,IAAI,OAAQ;AAAA,cACpC;AAAA,YACF;AAAA,UACF;AACA,gBAAM,WAAWC;AAAA,YACf,MAAM;AAAA,YACN,YAAY;AAAA,UACd;AAEA,cAAI,cAAc,WAAW,WAAW,GAAG;AACzC,kBAAM,UAAY,uBAAmB;AACrC,kBAAM,UAAY,uBAAmB;AACrC,kBAAM,WAAa,gBAAY,SAAS,SAAS,QAAQ;AACzD,iBAAK,KAAK,kBAAkB;AAC5B,iBAAK,YAAY,QAAQ;AAAA,UAC3B,OAAO;AACL,kBAAM,cAAc,SAAS,WAAW;AACxC,kBAAM,UAAY,sBAAkB,MAAM,YAAY,WAAW;AACjE,kBAAM,UAAY,sBAAkB,IAAI;AACxC,kBAAM,UAAY,eAAW,SAAS,SAAS,QAAQ;AACvD,iBAAK,KAAK,kBAAkB;AAC5B,iBAAK,YAAY,OAAO;AAAA,UAC1B;AACA,eAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAOA,SAASF,aACP,MACyC;AACzC,MAAM,iBAAa,IAAI,GAAG;AACxB,WAAS,kBAAc,KAAK,IAAI;AAAA,EAClC,WAAa,oBAAgB,IAAI,GAAG;AAClC,WAAS,kBAAc,KAAK,KAAK;AAAA,EACnC,OAAO;AACL,UAAM,SAASA;AAAA,MACb,KAAK;AAAA,IACP;AACA,UAAM,WAAa,kBAAe,KAAK,SAA0B,IAAI;AACrE,WAAS,wBAAoB,QAAQ,QAAQ;AAAA,EAC/C;AACF;AAOA,SAASC,mBACP,QAC2C;AAC3C,QAAM,OAAS,YAAU,cAAU,CAAC;AACpC,QAAM,QAAU,YAAU,kBAAc,CAAC;AACzC,QAAME,YAAY;AAAA,IACd,OAAK,eAAW,IAAI,GAAK,kBAAc,IAAI,CAAC;AAAA,IAC9C;AAAA,EACF;AAEA,SAAO,OAAO,WAAW,QAAQ,CAAC,aAAa;AAC7C,QAAIA,UAAQ,MAAM,QAAQ,GAAG;AAC3B,UAAI,KAAK,YAAY,WAAY,QAAO,CAAC;AAEzC,YAAM,UAAY,kBAAc,KAAK,OAAQ;AAC7C,YAAM,WAAW,sBAAsB,MAAM,OAAQ;AACrD,aAAS,iBAAa,SAAS,QAAQ;AAAA,IACzC,WAAa,oBAAgB,QAAQ,GAAG;AACtC,aAAS,uBAAmB,SAAS,QAAQ;AAAA,IAC/C,OAAO;AACL,YAAM,IAAI;AAAA,QACR,sCAAsC,YAAY,MAAM,CAAC;AAAA,MAC3D;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,sBACPC,aAC4C;AAC5C,MAAIA,YAAW,SAAS,iBAAiB;AACvC,UAAM,kBAAkB,QAAQ,KAAKA,YAAW,KAAK;AACrD,WAAO,kBAAoB,2BAAuBA,WAAU,IAAIA;AAAA,EAClE;AACA,SAAS,2BAAuBA,WAAU;AAC5C;AAEA,SAASF,iBACP,QACA,QACyD;AACzD,QAAM,WAAa,YAAU,kBAAc,CAAC;AAC5C,QAAMC,YAAY;AAAA,IACd,OAAK,eAAW,UAAU,GAAK,kBAAc,UAAU,CAAC;AAAA,IAC1D;AAAA,EACF;AAEA,QAAM,OAAO,OAAO,WAAW,KAAK,CAACE,UAASF,UAAQ,MAAME,KAAI,CAAC;AACjE,MAAI,CAAC,KAAM,QAAO,CAAC;AAEnB,MAAI,OAAO,SAAS,MAAM,KAAO,sBAAkB,SAAS,OAAO,GAAG;AACpE,WAAO,SAAS,QAAQ,SAAS;AAAA,MAAI,CAAC,UACpC,aAAa,KAAqB;AAAA,IACpC;AAAA,EACF;AACA,SAAO,CAAC,aAAa,SAAS,OAAQ,CAAC;AACzC;AAEA,SAAS,aACP,OACqD;AACrD,MAAM,iBAAa,KAAK,GAAG;AACzB,WAAO;AAAA,EACT,WAAa,oBAAgB,KAAK,GAAG;AACnC,UAAM,kBAAkB,aAAa,KAAK,MAAM,KAAK;AACrD,WAAO,kBACD,2BAAuB,KAAK,IAC5B,YAAQ,MAAM,KAAK;AAAA,EAC3B,OAAO;AACL,WAAS,2BAAuB,KAAK;AAAA,EACvC;AACF;;;AChNA,YAAYC,SAAO;AAInB,IAAO,iBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,QAAQ,QAAQ,MAAM,MAAM;AAC1B,WAAO;AAAA,MACL,mBAAmB;AAAA,QACjB,KAAK,MAAM;AACT,cAAI,CAAC,KAAK,oBAAoB,EAAG;AACjC,cAAI,KAAK,WAAW,kBAAkB,EAAG;AACzC,cAAI,KAAK,WAAW,iBAAiB,EAAG;AACxC,cAAI,CAAC,MAAM,KAAK,KAAK,IAAI,EAAG;AAE5B,gBAAM,UAAU,KAAK,MAAM,WAAW,KAAK,KAAK,IAAI;AACpD,cAAI,CAAC,QAAS;AACd,cACE,QAAQ,eAAe,KAAK,CAAC,QAAQ,IAAI,yBAAyB,CAAC;AAEnE;AAEF,qBAAW,SAAS,UAAU,IAAI,CAAC;AAAA,QACrC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,iBAAmB;AAAA,EACrB,eAAW;AAAA,EACX,mBAAiB,eAAW,SAAS,GAAG,CAAG,kBAAc,CAAC,CAAC;AAC/D;AAEA,SAAS,UAAU,MAAsC;AACvD,MAAI,KAAK,WAAW,QAAQ,EAAE,IAAI,KAAK,KAAK,CAAC,GAAG;AAC9C,WAAO,YAAY,KAAK,OAAO,GAAG;AAAA,EACpC,WAAW,KAAK,WAAW,WAAW,EAAE,IAAI,KAAK,KAAK,CAAC,GAAG;AACxD,WAAO,YAAY,KAAK,OAAO,GAAG;AAAA,EACpC,WACE,KAAK,YAAY,YAChB,KAAK,WAAW,oBAAoB,EAAE,MAAM,KAAK,KAAK,CAAC,KACtD,KAAK,WAAW,YAAY,UAC9B;AACA,WAAO,YAAY,KAAK,OAAO,GAAG;AAAA,EACpC,WAAW,eAAe,MAAM,KAAK,MAAM,GAAG;AAC5C,WAAO;AAAA,MACL,KAAK;AAAA,MACJ,KAAK,WAAW,IAAI,kBAAkB,EACpC,KAAK;AAAA,IACV;AAAA,EACF,WAAW,KAAK,WAAW,qBAAqB,EAAE,IAAI,KAAK,KAAK,CAAC,GAAG;AAClE,UAAM,OAAO,KAAK,WAAW,IAAI,MAAM;AACvC,UAAM,SAAU,KAAK,aAAa,KAAK,uBAAuB,IAAI,KAAM;AACxE,WAAO,YAAY,KAAK,OAAO,MAAM,UAAU,MAAM,CAAC;AAAA,EACxD,WAAW,KAAK,WAAW,eAAe,GAAG;AAC3C,WAAO,YAAY,KAAK,OAAO,GAAG;AAAA,EACpC,OAAO;AACL,WAAO,KAAK,KAAK;AAAA,EACnB;AACF;AAEA,SAAS,uBACPC,aACoB;AACpB,MAAIA,YAAW,aAAa,GAAG;AAC7B,WAAOA,YAAW,KAAK;AAAA,EACzB,WAAWA,YAAW,qBAAqB,GAAG;AAC5C,WAAOA,YAAW,KAAK,IAAI,QAAQ;AAAA,EACrC,WAAWA,YAAW,0BAA0B,GAAG;AACjD,WAAO;AAAA,EACT,WAAWA,YAAW,kBAAkB,GAAG;AACzC,WAAOA,YAAW,KAAK,IAAI,QAAQ;AAAA,EACrC,WAAWA,YAAW,iBAAiB,GAAG;AACxC,WAAO;AAAA,MACLA,YAAW,IAAI,QAAQ;AAAA,IACzB;AAAA,EACF,WAAWA,YAAW,iBAAiB,GAAG;AACxC,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,UAAU,KAAa;AAC9B,SAAO,IAAI,SAAS,IAAI,IAAI,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC,IAAI;AAChE;;;AC1FA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAGnB,IAAO,6BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AACR,UAAM,oBAAsB,YAAU,kBAAc,CAAC;AACrD,UAAM,QAAU,YAAU,mBAAe,CAAC;AAC1C,UAAM,UAAY,YAAU,eAAW,CAAC;AACxC,UAAM,QAAU;AAAA,MACZ,OAAK,eAAW,GAAK,iBAAa,GAAK,kBAAc,CAAC;AAAA,IAC1D;AAGA,UAAM,mBAAqB;AAAA,MACzB;AAAA,MACE;AAAA,QACA;AAAA,QACA,sBAAsB,aAAa,QAAQ;AAAA,QAC3C;AAAA,MACF;AAAA,MACE;AAAA,QACA;AAAA,QACE;AAAA,UACE,eAAW,WAAW;AAAA,UACtB,gBAAY,KAAK;AAAA,UACnB;AAAA,QACF;AAAA,QACE,eAAW,WAAW;AAAA,MAC1B;AAAA,IACF;AAEA,UAAM,kBAAoB;AAAA,MACxB;AAAA,MACE;AAAA,QACA;AAAA,QACA,sBAAsB,aAAa,QAAQ;AAAA,QAC3C;AAAA,MACF;AAAA,MACE;AAAA,QACA;AAAA,QACE;AAAA,UACE,eAAW,WAAW;AAAA,UACtB,gBAAY,KAAK;AAAA,UACnB;AAAA,QACF;AAAA,QACE,eAAW,WAAW;AAAA,MAC1B;AAAA,IACF;AAEA,UAAM,eAAiB,wBAAoB,QAAW;AAAA,MAClD;AAAA,QACA;AAAA,QACE;AAAA,UACA;AAAA,UACE;AAAA,YACE,eAAW,WAAW;AAAA,YACtB,gBAAY,KAAK;AAAA,YACnB;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAED,UAAM,oBAAsB,wBAAoB,QAAW;AAAA,MACvD;AAAA,QACA;AAAA,QACE;AAAA,UACA;AAAA,UACA;AAAA,UACE;AAAA,YACE,eAAW,WAAW;AAAA,YACtB,gBAAY,KAAK;AAAA,YACnB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAED,UAAM,mBAAqB,wBAAoB,QAAW;AAAA,MACtD;AAAA,QACA;AAAA,QACE;AAAA,UACA;AAAA,UACA;AAAA,UACE;AAAA,YACE,eAAW,WAAW;AAAA,YACtB,gBAAY,KAAK;AAAA,YACnB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAGD,UAAM,oBAAsB;AAAA,MACxB,qBAAiB,OAAO,SAAW,eAAW,WAAW,CAAC;AAAA,MAC1D,mBAAe;AAAA,QACb;AAAA,UACE;AAAA,YACA;AAAA,YACE,gBAAY,OAAO;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,cAAgB,wBAAoB,QAAW;AAAA,MACjD;AAAA,QACA;AAAA,QACE;AAAA,UACE;AAAA,YACA;AAAA,YACA,sBAAsB,aAAa,QAAQ;AAAA,YAC3C;AAAA,UACF;AAAA,UACE;AAAA,YACE,eAAW,WAAW;AAAA,YACtB,gBAAY,KAAK;AAAA,YACnB;AAAA,UACF;AAAA,UACE,eAAW,WAAW;AAAA,QAC1B;AAAA,MACF;AAAA,IACF,CAAC;AAED,WAAO;AAAA,MACL,qBAAqB;AAAA,QACnB,KAAK,MAAM;AACT,gBAAM,KAAK,KAAK,WAAW;AAC3B,cAAI,CAAG,eAAW,EAAE,KAAK,KAAK,QAAQ,EAAG;AAEzC,gBAAM,WAAW,aAAa,MAAM,KAAK,IAAI,IACvC,sBAAkB,MAAM,SAAU,kBAAkB,OAAQ,IAC9D,kBAAkB,MAAM,KAAK,IAAI,IAC7B,sBAAkB,MAAM,SAAY,mBAAe,KAAK,CAAC,IAC3D,iBAAiB,MAAM,KAAK,IAAI,IAC5B,sBAAkB,MAAM,SAAY,mBAAe,IAAI,CAAC,IAC1D,YAAY,MAAM,KAAK,IAAI,IACzB,MAAM,UACN;AACV,cAAI,CAAC,SAAU;AAEf,mBAAS,IAAI,GAAG,OAAO,QAAQ,IAAI,MAAM,QAAS,OAAO,KAAK;AAC5D,eAAG,OAAO,CAAC,IAAM,eAAW,KAAK,MAAM,YAAY,OAAO,CAAC;AAAA,UAC7D;AACA,aAAG,OAAO,MAAM,QAAS,KAAK,IAAI;AAClC,eAAK,OAAO;AACZ,eAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA,aAAa;AAAA,QACX,KAAK,MAAM;AACT,gBAAM,KAAK,KAAK,WAAW;AAC3B,cAAI,CAAG,eAAW,EAAE,KAAK,KAAK,QAAQ,EAAG;AACzC,cAAI,CAAC,kBAAkB,MAAM,KAAK,IAAI,EAAG;AAEzC,gBAAM,UAAU,KAAK,MAAM,cAAc,QAAQ,QAAS,IAAI;AAC9D,cAAI,CAAC,QAAS;AACd,gBAAM,kBACJ,QAAQ,KAAK,YAAY,YAAY,QAAQ,KAAK,WAAW;AAC/D,cAAI,CAAC,gBAAiB;AAEtB,kBAAQ,KAAK;AAAA,YACT,sBAAkB,QAAQ,SAAU,kBAAkB,OAAQ;AAAA,UAClE;AACA,eAAK,OAAO;AACZ,eAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACjLA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAInB,IAAO,8BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AACR,UAAM,WAAa,YAAU,OAAG,MAAe,IAAa,CAAC;AAE7D,UAAM,OAAS,YAAU,OAAK,eAAW,GAAK,qBAAiB,CAAC,CAAC;AACjE,UAAM,QAAU,YAAU,kBAAc,CAAC;AAEzC,UAAM,YAAc;AAAA,MAClB;AAAA,MACA;AAAA,MACE,yBAAqB,KAAO,gBAAY,IAAI,GAAG,KAAK;AAAA,IACxD;AAEA,UAAM,SAAW,YAAU,kBAAc,CAAC;AAC1C,UAAM,WAAa,YAAU,kBAAc,CAAC;AAC5C,UAAM,SAAW,YAAU,eAAW,CAAC;AACvC,UAAM,SAAW;AAAA,MACb,qBAAmB,gBAAY,MAAM,GAAK,gBAAY,QAAQ,CAAC;AAAA,IACnE;AAEA,UAAM,gBAAkB;AAAA,MACtB;AAAA,MACE,qBAAmB,yBAAqB,KAAK,QAAQ,MAAM,GAAG,QAAQ;AAAA,MACtE,yBAAqB,KAAK,QAAQ,KAAK;AAAA,IAC3C;AAGA,UAAM,wBAA0B;AAAA,MAC9B;AAAA,MACE;AAAA,QACA;AAAA,QACE,yBAAqB,KAAK,QAAQ,QAAQ;AAAA,QAC5C;AAAA,MACF;AAAA,MACE;AAAA,QACA;AAAA,QACE,qBAAmB,gBAAY,MAAM,GAAK,gBAAY,MAAM,GAAG,IAAI;AAAA,QACrE;AAAA,MACF;AAAA,IACF;AAEA,UAAM,UAAY,YAAU,eAAW,CAAC;AAExC,UAAM,6BAA+B;AAAA,MACnC;AAAA,MACE;AAAA,QACE,yBAAqB,KAAK,QAAQ,MAAM;AAAA,QACxC,yBAAqB,KAAK,SAAS,QAAQ;AAAA,QAC7C;AAAA,MACF;AAAA,MACE;AAAA,QACA;AAAA,QACE,qBAAmB,gBAAY,MAAM,GAAK,gBAAY,OAAO,GAAG,IAAI;AAAA,QACtE;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,mBAAmB;AAAA,QACjB,KAAK,MAAM;AACT,cAAI,UAAU,MAAM,KAAK,IAAI,GAAG;AAC9B,iBAAK;AAAA,cACD;AAAA,gBACA,SAAS,UAAW;AAAA,gBACpB,KAAK;AAAA,gBACL,MAAM;AAAA,cACR;AAAA,YACF;AACA,iBAAK;AAAA,UACP,WAAW,cAAc,MAAM,KAAK,IAAI,GAAG;AACzC,kBAAM,UAAU,KAAK,MAAM,WAAW,OAAO,QAAS,IAAI;AAC1D,gBAAI,CAAC,oBAAoB,SAAS,CAAC,EAAG;AAEtC,oBAAQ,KAAK,OAAO;AACpB,mBAAO,QAAS,SAAS,OAAO;AAChC,iBAAK;AAAA,cACD;AAAA,gBACA,SAAS,UAAW;AAAA,gBACpB,OAAO;AAAA,gBACP,MAAM;AAAA,cACR;AAAA,YACF;AACA,iBAAK;AAAA,UACP,WAAW,sBAAsB,MAAM,KAAK,IAAI,GAAG;AACjD,kBAAM,UAAU,KAAK,MAAM,WAAW,OAAO,QAAS,IAAI;AAC1D,gBAAI,CAAC,oBAAoB,SAAS,CAAC,EAAG;AAEtC,oBAAQ,KAAK,OAAO;AACpB,iBAAK;AAAA,cACD;AAAA,gBACA,SAAS,UAAW;AAAA,gBAClB,qBAAiB,OAAO,SAAU,SAAS,SAAU,IAAI;AAAA,gBAC3D,MAAM;AAAA,cACR;AAAA,YACF;AACA,iBAAK;AAAA,UACP,WAAW,2BAA2B,MAAM,KAAK,IAAI,GAAG;AACtD,kBAAM,UAAU,KAAK,MAAM,WAAW,OAAO,QAAS,IAAI;AAC1D,kBAAM,WAAW,KAAK,MAAM,WAAW,QAAQ,QAAS,IAAI;AAC5D,gBACE,CAAC,oBAAoB,SAAS,CAAC,KAC/B,CAAC,oBAAoB,UAAU,CAAC;AAEhC;AAEF,oBAAQ,KAAK,OAAO;AACpB,qBAAS,KAAK,OAAO;AACrB,iBAAK;AAAA,cACD;AAAA,gBACA,SAAS,UAAW;AAAA,gBAClB,qBAAiB,OAAO,SAAU,SAAS,SAAU,IAAI;AAAA,gBAC3D,MAAM;AAAA,cACR;AAAA,YACF;AACA,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AChIA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAInB,IAAO,6BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AACR,UAAM,SAAW,YAAU,eAAW,CAAC;AACvC,UAAM,OAAS,YAAU,kBAAc,CAAC;AACxC,UAAM,QAAU,YAAU,kBAAc,CAAC;AAEzC,UAAM,YAAc;AAAA,MAChB;AAAA,QACA;AAAA,QACE;AAAA,UACA;AAAA,UACE,yBAAqB,KAAK,QAAQ,IAAI;AAAA,UACtC,gBAAY;AAAA,QAChB;AAAA,QACE;AAAA,UACA;AAAA,UACE,gBAAY,MAAM;AAAA,UAClB,eAAW,WAAW;AAAA,QAC1B;AAAA,MACF;AAAA,MACE,gBAAY,MAAM;AAAA,MACpB;AAAA,IACF;AAEA,UAAM,iBAAmB;AAAA,MACrB;AAAA,QACA;AAAA,QACE,yBAAqB,KAAK,QAAQ,IAAI;AAAA,QACtC,gBAAY;AAAA,MAChB;AAAA,MACE,gBAAY,MAAM;AAAA,MACpB;AAAA,IACF;AAIA,UAAM,kBAAoB;AAAA,MACtB;AAAA,QACE;AAAA,UACA;AAAA,UACE,qBAAiB,OAAO,MAAQ,gBAAY,CAAC;AAAA,UAC7C;AAAA,YACA;AAAA,YACE,gBAAY,IAAI;AAAA,YAChB,eAAW,WAAW;AAAA,UAC1B;AAAA,QACF;AAAA,QACE,qBAAiB,MAAM,MAAQ,gBAAY,CAAC;AAAA,MAChD;AAAA,MACE,gBAAY,IAAI;AAAA,MAClB;AAAA,IACF;AAEA,UAAM,cAAgB;AAAA,MAClB;AAAA,QACA,CAAG,gBAAY,MAAM,CAAC;AAAA,QACpB,kBAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,MACL,uBAAuB;AAAA,QACrB,KAAK,MAAM;AACT,cAAI,UAAU,MAAM,KAAK,IAAI,GAAG;AAC9B,kBAAM,UAAU,KAAK,MAAM,WAAW,OAAO,QAAS,IAAI;AAE1D,gBACE,YAAY,MAAM,KAAK,WAAW,MAAM,KACxC,oBAAoB,SAAS,GAAG,OAAO,GACvC;AACA,mBAAK,WAAW,WAAY;AAAA,gBACxB,sBAAkB,MAAM,KAAK,SAAU,MAAM,OAAQ;AAAA,cACzD;AACA,mBAAK;AAAA,YACP,WAAW,oBAAoB,SAAS,GAAG,KAAK,GAAG;AACjD,sBAAQ,KAAK,OAAO;AACpB,mBAAK;AAAA,gBACD,sBAAkB,MAAM,KAAK,SAAU,MAAM,OAAQ;AAAA,cACzD;AACA,mBAAK;AAAA,YACP;AAAA,UACF,WAAW,eAAe,MAAM,KAAK,IAAI,GAAG;AAC1C,kBAAM,UAAU,KAAK,MAAM,WAAW,OAAO,QAAS,IAAI;AAC1D,gBAAI,CAAC,oBAAoB,SAAS,CAAC,EAAG;AAEtC,oBAAQ,KAAK,OAAO;AACpB,iBAAK;AAAA,cACD,sBAAkB,MAAM,KAAK,SAAU,MAAM,OAAQ;AAAA,YACzD;AACA,iBAAK;AAAA,UACP,WAAW,gBAAgB,MAAM,KAAK,IAAI,GAAG;AAC3C,iBAAK;AAAA,cACD,sBAAkB,MAAM,KAAK,SAAU,MAAM,OAAQ;AAAA,YACzD;AACA,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC9GA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAInB,IAAO,wCAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AACR,UAAM,SAAW,YAAU,eAAW,CAAC;AACvC,UAAM,SAAW,YAAU,eAAW,CAAC;AACvC,UAAM,WAAa,YAAU,eAAW,CAAC;AACzC,UAAM,QAAU,YAAU,kBAAc,CAAC;AACzC,UAAM,WAAa,YAAmB,aAAS,CAAC;AAEhD,UAAM,gBAAkB;AAAA,MACtB;AAAA,MACE;AAAA,QACE,yBAAqB,KAAK,QAAQ,MAAM;AAAA,QAC1C;AAAA,QACA;AAAA,MACF;AAAA,MACE;AAAA,QACA;AAAA,QACE;AAAA,UACE,gBAAY,MAAM;AAAA,UAClB,gBAAY,QAAQ;AAAA,UACtB;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAGA,UAAM,OAAS,YAAU,OAAK,eAAW,GAAK,qBAAiB,CAAC,CAAC;AACjE,UAAM,gBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,MACE,yBAAqB,KAAO,gBAAY,IAAI,GAAG,KAAK;AAAA,IACxD;AAEA,WAAO;AAAA,MACL,mBAAmB;AAAA,QACjB,KAAK,MAAM;AACT,cAAI,cAAc,MAAM,KAAK,IAAI,GAAG;AAClC,kBAAM,UAAU,KAAK,MAAM,WAAW,OAAO,QAAS,IAAI;AAC1D,gBAAI,CAAC,oBAAoB,SAAS,CAAC,EAAG;AAEtC,oBAAQ,KAAK,OAAO;AACpB,iBAAK;AAAA,cACD;AAAA,gBACA;AAAA,gBACE;AAAA,kBACA,OAAO;AAAA,kBACP,SAAS;AAAA,kBACT,SAAS;AAAA,gBACX;AAAA,gBACA,MAAM;AAAA,cACR;AAAA,YACF;AACA,iBAAK;AAAA,UACP,WAAW,cAAc,MAAM,KAAK,IAAI,GAAG;AACzC,iBAAK;AAAA,cACD,yBAAqB,OAAO,KAAK,SAAU,MAAM,OAAQ;AAAA,YAC7D;AACA,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACxEA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAInB,IAAO,4BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AACR,UAAM,SAAW,YAAU,kBAAc,CAAC;AAC1C,UAAM,SAAW,YAAU,qBAAmB,gBAAY,MAAM,CAAC,CAAC;AAElE,UAAM,gBAAkB;AAAA,MACpB;AAAA,QACA;AAAA,QACE,qBAAiB,OAAO,QAAU,gBAAY,CAAC;AAAA,QAC/C;AAAA,UACA;AAAA,UACE,gBAAY,MAAM;AAAA,UAClB,eAAW,WAAW;AAAA,QAC1B;AAAA,MACF;AAAA,MACE,eAAW,WAAW;AAAA,MACxB;AAAA,IACF;AAEA,UAAM,SAAW,YAAU,eAAW,CAAC;AACvC,UAAM,YAAc,YAAU,qBAAmB,gBAAY,MAAM,CAAC,CAAC;AAErE,UAAM,aAAe;AAAA,MACjB;AAAA,QACA;AAAA,QACE;AAAA,UACA;AAAA,UACE,yBAAqB,KAAK,QAAQ,MAAM;AAAA,UACxC,gBAAY;AAAA,QAChB;AAAA,QACE;AAAA,UACA;AAAA,UACE,gBAAY,MAAM;AAAA,UAClB,eAAW,WAAW;AAAA,QAC1B;AAAA,MACF;AAAA,MACE,eAAW,WAAW;AAAA,MACxB;AAAA,IACF;AAEA,WAAO;AAAA,MACL,uBAAuB;AAAA,QACrB,KAAK,MAAM;AACT,cAAI,cAAc,MAAM,KAAK,IAAI,GAAG;AAClC,mBAAO,QAAS,WAAW;AAC3B,iBAAK;AAAA,cACD;AAAA,gBACA,OAAO;AAAA,gBACP,OAAO,QAAS;AAAA,gBAChB,OAAO,QAAS;AAAA,gBAChB;AAAA,cACF;AAAA,YACF;AACA,iBAAK;AAAA,UACP,WAAW,WAAW,MAAM,KAAK,IAAI,GAAG;AACtC,kBAAM,UAAU,KAAK,MAAM,WAAW,OAAO,QAAS,IAAI;AAC1D,gBAAI,CAAC,oBAAoB,SAAS,CAAC,EAAG;AAEtC,oBAAQ,KAAK,OAAO;AACpB,sBAAU,QAAS,WAAW;AAC9B,iBAAK;AAAA,cACD;AAAA,gBACA,OAAO;AAAA,gBACP,UAAU,QAAS;AAAA,gBACnB,UAAU,QAAS;AAAA,gBACnB;AAAA,cACF;AAAA,YACF;AACA,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AClFA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AASnB,SAAS,OAAO,KAAa;AAC3B,SACE,IACG,WAAW,MAAM,MAAM,EACvB,WAAW,KAAK,KAAK,EACrB,WAAW,KAAK,KAAK,EACrB,WAAW,MAAM,KAAK,EACtB,WAAW,MAAM,KAAK,EACtB,WAAW,MAAM,KAAK,EAEtB,WAAW,MAAM,KAAK,EACtB,WAAW,KAAM,KAAK,EACtB,WAAW,MAAM,KAAK;AAE7B;AAEA,SAAS,KAAK,UAA6B,OAAqB;AAC9D,MAAI,MAAM,SAAS,iBAAiB;AAClC,UAAM,YAAY,SAAS,OAAO,GAAG,EAAE;AACvC,cAAU,MAAM,OAAO,OAAO,MAAM,KAAK;AAAA,EAC3C,WAAW,MAAM,SAAS,mBAAmB;AAC3C,UAAM,YAAY,SAAS,OAAO,GAAG,EAAE;AACvC,UAAM,aAAa,MAAM,OAAO,CAAC;AACjC,cAAU,MAAM,OAAO,WAAW,MAAM;AACxC,aAAS,YAAY,KAAK,GAAG,MAAM,WAAW;AAC9C,aAAS,OAAO,KAAK,GAAG,MAAM,OAAO,MAAM,CAAC,CAAC;AAAA,EAC/C,OAAO;AACL,aAAS,YAAY,KAAK,KAAK;AAC/B,aAAS,OAAO,KAAO,oBAAgB,EAAE,KAAK,GAAG,CAAC,CAAC;AAAA,EACrD;AACF;AAEA,SAAS,QAAQ,UAA6B,OAAqB;AACjE,MAAI,MAAM,SAAS,iBAAiB;AAClC,UAAM,aAAa,SAAS,OAAO,CAAC;AACpC,eAAW,MAAM,MAAM,OAAO,MAAM,KAAK,IAAI,WAAW,MAAM;AAAA,EAChE,OAAO;AACL,aAAS,YAAY,QAAQ,KAAK;AAClC,aAAS,OAAO,QAAU,oBAAgB,EAAE,KAAK,GAAG,CAAC,CAAC;AAAA,EACxD;AACF;AAEA,IAAO,4BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,QAAQ;AAAA,EACf,UAAU;AACR,UAAM,SAAW,YAAU,OAAK,kBAAc,GAAK,oBAAgB,CAAC,CAAC;AACrE,UAAM,gBAAkB;AAAA,MACtB,sBAAsB,QAAQ,QAAQ;AAAA,MACpC,YAAU,kBAAc,CAAC;AAAA,IAC7B;AAEA,WAAO;AAAA,MACL,kBAAkB;AAAA,QAChB,KAAK,MAAM;AACT,cAAI,KAAK,KAAK,aAAa,IAAK;AAEhC,cAAM,sBAAkB,KAAK,KAAK,IAAI,GAAG;AACvC,iBAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK;AACpC,iBAAK,YAAY,KAAK,KAAK,IAAI;AAC/B,iBAAK;AAAA,UACP,WACI,sBAAkB,KAAK,KAAK,KAAK,KACjC,iBAAa,KAAK,KAAK,IAAI,GAC7B;AACA,oBAAQ,KAAK,KAAK,OAAO,KAAK,KAAK,IAAI;AACvC,iBAAK,YAAY,KAAK,KAAK,KAAK;AAChC,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,MACA,gBAAgB;AAAA,QACd,KAAK,MAAM;AACT,cAAI,cAAc,MAAM,KAAK,IAAI,GAAG;AAClC,kBAAM,WAAa;AAAA,cACjB,CAAG,oBAAgB,EAAE,KAAK,GAAG,CAAC,CAAC;AAAA,cAC/B,CAAC;AAAA,YACH;AACA,iBAAK,UAAU,OAAO,OAAQ;AAE9B,uBAAW,OAAO,KAAK,KAAK,WAAW;AACrC,mBAAK,UAAU,GAAmB;AAAA,YACpC;AAEA,iBAAK,YAAY,QAAQ;AACzB,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AClGA,IAAO,oBAAQ,gBAAgB;AAAA,EAC7B,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,YAAY,OAAO,OAAO,kBAAU;AACtC,CAAC;;;ACPD,IAAAC,sBAAA;AAAA,SAAAA,qBAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,YAAYC,SAAO;AAGnB,IAAO,2BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,SAAS,OAAO;AAAA,IACd,aAAa;AAAA,MACX,KAAK,MAAM;AACT,YACE,CAAG,qBAAiB,KAAK,KAAK,UAAU,KACxC,CAAG,qBAAiB,KAAK,KAAK,UAAU,GACxC;AACA,eAAK,KAAK,aAAe,mBAAe,CAAC,KAAK,KAAK,UAAU,CAAC;AAE9D,eAAK;AAAA,QACP;AACA,YAAI,KAAK,KAAK,aAAa,CAAG,qBAAiB,KAAK,KAAK,SAAS,GAAG;AACnE,eAAK,KAAK,YAAc,mBAAe,CAAC,KAAK,KAAK,SAAS,CAAC;AAC5D,eAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,KAAK,MAAM;AACT,YACE,CAAG,qBAAiB,KAAK,KAAK,IAAI,KAClC,CAAG,qBAAiB,KAAK,KAAK,IAAI,GAClC;AACA,eAAK,KAAK,OAAS,mBAAe,CAAC,KAAK,KAAK,IAAI,CAAC;AAElD,eAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB,KAAK,MAAM;AACT,YAAM,yBAAqB,KAAK,KAAK,IAAI,GAAG;AAC1C,eAAK,KAAK,OAAS,mBAAe;AAAA,YAC9B,oBAAgB,KAAK,KAAK,IAAI;AAAA,UAClC,CAAC;AAED,eAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC/CA,SAAS,wBAAwB;AACjC,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAGnB,IAAO,8BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,UAAU;AACR,UAAM,gBAAkB;AAAA,MACpB,kBAAgB,YAAQ,CAAC,UAAU,iBAAiB,KAAK,CAAC,CAAC;AAAA,IAC/D;AACA,UAAM,kBAAoB;AAAA,MACtB,qBAAmB,aAAS,GAAG,eAAe,IAAI;AAAA,MAClD,6BAA2B,aAAS,GAAG,eAAe,IAAI;AAAA,IAC9D;AACA,UAAM,aAAe;AAAA,MACjB,mBAAe,aAAa;AAAA,MAC5B,kBAAc,aAAa;AAAA,MAC3B,iBAAa,QAAW,aAAa;AAAA,MACrC,gBAAY,QAAW,aAAa;AAAA,IACxC;AAEA,WAAO;AAAA,MACL,6CAA6C;AAAA,QAC3C,KAAK,MAAM;AACT,cAAI,CAAC,gBAAgB,MAAM,KAAK,IAAI,EAAG;AACvC,eAAK,KAAK,WAAW;AACrB,eAAK,KAAK,WAAa,eAAW,cAAc,QAAS,KAAK;AAC9D,eAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA,yDAAyD;AAAA,QACvD,KAAK,MAAM;AACT,cAAI,CAAC,WAAW,MAAM,KAAK,IAAI,EAAG;AAClC,cACG,KAAK,SAAS,iBACb,cAAc,QAAS,UAAU,iBAClC,KAAK,SAAS,oBACb,cAAc,QAAS,UAAU;AAEnC;AAEF,eAAK,KAAK,WAAW;AACrB,eAAK,KAAK,MAAQ,eAAW,cAAc,QAAS,KAAK;AACzD,eAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AClDA,YAAYC,SAAO;AAGnB,IAAO,uBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,UAAU;AACR,WAAO;AAAA,MACL,cAAc;AAAA,QACZ,KAAK,MAAM;AACT,gBAAM,EAAE,MAAM,MAAM,MAAM,OAAO,IAAI,KAAK;AAC1C,cAAI,QAAQ,OAAQ;AACpB,eAAK;AAAA,YACD,mBAAe,QAAU,mBAAe,IAAI,GAAG,IAAI;AAAA,UACvD;AACA,eAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACpBA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAGnB,IAAO,mBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,SAAS,MAAM;AACb,UAAM,kBAAoB;AAAA,MACxB;AAAA,MACE,mBAAe,CAAC;AAAA,MAChB,mBAAe,CAAC;AAAA,IACpB;AACA,UAAM,0BAA4B;AAAA,MAChC;AAAA,MACE,oBAAgB,KAAO,mBAAe,CAAC,CAAC;AAAA,MACxC,mBAAe,CAAC;AAAA,IACpB;AAEA,WAAO;AAAA,MACL,kBAAkB;AAAA,QAChB,KAAK,MAAM;AACT,cAAI,KAAK,MAAM,WAAW,YAAY,EAAE,WAAW,KAAK,CAAC,EAAG;AAE5D,cAAI,gBAAgB,MAAM,KAAK,IAAI,GAAG;AACpC,iBAAK,YAAc,eAAW,UAAU,CAAC;AACzC,iBAAK;AAAA,UACP,WAAW,wBAAwB,MAAM,KAAK,IAAI,GAAG;AACnD,iBAAK,YAAc,oBAAgB,KAAO,eAAW,UAAU,CAAC,CAAC;AACjE,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACpCA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAMnB,IAAM,4BAA4B;AAAA,EAChC,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AACT;AAEA,IAAM,6BAA6B;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AACR;AAEA,IAAO,+BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,SAAS,MAAM;AACb,UAAMC,sBAAsB;AAAA,MACxB,OAAG,GAAG,OAAO,OAAO,0BAA0B,CAAC;AAAA,IACnD;AACA,UAAM,iBAAmB,oBAAgB,KAAKA,mBAAiB;AAE/D,UAAMC,qBAAqB;AAAA,MACvB,qBAAmB,OAAG,GAAG,OAAO,OAAO,yBAAyB,CAAC,CAAC;AAAA,IACtE;AACA,UAAM,gBAAkB,oBAAgB,KAAKA,kBAAgB;AAE7D,WAAO;AAAA,MACL,iBAAiB;AAAA,QACf,KAAK,MAAM;AACT,gBAAM,EAAE,SAAS,IAAI,KAAK;AAE1B,cAAI,cAAc,MAAM,KAAK,IAAI,GAAG;AAClC,YAAAA,mBAAiB,QAAS,WACxB,0BACEA,mBAAiB,QACd,QACL;AAEF,iBAAK,YAAYA,mBAAiB,OAAQ;AAC1C,iBAAK;AAAA,UACP,WAAW,eAAe,MAAM,KAAK,IAAI,GAAG;AAC1C,gBAAI,UAAU;AACd,mBAAOD,oBAAkB,MAAM,OAAO,GAAG;AACvC,sBAAQ,WACN,2BACE,QAAQ,QACV;AAEF,sBAAQ,QAAU,oBAAgB,KAAK,QAAQ,KAAK;AACpD,kBAAI,CAACA,oBAAkB,MAAM,QAAQ,IAAI,GAAG;AAC1C,wBAAQ,OAAS,oBAAgB,KAAK,QAAQ,IAAI;AAAA,cACpD;AACA,wBAAU,QAAQ;AAAA,YACpB;AAEA,iBAAK,YAAY,QAAQ;AACzB,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACrEA,SAAS,uBAAuB;AAChC,YAAYE,SAAO;AAInB,IAAO,qBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,SAAS,MAAM;AACb,UAAM,SAAW,YAAU,cAAU,CAAC;AACtC,UAAMC,YAAY,mBAAe,sBAAsB,QAAQ,OAAO,GAAG;AAAA,MACrE,kBAAc,MAAM;AAAA,IACxB,CAAC;AAED,WAAO;AAAA,MACL,gBAAgB;AAAA,QACd,KAAK,MAAM;AACT,cACEA,UAAQ,MAAM,KAAK,IAAI,KACvB,CAAC,KAAK,MAAM,WAAW,QAAQ,EAAE,WAAW,KAAK,CAAC,GAClD;AACA,gBAAI;AACF,mBAAK,MAAM,OAAO,OAAQ;AAC1B,oBAAM,SAAS,gBAAgB,OAAO,OAAQ;AAC9C,mBAAK,YAAY,MAAM;AACvB,mBAAK;AAAA,YACP,QAAQ;AAAA,YAER;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACnCA,SAAS,iBAAiB;AAC1B,YAAYC,SAAO;AAGnB,IAAO,wBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,SAAS,MAAM;AACb,UAAM,UAAU;AAChB,UAAM,aAAa;AAEnB,WAAO;AAAA,MACL,qBAAqB;AAAA,QACnB,KAAK,MAAM;AACT,gBAAMC,cAAa,KAAK,KAAK;AAC7B,cAAI,CAAG,wBAAoBA,WAAU,EAAG;AACxC,cAAIA,YAAW,aAAa,MAAM;AAChC,iBAAK;AAAA,cACH,QAAQ;AAAA,gBACN,MAAMA,YAAW;AAAA,gBACjB,MAAMA,YAAW;AAAA,cACnB,CAAC;AAAA,YACH;AACA,iBAAK;AAAA,UACP,WAAWA,YAAW,aAAa,MAAM;AACvC,iBAAK;AAAA,cACH,WAAW;AAAA,gBACT,MAAMA,YAAW;AAAA,gBACjB,MAAMA,YAAW;AAAA,cACnB,CAAC;AAAA,YACH;AACA,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACrCA,YAAYC,SAAO;AAGnB,IAAO,wBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,UAAU;AACR,UAAM,WAAa,YAAU,gBAAY,CAAC;AAC1C,UAAMC,YAAY;AAAA,MACd,aAAS;AAAA,MACT,aAAS;AAAA,MACT,mBAAe,CAAC,QAAQ,CAAC;AAAA,IAC7B;AAEA,WAAO;AAAA,MACL,aAAa;AAAA,QACX,KAAK,MAAM;AACT,cAAIA,UAAQ,MAAM,KAAK,IAAI,GAAG;AAC5B,iBAAK,KAAK,YAAY,SAAS;AAC/B,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACzBA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAGnB,IAAO,6BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,SAAS,OAAO;AAAA,IACd,oCAAoC;AAAA,MAClC,KAAK,MAAM;AACT,YAAI,CAACC,UAAQ,MAAM,KAAK,IAAI,EAAG;AAC/B,cAAM,YAAY,KAAK,SAAS;AAChC,YACI,uBAAmB,KAAK,MAAM,EAAE,UAAU,IAAI,CAAC,KACjD,CAAC,OAAO,UAAU,UAAU,KAAK,GACjC;AACA;AAAA,QACF;AACA,aAAK,YAAc,gBAAY,UAAU,KAAK,CAAC;AAC/C,aAAK,KAAK;AACV,aAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAMA,YAAY;AAAA,EACd,oBAAgB,KAAO,OAAK,kBAAc,GAAK,mBAAe,CAAC,CAAC;AAAA,EAChE;AAAA,IACE,OAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,OAAO,MAAM,GAAG;AAAA,IAClE;AAAA,MACE,kBAAc;AAAA,MACd,mBAAe;AAAA,MACf,oBAAgB,KAAO,mBAAe,CAAC;AAAA,IAC3C;AAAA,IACE;AAAA,MACE,kBAAc;AAAA,MACd,mBAAe;AAAA,MACf,oBAAgB,KAAO,mBAAe,CAAC;AAAA,IAC3C;AAAA,EACF;AACF;;;ACvCA,IAAO,uBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,SAAS,OAAO;AAAA,IACd,cAAc,MAAM;AAClB,UAAI,KAAK,KAAK,OAAO;AACnB,aAAK,KAAK,QAAQ;AAClB,aAAK;AAAA,MACP;AAAA,IACF;AAAA,IACA,eAAe,MAAM;AACnB,UAAI,KAAK,KAAK,OAAO;AACnB,aAAK,KAAK,QAAQ;AAClB,aAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACF;;;AClBA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAGnB,IAAO,4BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,UAAU;AACR,UAAMC,cAAe,YAAU,kBAAc,CAAC;AAC9C,UAAM,YAAc;AAAA,MAClB;AAAA,MACE,oBAAgB,KAAKA,WAAU;AAAA,IACnC;AACA,UAAM,YAAc,oBAAgB,KAAK,SAAS;AAClD,UAAM,YAAc;AAAA,MAClB;AAAA,QACI,oBAAgB;AAAA,QAChB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAG,4BAA0B,aAAS,GAAG,SAAS,CAAC;AAAA,IACrD;AAEA,WAAO;AAAA,MACL,aAAa;AAAA,QACX,KAAK,MAAM;AACT,cAAI,UAAU,MAAM,KAAK,KAAK,IAAI,GAAG;AACnC,iBAAK,IAAI,MAAM,EAAE,YAAYA,YAAW,OAAQ;AAChD,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,KAAK,MAAM;AACT,cAAI,UAAU,MAAM,KAAK,IAAI,GAAG;AAC9B,iBAAK,YAAc,oBAAgB,KAAKA,YAAW,OAAQ,CAAC;AAC5D,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,MACA,gBAAgB;AAAA,QACd,KAAK,MAAM;AACT,cAAI,UAAU,MAAM,KAAK,IAAI,GAAG;AAC9B,YAAC,KAAK,IAAI,kBAAkB,EAAe;AAAA,cACzCA,YAAW;AAAA,YACb;AACA,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC5DA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAGnB,IAAO,mBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,UAAU;AAGR,UAAM,qBAAuB;AAAA,MACzB,eAAW;AAAA,MACX,qBAAmB,eAAW,GAAK,OAAK,eAAW,GAAG,WAAW,CAAC;AAAA,IACtE;AACA,UAAM,mBAAqB,YAAU,uBAAmB,CAAC;AACzD,UAAM,oBAAsB;AAAA;AAAA,MAExB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,WAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,KAAK,MAAM;AACT,cAAI,CAAC,kBAAkB,MAAM,KAAK,IAAI,EAAG;AAEzC,gBAAM,EAAE,YAAY,IAAI,iBAAiB;AACzC,eAAK,KAAK,QAAQ,YAAY,IAAI;AAClC,gBAAM,WAAW,KAAK,WAAW,sBAAsB,IACnD,YAAY,IAAM,uBAAmB,IACrC;AACJ,eAAK,aAAa,QAAQ;AAC1B,eAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA,qBAAqB;AAAA,QACnB,KAAK,MAAM;AACT,cAAI,CAAG,yBAAqB,KAAK,KAAK,UAAU,EAAG;AAEnD,gBAAM,aAAa,KAAK,KAAK,WAAW,YAAY;AAAA,YAChD;AAAA,UACJ;AACA,eAAK,oBAAoB,UAAU;AACnC,eAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,KAAK,MAAM;AACT,cAAI,CAAG,yBAAqB,KAAK,KAAK,QAAQ,EAAG;AAEjD,gBAAM,EAAE,YAAY,IAAI,KAAK,KAAK;AAClC,eAAK,KAAK,WAAW,YAAY,IAAI;AACrC,gBAAM,aAAa,YAAY,IAAM,uBAAmB;AACxD,eAAK,aAAa,UAAU;AAC5B,eAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA,aAAa;AAAA,QACX,KAAK,MAAM;AACT,cAAI,CAAG,yBAAqB,KAAK,KAAK,IAAI,EAAG;AAE7C,gBAAM,EAAE,YAAY,IAAI,KAAK,KAAK;AAClC,eAAK,KAAK,OAAO,YAAY,IAAI;AACjC,gBAAM,aAAa,YAAY,IAAM,uBAAmB;AACxD,eAAK,aAAa,UAAU;AAC5B,eAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,KAAK,MAAM;AACT,cAAI,CAAG,yBAAqB,KAAK,KAAK,YAAY,EAAG;AAErD,gBAAM,EAAE,YAAY,IAAI,KAAK,KAAK;AAClC,eAAK,KAAK,eAAe,YAAY,IAAI;AACzC,gBAAM,aAAa,YAAY,IAAM,uBAAmB;AACxD,eAAK,aAAa,UAAU;AAC5B,eAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA,gBAAgB;AAAA,QACd,KAAK,MAAM;AACT,cAAI,CAAG,yBAAqB,KAAK,KAAK,QAAQ,EAAG;AAEjD,gBAAM,EAAE,YAAY,IAAI,KAAK,KAAK;AAClC,eAAK,KAAK,WAAW,YAAY,IAAI;AACrC,gBAAM,aAAa,YAAY,IAAM,uBAAmB;AACxD,eAAK,aAAa,UAAU;AAC5B,eAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA,gBAAgB;AAAA,QACd,KAAK,MAAM;AACT,cAAI,CAAG,yBAAqB,KAAK,KAAK,KAAK,EAAG;AAE9C,gBAAM,EAAE,YAAY,IAAI,KAAK,KAAK;AAClC,eAAK,KAAK,QAAQ,YAAY,IAAI;AAClC,gBAAM,aAAa,YAAY,IAAM,uBAAmB;AACxD,eAAK,aAAa,UAAU;AAC5B,eAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA,gBAAgB;AAAA,QACd,KAAK,MAAM;AACT,cAAI,CAAG,yBAAqB,KAAK,KAAK,KAAK,EAAG;AAE9C,gBAAM,EAAE,YAAY,IAAI,KAAK,KAAK;AAClC,eAAK,KAAK,QAAQ,YAAY,IAAI;AAClC,gBAAM,aAAa,YAAY,IAAM,uBAAmB;AACxD,eAAK,aAAa,UAAU;AAC5B,eAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA,cAAc;AAAA,QACZ,KAAK,MAAM;AACT,cAAM,yBAAqB,KAAK,KAAK,IAAI,GAAG;AAC1C,kBAAM,aAAa,KAAK,KAAK,KAAK,YAAY;AAAA,cAC1C;AAAA,YACJ;AACA,iBAAK,KAAK,OAAO;AACjB,iBAAK,aAAa,UAAU;AAC5B,iBAAK;AAAA,UACP;AACA,cACI,yBAAqB,KAAK,KAAK,MAAM,KACvC,KAAK,KAAK,KAAK,SAAS,kBACxB;AACA,kBAAM,EAAE,YAAY,IAAI,KAAK,KAAK;AAClC,iBAAK,KAAK,SAAS,YAAY,IAAI;AACnC,kBAAM,aAAa,YAAY,IAAM,uBAAmB;AACxD,iBAAK,KAAK,OAAS,mBAAe,UAAU;AAC5C,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,MACA,qBAAqB;AAAA,QACnB,KAAK,MAAM;AACT,gBAAM,WAAa,YAAU,uBAAmB,CAAC;AACjD,gBAAMC,YAAY,wBAAoB,QAAW;AAAA,YAC7C,uBAAmB,QAAW,QAAQ;AAAA,UAC1C,CAAC;AACD,cAAI,CAACA,UAAQ,MAAM,KAAK,IAAI,EAAG;AAE/B,gBAAM,EAAE,YAAY,IAAI,SAAS;AACjC,eAAK,KAAK,aAAa,CAAC,EAAE,OAAO,YAAY,IAAI;AACjD,gBAAM,aAAa,YAAY,IAAM,uBAAmB;AACxD,eAAK,mBAAmB,GAAG,aAAa,UAAU;AAClD,eAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA,oBAAoB;AAAA,QAClB,KAAK,MAAM;AACT,gBAAM,EAAE,YAAY,IAAI,KAAK;AAC7B,cAAI,YAAY,MAAM,CAAC,SAAS,YAAY,MAAM,IAAI,CAAC,GAAG;AACxD,iBAAK,YAAY,YAAY,GAAG,EAAE,CAAE;AACpC,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC/KA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAGnB,IAAMC,YAAY;AAAA,EACd,wBAAoB,OAAS,YAAU,uBAAqB,eAAW,CAAC,CAAC,CAAC;AAC9E;AAKA,IAAO,8BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,SAAS,OAAO;AAAA,IACd,cAAc;AAAA,MACZ,KAAK,MAAM;AACT,YAAI,CAACA,UAAQ,MAAM,KAAK,IAAI,EAAG;AAC/B,cAAM,EAAE,MAAM,MAAM,OAAO,IAAI,KAAK;AACpC,cAAM,EAAE,aAAa,IAAI;AAEzB,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,gBAAM,aAAa,aAAa,CAAC;AACjC,gBAAM,UAAU,KAAK,MAAM;AAAA,YACxB,WAAW,GAAoB;AAAA,UAClC;AACA,cAAI,CAAC,QAAS;AAEd,gBAAM,uBACJ,QAAQ,mBAAmB;AAAA,YAAK,CAAC,cAC/B,UAAU,KAAK,CAAC,MAAM,EAAE,SAAS,QAAQ,EAAE,SAAS,MAAM;AAAA,UAC5D,KACA,QAAQ,eAAe;AAAA,YAAK,CAAC,cAC3B,UAAU,KAAK,CAAC,MAAM,EAAE,SAAS,QAAQ,EAAE,SAAS,MAAM;AAAA,UAC5D;AACF,cAAI,qBAAsB;AAE1B,eAAK,aAAe,wBAAoB,OAAO,CAAC,UAAU,CAAC,CAAC;AAC5D,uBAAa,MAAM;AACnB;AACA,eAAK;AAAA,QACP;AAEA,YAAI,aAAa,WAAW,EAAG,MAAK,IAAI,MAAM,EAAE,OAAO;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AACF;;;AChDA,YAAYC,SAAO;AAGnB,IAAO,sCAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,SAAS,OAAO;AAAA,IACd,qBAAqB;AAAA,MACnB,KAAK,MAAM;AACT,YAAI,KAAK,KAAK,aAAa,SAAS,GAAG;AAErC,cAAI,KAAK,QAAQ,UAAU,KAAK,WAAW,eAAe,GAAG;AAC3D,gBACE,CAAC,KAAK,WAAW,KAAK,QACtB,CAAC,KAAK,WAAW,KAAK,UACtB,KAAK,KAAK,SAAS,OACnB;AACA,mBAAK,WAAW;AAAA,gBACd,KAAK,KAAK,aAAa;AAAA,kBAAI,CAAC,gBACxB,wBAAoB,KAAK,KAAK,MAAM,CAAC,WAAW,CAAC;AAAA,gBACrD;AAAA,cACF;AACA,mBAAK,OAAO;AACZ,mBAAK;AAAA,YACP;AAAA,UACF,OAAO;AACL,gBAAI,KAAK,WAAW,yBAAyB,GAAG;AAC9C,mBAAK,WAAW;AAAA,gBACd,KAAK,KAAK,aAAa;AAAA,kBAAI,CAAC,gBACxB;AAAA,oBACE,wBAAoB,KAAK,KAAK,MAAM,CAAC,WAAW,CAAC;AAAA,kBACrD;AAAA,gBACF;AAAA,cACF;AAAA,YACF,OAAO;AACL,mBAAK;AAAA,gBACH,KAAK,KAAK,aAAa;AAAA,kBAAI,CAAC,gBACxB,wBAAoB,KAAK,KAAK,MAAM,CAAC,WAAW,CAAC;AAAA,gBACrD;AAAA,cACF;AAAA,YACF;AACA,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC/CA,SAAS,aAAAC,kBAAiB;AAC1B,YAAYC,SAAO;AAGnB,IAAO,wBAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,UAAU;AACR,UAAM,OAAS,YAAU,kBAAc,CAAC;AACxC,UAAM,aAAe,YAAU,kBAAc,CAAC;AAC9C,UAAM,YAAc,YAAU,kBAAc,CAAC;AAC7C,UAAM,cAAgB,0BAAsB,MAAM,YAAY,SAAS;AAEvE,UAAM,UAAUD;AAChB,UAAM,gBAAgBA;AAEtB,WAAO;AAAA,MACL,qBAAqB;AAAA,QACnB,KAAK,MAAM;AACT,cAAI,YAAY,MAAM,KAAK,KAAK,UAAU,GAAG;AAC3C,iBAAK;AAAA,cACH,QAAQ;AAAA,gBACN,MAAM,KAAK;AAAA,gBACX,YAAY,WAAW;AAAA,gBACvB,WAAW,UAAU;AAAA,cACvB,CAAC;AAAA,YACH;AACA,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,KAAK,MAAM;AACT,cAAI,YAAY,MAAM,KAAK,KAAK,QAAQ,GAAG;AACzC,iBAAK;AAAA,cACH,cAAc;AAAA,gBACZ,MAAM,KAAK;AAAA,gBACX,YAAY,WAAW;AAAA,gBACvB,WAAW,UAAU;AAAA,cACvB,CAAC;AAAA,YACH;AACA,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC/CA,YAAYE,SAAO;AACnB,YAAYC,SAAO;AAGnB,IAAM,eAAe;AAAA,EACnB,KAAK;AAAA,EACL,KAAK;AACP;AAEA,IAAO,2BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,UAAU;AACR,UAAM,WAAa,YAAU,OAAG,KAAc,GAAY,CAAC;AAC3D,UAAM,WAAa,YAAU,kBAAc,CAAC;AAC5C,UAAMC,YAAY;AAAA,MAChB;AAAA,MACE,oBAAgB,UAAU,QAAQ;AAAA,MAClC,kBAAc,GAAG;AAAA,IACrB;AACA,WAAO;AAAA,MACL,kBAAkB;AAAA,QAChB,KAAK,MAAM;AACT,cAAI,CAACA,UAAQ,MAAM,KAAK,IAAI,EAAG;AAC/B,eAAK;AAAA,YACD;AAAA,cACA,aAAa,SAAS,OAAQ;AAAA,cAC5B,oBAAgB,UAAU,SAAS,OAAQ;AAAA,cAC3C,kBAAc,WAAW;AAAA,YAC7B;AAAA,UACF;AACA,eAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACpCA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAGnB,IAAO,4BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,UAAU;AACR,UAAM,WAAa,YAAU,kBAAc,CAAC;AAC5C,UAAMC,YAAY;AAAA,MACd,oBAAkB,OAAG,QAAQ,KAAK,QAAQ,GAAG,QAAQ;AAAA,IACzD;AACA,UAAM,aAAe,oBAAkB,oBAAgB,QAAQ,QAAQ,CAAC;AACxE,WAAO;AAAA,MACL,qBAAqB;AAAA,QACnB,KAAK,MAAM;AACT,cAAI,CAACA,UAAQ,MAAM,KAAK,IAAI,EAAG;AAC/B,eAAK,YAAY,SAAS,OAAQ;AAClC,eAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,KAAK,MAAM;AACT,cAAI,CAAC,WAAW,MAAM,KAAK,IAAI,EAAG;AAClC,eAAK,YAAY,SAAS,OAAQ;AAClC,eAAK,YAAc,oBAAgB,CAAC;AACpC,eAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC/BA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAGnB,IAAO,4BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,SAAS,OAAO;AAAA,IACd,gBAAgB,MAAM;AACpB,UAAIC,aAAY,MAAM,KAAK,IAAI,GAAG;AAChC,aAAK,YAAc,mBAAe,IAAI,CAAC;AACvC,aAAK;AAAA,MACP,WAAWC,cAAa,MAAM,KAAK,IAAI,GAAG;AACxC,aAAK,YAAc,mBAAe,KAAK,CAAC;AACxC,aAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAMD,eAAgB;AAAA,EAClB,oBAAgB,KAAO,mBAAe,CAAC,CAAC;AAAA,EACxC,oBAAgB,KAAO,oBAAgB,KAAO,mBAAe,CAAC,CAAC,CAAC;AAAA,EAChE,oBAAgB,KAAO,oBAAgB,KAAO,oBAAgB,CAAC,CAAC,CAAC,CAAC;AACtE;AAEA,IAAMC,gBAAiB;AAAA,EACnB,oBAAgB,KAAO,mBAAe,CAAC,CAAC;AAAA,EACxC,oBAAgB,KAAO,oBAAgB,CAAC,CAAC,CAAC;AAC9C;;;AC7BA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAGnB,IAAO,4BAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,OAAO;AAAA,EACP,SAAS,MAAM;AACb,UAAMC,YAAY,oBAAgB,QAAU,mBAAe,CAAC,CAAC;AAC7D,WAAO;AAAA,MACL,iBAAiB;AAAA,QACf,KAAK,MAAM;AACT,cACEA,UAAQ,MAAM,KAAK,IAAI,KACvB,CAAC,KAAK,MAAM,WAAW,aAAa,EAAE,WAAW,KAAK,CAAC,GACvD;AACA,iBAAK,YAAc,eAAW,WAAW,CAAC;AAC1C,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACxBA,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAKnB,IAAM,oBAAoB;AAAA,EACxB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AAEA,IAAO,eAAQ;AAAA,EACb,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,SAAS,MAAM;AACb,UAAM,YAAc;AAAA,MAChB,kBAAc;AAAA,MACd,mBAAe;AAAA,MACf;AAAA,QACA;AAAA,QACE,OAAK,mBAAe,GAAK,eAAW,UAAU,CAAC;AAAA,MACnD;AAAA,MACE,mBAAe;AAAA,MACf,gBAAY;AAAA,MACZ,eAAW,WAAW;AAAA,MACtB,eAAW,KAAK;AAAA,MAChB,eAAW,UAAU;AAAA,IACzB;AACA,UAAMC,YAAY;AAAA,MACd,OAAG,GAAG,OAAO,OAAO,iBAAiB,CAAC;AAAA,MACxC;AAAA,MACE,YAAQ,CAAC,SAAS,CAAC,UAAU,MAAM,IAAI,CAAC;AAAA,IAC5C;AAEA,WAAO;AAAA,MACL,kBAAkB;AAAA,QAChB,KAAK,MAAM;AACT,cAAIA,UAAQ,MAAM,KAAK,IAAI,GAAG;AAC5B,iBAAK;AAAA,cACD;AAAA,gBACA,kBACE,KAAK,KAAK,QACZ;AAAA,gBACA,KAAK,KAAK;AAAA,gBACV,KAAK,KAAK;AAAA,cACZ;AAAA,YACF;AACA,iBAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC5DA,IAAO,mBAAQ,gBAAgB;AAAA,EAC7B,MAAM;AAAA,EACN,MAAM,CAAC,MAAM;AAAA,EACb,YAAY,OAAO,OAAOC,mBAAU;AACtC,CAAC;;;ACPD,SAAS,aAAa;;;ACCtB,YAAYC,SAAO;AACnB,YAAYC,SAAO;;;ACFnB,SAAS,aAAa;AAGtB,IAAM,EAAE,SAAS,MAAM,SAAS,IAAI;AAE7B,SAAS,aAAa,MAAc,IAAoB;AAC7D,MAAI,GAAG,WAAW,eAAe,EAAG,QAAO,GAAG,QAAQ,iBAAiB,EAAE;AACzE,QAAMC,gBAAe,SAAS,QAAQ,IAAI,GAAG,EAAE;AAC/C,SAAOA,cAAa,WAAW,GAAG,IAAIA,gBAAe,OAAOA;AAC9D;AAQO,SAAS,sBACd,MACA,OACwB;AACxB,QAAM,QAAQ,iBAAiB,MAAM,KAAK;AAC1C,QAAM,KAAK,IAAI;AAEf,QAAM,aAAa,OAAO,OAAO,KAAK,EAAE;AAAA,IACtC,CAAC,KAAK,SAAS,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,EAAE,MAAM;AAAA,IACpD;AAAA,EACF;AAEA,QAAM,SAAS,MAAM,aAAa,CAAC,EAChC,KAAK,CAAC,EACN,IAAI,CAAC,GAAG,MAAM,MAAM,CAAC,EAAE,EACvB,KAAK,GAAG;AAEX,SAAO,OAAO;AAAA,IACZ,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAC,IAAI,IAAI,MAAM;AACxC,YAAM,UAAU,KAAK,WAAW,eAAe,IAC3C,OACA,KAAK,QAAQ,IAAI;AACrB,aAAO,CAAC,IAAI,OAAO;AAAA,IACrB,CAAC;AAAA,EACH;AACF;AAKA,SAAS,iBACP,OACA,OACA,MAAM,KACN,QAAgC,CAAC,GACjC;AACA,QAAM,UAAU,OAAO,QAAQ,MAAM,KAAK,CAAC;AAE3C,aAAW,CAAC,IAAI,IAAI,KAAK,SAAS;AAChC,UAAM,aAAa,OAAO,OAAO,OAAO,EAAE;AAC1C,QAAI,WAAY;AAEhB,QAAI;AACJ,QAAI,KAAK,WAAW,GAAG,GAAG;AACxB,aAAO,KAAK,KAAK,IAAI;AACrB,UAAI,CAAC,KAAK,SAAS,KAAK,EAAG,SAAQ;AAAA,IACrC,OAAO;AACL,aAAO,KAAK,gBAAgB,MAAM,UAAU;AAAA,IAC9C;AACA,UAAM,EAAE,IAAI;AAEZ,UAAM,SAAS,KAAK,SAAS,KAAK,IAAI,QAAQ,IAAI,IAAI;AACtD,qBAAiB,OAAO,IAAI,QAAQ,KAAK;AAAA,EAC3C;AAEA,SAAO;AACT;;;ACvEA,SAAS,WAAAC,UAAS,QAAAC,OAAM,WAAW,WAAW;AAGvC,IAAM,SAAN,MAAa;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EAEA,YACE,MACA,SACA,SACA;AACA,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB;AAAA,EAEA,cAAc,UAAoD;AAChE,UAAM,eAAe,OAAO,KAAK,QAAQ;AACzC,QAAI,aAAa,WAAW,EAAG;AAE/B,UAAM,iBAAiB,IAAI,IAAI,YAAY;AAE3C,eAAWC,WAAU,KAAK,QAAQ,OAAO,GAAG;AAC1C,uBAASA,QAAO,KAAK;AAAA,QACnB,MAAM,MAAM;AACV,qBAAW,eAAe,cAAc;AACtC,gBAAI,SAAS,WAAW,EAAE,MAAM,KAAK,IAAI,GAAG;AAC1C,kBAAI,eAAe,IAAI,WAAW,GAAG;AACnC,+BAAe,OAAO,WAAW;AAAA,cACnC,OAAO;AACL,sBAAM,IAAI,MAAM,WAAW,WAAW,mBAAmB;AAAA,cAC3D;AACA,oBAAM,eAAe,YAAY,WAAW,IAAI,IAC5C,cACA,gBAAgB,WAAW;AAC/B,cAAAA,QAAO,OAAO;AACd,mBAAK,KAAK;AACV;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,KAAK,MAA6B;AACtC,UAAM,aAAa;AAAA,MACjB,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,SAAS,MAAM,KAAK,KAAK,QAAQ,OAAO,GAAG,CAACA,aAAY;AAAA,QACtD,IAAIA,QAAO;AAAA,QACX,MAAMA,QAAO;AAAA,MACf,EAAE;AAAA,IACJ;AAEA,UAAM,EAAE,OAAO,UAAU,IAAI,MAAM,OAAO,kBAAkB;AAC5D,UAAM,MAAM,MAAM,EAAE,WAAW,KAAK,CAAC;AAErC,UAAM;AAAA,MACJD,MAAK,MAAM,aAAa;AAAA,MACxB,KAAK,UAAU,YAAY,MAAM,CAAC;AAAA,MAClC;AAAA,IACF;AAEA,UAAM,QAAQ;AAAA,MACZ,MAAM,KAAK,KAAK,QAAQ,OAAO,GAAG,OAAOC,YAAW;AAClD,cAAM,aAAa,UAAUD,MAAK,MAAMC,QAAO,IAAI,CAAC;AACpD,YAAI,CAAC,WAAW,WAAW,OAAO,GAAG,GAAG;AACtC,gBAAM,IAAI,MAAM,4BAA4BA,QAAO,IAAI,EAAE;AAAA,QAC3D;AACA,cAAM,MAAMF,SAAQ,UAAU,GAAG,EAAE,WAAW,KAAK,CAAC;AACpD,cAAM,UAAU,YAAYE,QAAO,MAAM,MAAM;AAAA,MACjD,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,kBAAwB;AAAA,EAAC;AAC3B;;;ACnFO,IAAM,mBAAN,cAA+B,OAAO;AAAA,EAC3C,YAAY,SAAiB,SAAwC;AACnE,UAAM,cAAc,SAAS,OAAO;AAAA,EACtC;AACF;;;ACJO,IAAM,SAAN,MAAa;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,EACA;AAAA,EAEA,YAAY,IAAY,KAAa,SAAkB;AACrD,SAAK,KAAK;AACV,SAAK,MAAM;AACX,SAAK,UAAU;AACf,SAAK,OAAO,KAAK,UAAU,UAAU,EAAE;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAyB;AACvB,SAAK,QAAQ,SAAS,KAAK,GAAG;AAC9B,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,OAAe;AACjB,WAAO,KAAK,SAAS,KAAK,eAAe;AAAA,EAC3C;AAAA,EAEA,IAAI,KAAK,MAAc;AACrB,SAAK,QAAQ;AAAA,EACf;AACF;;;AChCO,IAAM,mBAAN,cAA+B,OAAO;AAAA,EAC3C;AAAA,EAEA,YACE,IACA,KACA,SACA,cACA;AACA,UAAM,IAAI,KAAK,OAAO;AACtB,SAAK,eAAe;AAAA,EACtB;AACF;;;ALLO,IAAM,mBAAmB;AAAA,EAC9B,MAAM;AAAA,EACN,MAAM,CAAC,QAAQ;AAAA,EACf,OAAO;AAAA,EACP,QAAQ,SAAS;AACf,UAAM,UAAU,oBAAI,IAA8B;AAElD,UAAM,QAAU;AAAA,MACZ;AAAA,QACE;AAAA,UACE,mBAAe;AAAA,UACf,oBAAgB;AAAA;AAAA,YAEd,uBAAmB;AAAA;AAAA,YAEnB;AAAA,cACE;AAAA,gBACE;AAAA,kBACA,SAAS;AAAA,kBACP;AAAA,oBACE,mBAAe;AAAA,oBACf,eAAW,WAAW;AAAA,oBACtB,kBAAc;AAAA,kBAClB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,UAAM,iBAAmB,YAAU,mBAAe,CAAC;AAEnD,UAAMC,YAAY;AAAA,MACd;AAAA;AAAA,QAEE,uBAAmB,QAAW;AAAA,UAC5B,eAAW;AAAA,UACX,eAAW;AAAA,UACX,eAAW;AAAA,QACf,CAAC;AAAA;AAAA,QAED;AAAA,UACE,CAAC;AAAA,UACC,mBAAe;AAAA,YACb,wBAAoB,QAAW;AAAA,cAC7B,eAAW;AAAA,cACX,eAAW;AAAA,cACX,eAAW;AAAA,YACf,CAAC;AAAA,YACC,oBAAkB,eAAW,CAAC;AAAA,UAClC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA;AAAA,QACI,qBAAiB,KAAK;AAAA,QACtB,qBAAiB;AAAA,QACjB,oBAAgB,CAAC,cAAc,CAAC;AAAA,MACpC;AAAA,IACF;AAEA,WAAO;AAAA,MACL,eAAe,MAAM;AACnB,YAAI,CAACA,UAAQ,MAAM,KAAK,IAAI,EAAG;AAC/B,aAAK,KAAK;AAEV,cAAM,UAAU,eAAe,QAAS,MAAM,SAAS;AAEvD,cAAM,cAAc,KAAK;AAAA,UACvB,MAAM,YAAa,KAAK,GAAG;AAAA,QAC7B;AAEA,cAAM,iBAAyD,CAAC;AAEhE,mBAAW,iBAAiB,aAAa;AACvC,gBAAM,KACJ,cAAc,KAAK,IACnB,MAAM,SAAS;AACjB,gBAAM,KAAK,cAAc;AAAA,YACvB;AAAA,UACF;AAEA,gBAAM,eAAwC,eAAe,EAAE,IAC7D,CAAC;AACH,gBAAM,uBACJ,cAAc;AAAA,YACZ;AAAA,UACF,EACA,KAAK;AAEP,qBAAW,cAAc,sBAAsB;AAE7C,gBACE,WAAW,MAAM,SAAS,oBAC1B,WAAW,MAAM,SAAS;AAE1B;AAEF,kBAAM,WAAW,YAAY,WAAW,GAAG;AAC3C,kBAAM,QAAQ,WAAW,MAAM,MAAM,SAAS;AAC9C,yBAAa,KAAK,IAAI;AAAA,UACxB;AAEA,2BAAiB,IAAI,CAAC,WAAW,UAAU,SAAS,CAAC;AACrD,gBAAMC,QAAS,SAAO,YAAQ,GAAG,KAAK,KAAK,IAAI,CAAC;AAChD,gBAAMC,UAAS,IAAI;AAAA,YACjB;AAAA,YACAD;AAAA,YACA,OAAO;AAAA,YACP;AAAA,UACF;AACA,kBAAQ,IAAI,GAAG,SAAS,GAAGC,OAAM;AAAA,QACnC;AAEA,cAAM,gBAAgB,sBAAsB,gBAAgB,OAAO;AAEnE,mBAAWA,WAAU,QAAQ,OAAO,GAAG;AACrC,UAAAA,QAAO,OAAO,cAAcA,QAAO,EAAE;AAAA,QACvC;AAEA,YAAI,QAAQ,OAAO,GAAG;AACpB,kBAAS,SAAS,IAAI,iBAAiB,SAAS,OAAO;AAAA,QACzD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AMvIA,YAAYC,SAAO;AACnB,YAAYC,SAAO;;;ACAnB,YAAYC,SAAO;AACnB,YAAYC,SAAO;;;ACHnB,SAAS,aAAAC,kBAAiB;AAG1B,YAAYC,SAAO;AACnB,YAAYC,SAAO;AAInB,IAAM,uBAAuBC;AAC7B,IAAM,sBAAsBA;AAerB,SAAS,WAAWC,SAA6B;AAEtD,QAAM,wBAA0B;AAAA,IAC5B,mBAAe,sBAAsB,WAAW,GAAG,GAAG,CAAG,eAAW,CAAC,CAAC;AAAA,EAC1E;AAEA,QAAM,cAAgB,YAAU,eAAW,CAAC;AAC5C,QAAM,eAAiB,YAAU,cAAU,CAAC;AAC5C,QAAM,gBAAkB,YAAU,kBAAc,CAAC;AAEjD,QAAM,sBAAwB;AAAA,IAC1B,mBAAe,sBAAsB,WAAW,GAAG,GAAG;AAAA,MACtD;AAAA,MACE,kBAAc,YAAY;AAAA,MAC1B;AAAA,QACA;AAAA,QACA,CAAC;AAAA,QACC,mBAAe,CAAG,oBAAgB,aAAa,CAAC,CAAC;AAAA,MACrD;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,wBAA0B;AAAA,IAC5B,gBAAY,WAAW;AAAA,IACvB,qBAAiB,CAAC,CAAC;AAAA,EACvB;AAEA,QAAM,aAAe;AAAA,IACjB;AAAA,MACE;AAAA,QACE,eAAW;AAAA,QACX,4BAAwB,CAAC,GAAK,kBAAc,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AAEA,QAAM,uBAAyB;AAAA,IAC3B,mBAAe,sBAAsB,WAAW,GAAG,GAAG;AAAA,MACtD;AAAA,MACE,qBAAiB,UAAU;AAAA,IAC/B,CAAC;AAAA,EACH;AAGA,QAAM,kBAAoB,YAAU,eAAW,CAAC;AAChD,QAAM,mBAAqB,YAAU,cAAU,CAAC;AAChD,QAAMC,kBAAmB,wBAAoB,QAAW;AAAA,IACpD;AAAA,MACA;AAAA,MACE,mBAAiB,eAAW,SAAS,GAAG;AAAA,QACtC,mBAAe,gBAAgB;AAAA,MACnC,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAGD,QAAM,aAAe;AAAA,IACjB;AAAA,MACA;AAAA,MACE,eAAW,QAAQ;AAAA,MACnB,mBAAe,sBAAsB,WAAW,KAAK,CAAC;AAAA,IAC1D;AAAA,EACF;AAEA,mBAASD,QAAO,KAAK;AAAA,IACnB,MAAM,MAAM;AAEV,UAAI,KAAK,YAAY,WAAY,QAAO,KAAK,KAAK;AAElD,UAAI,sBAAsB,MAAM,KAAK,IAAI,GAAG;AAC1C,QAAAA,QAAO,IAAI,QAAQ,aAAa;AAChC,aAAK,OAAO;AAAA,MACd,WACEA,QAAO,IAAI,QAAQ,eAAe,YAClCC,gBAAe,MAAM,KAAK,IAAI,GAC9B;AACA,aAAK;AAAA,UACH,qBAAqB;AAAA,YACnB,MAAM,gBAAgB;AAAA,YACtB,MAAM,OAAO,iBAAiB,OAAO;AAAA,UACvC,CAAC;AAAA,QACH;AAAA,MACF,WAAW,qBAAqB,MAAM,KAAK,IAAI,GAAG;AAChD,cAAM,iBAAiB,KAAK,MAAM,WAAW,YAAY,QAAS,IAAI;AACtE,cAAM,cAAc,sBAAsB;AAAA,UACxC,gBAAgB,KAAK;AAAA,QACvB,IACK,gBAAgB,KAAK,KAAK,OAC3B;AAEJ,mBAAW,YAAY,WAAW,SAAU;AAC1C,gBAAM,cAAc,SAAS;AAC7B,gBAAMC,iBAAiB,SAAS,MAC7B;AACH,cAAI,aAAa;AACf,wBAAY,WAAW;AAAA,cACnB,mBAAe,aAAaA,cAAa;AAAA,YAC7C;AAAA,UACF,OAAO;AACL,2BAAe,MAAMA,gBAAe,YAAY,IAAI;AAAA,UACtD;AAAA,QACF;AAEA,aAAK,OAAO;AAAA,MACd,WAAW,oBAAoB,MAAM,KAAK,IAAI,GAAG;AAC/C,uBAAe,MAAM,cAAc,SAAU,aAAa,OAAQ;AAClE,aAAK,OAAO;AAAA,MACd,WAAW,WAAW,MAAM,KAAK,IAAI,GAAG;AACtC,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,eACP,cACA,OACA,YACA;AACA,MAAI,MAAM,SAAS,cAAc;AAC/B,UAAM,UAAU,aAAa,MAAM,WAAW,MAAM,IAAI;AACxD,QAAI,CAAC,QAAS;AAEd,UAAM,cAAc;AAAA,MAClB,QAAQ;AAAA,MACN;AAAA,QACE,wBAAoB;AAAA,QACpB,qBAAiB;AAAA,QACjB,wBAAoB;AAAA,MACxB;AAAA,IACF;AACA,QAAI,CAAC,YAAa;AAElB,QAAI,eAAe,WAAW;AAE5B,kBAAY;AAAA,QACR;AAAA,UACE,0BAAsB,YAAY,IAAI,IACpC,YAAY,KAAK,aAAa,CAAC,EAAE,OACjC,YAAY;AAAA,QAClB;AAAA,MACF;AAAA,IACF,OAAO;AAEL,iBAAW,SAAS,UAAU;AAC9B,kBAAY,YAAc,2BAAuB,YAAY,IAAI,CAAC;AAAA,IACpE;AAAA,EACF,WAAW,eAAe,WAAW;AACnC,iBAAa,YAAc,6BAAyB,KAAK,CAAC;AAAA,EAC5D,OAAO;AACL,iBAAa;AAAA,MACX,oBAAoB,EAAE,MAAQ,eAAW,UAAU,GAAG,OAAO,MAAM,CAAC;AAAA,IACtE;AAAA,EACF;AACF;;;AClLA,SAAS,cAAAC,mBAAkB;AAG3B,YAAYC,SAAO;AA+BZ,SAAS,sBAAsB,QAA6B;AACjE,WAAS,kBAAkB,MAAgB;AAEzC,UAAM,UAAU,KAAK,MAAM,WAAW,UAAU,QAAS,IAAI;AAC7D,UAAM,aAAa,SAAS,KAAK;AACjC,QAAI,kBAAkB,MAAM,UAAU,GAAG;AACvC,aAAO,OAAO,QAAQ,IAAI,iBAAiB,QAAS,MAAM,SAAS,CAAC;AAAA,IACtE;AAAA,EACF;AAEA,QAAM,mBAAqB,YAAU,mBAAe,CAAC;AAErD,QAAM,oBAAsB;AAAA,IACxB,eAAW;AAAA,IACX,mBAAiB,eAAW,SAAS,GAAG,CAAC,gBAAgB,CAAC;AAAA,EAC9D;AAGA,QAAM,YAAc,YAAU,eAAW,CAAC;AAE1C,QAAM,gBAAkB,YAAU,eAAW,CAAC;AAE9C,QAAM,WAAa,mBAAe,sBAAsB,WAAW,GAAG,GAAG;AAAA,IACvE;AAAA,EACF,CAAC;AAED,QAAM,wBAA0B,uBAAmB,eAAe,QAAQ;AAG1E,QAAM,mCAAqC;AAAA,IACzC,sBAAsB,UAAU,GAAG;AAAA,IACjC,mBAAe,UAAU,CAAC,CAAC;AAAA,EAC/B;AAEA,QAAM,qBAAqBC;AAE3B,SAAO,QAAQ,QAAQ,CAACC,YAAW;AACjC,qBAASA,QAAO,KAAK;AAAA,MACnB,kCAAkC,MAAM;AACtC,YAAI,iCAAiC,MAAM,KAAK,IAAI,GAAG;AAErD,gBAAM,iBAAiB,kBAAkB,IAAI;AAC7C,cAAI,gBAAgB,IAAI,QAAQ,eAAe,UAAU;AACvD,iBAAK;AAAA,cACH,mBAAmB,EAAE,QAAQ,UAAU,QAAS,CAAC;AAAA,YACnD;AAAA,UACF,OAAO;AACL,iBAAK,YAAY,UAAU,OAAQ;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AAAA,MACA,mBAAmB,MAAM;AACvB,YAAI,sBAAsB,MAAM,KAAK,IAAI,GAAG;AAE1C,gBAAM,iBAAiB,kBAAkB,IAAI;AAC7C,gBAAM,OAAO,KAAK,IAAI,MAAM;AAC5B,cAAI,gBAAgB,IAAI,QAAQ,eAAe,UAAU;AACvD,iBAAK;AAAA,cACH,mBAAmB,EAAE,QAAQ,UAAU,QAAS,CAAC;AAAA,YACnD;AAAA,UACF,OAAO;AACL,iBAAK,YAAY,UAAU,OAAQ;AAAA,UACrC;AAGA,gBAAM,UAAU,KAAK,MAAM,cAAc,cAAc,QAAS,IAAI;AACpE,mBAAS,eAAe,QAAQ,CAAC,YAAY;AAC3C,gBACE,QAAQ,YAAY,iBAAiB,KACrC,QAAQ,YAAY,mBAAmB,GACvC;AACA,sBAAQ,WAAW,YAAY,OAAO;AAAA,YACxC;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC;AACH;;;ACjHA,SAAS,aAAAC,kBAAiB;AAE1B,YAAYC,SAAO;AAInB,IAAM,WAAWC;AAcV,SAAS,oBAAoBC,SAA6B;AAC/D,QAAM,EAAE,SAAAC,SAAQ,IAAID,QAAO;AAC3B,QAAM,UAAuB,CAAC;AAE9B,QAAM,OAAS,YAAU,mBAAe,CAAC;AACzC,QAAM,SAAW,YAAU,YAAU,eAAW,CAAC,CAAC;AAClD,QAAM,OAAS;AAAA,IACX,YAAU,OAAK,mBAAe,GAAK,eAAW,SAAS,CAAC,GAAK,cAAU,CAAC;AAAA,EAC5E;AACA,QAAME,YAAY;AAAA,IACd;AAAA,MACA;AAAA,QACI,uBAAmB,QAAW,QAAQ,IAAI;AAAA,QAC5C;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,aAAW,QAAQD,SAAQ,MAAM;AAC/B,QAAIC,UAAQ,MAAM,IAAI,GAAG;AACvB,YAAM,OAAO,OAAO,QAAS;AAAA,QAAI,CAAC,OAAO,MACvC,SAAS,EAAE,MAAM,OAAO,MAAM,KAAK,QAAS,IAAI,CAAC,EAAE,CAAC;AAAA,MACtD;AACA,cAAQ,KAAK,GAAG,IAAI;AACpB,cAAQ,KAAK,GAAG,KAAK,QAAS,IAAI;AAAA,IAEpC,OAAO;AACL,cAAQ,KAAK,IAAI;AAAA,IACnB;AAAA,EACF;AACA,EAAAD,SAAQ,OAAO;AACjB;;;AHzCO,IAAM,gBAAN,cAA4B,OAAO;AAAA,EACxC,YAAY,SAAiB,SAAqC;AAChE,UAAM,WAAW,SAAS,OAAO;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAwB;AACtB,SAAK,QAAQ,QAAQ,mBAAmB;AACxC,SAAK,QAAQ,QAAQ,UAAU;AAC/B,0BAAsB,IAAI;AAC1B,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAKQ,sBAAsB;AAC5B,UAAM,YAAc,YAAU,OAAK,mBAAe,GAAK,kBAAc,CAAC,CAAC;AACvE,UAAME,kBAAmB;AAAA,MACrB,mBAAiB,eAAW,SAAS,GAAG,CAAC,SAAS,CAAC;AAAA,IACvD;AACA,UAAM,WAAa,YAAU,kBAAc,CAAC;AAC5C,UAAM,gBAAkB,sBAAoB,aAAS,GAAG,QAAQ;AAEhE,SAAK,QAAQ,QAAQ,CAACC,YAAW;AAC/B,uBAASA,QAAO,KAAK;AAAA,QACnB,oCAAoC,CAAC,SAAS;AAC5C,cAAI;AACJ,cAAI;AAEJ,cAAID,gBAAe,MAAM,KAAK,IAAI,GAAG;AACnC,uBAAW,UAAU,QAAS,MAAM,SAAS;AAC7C,aAAC,GAAG,IAAI,KAAK,IAAI,WAAW;AAAA,UAC9B,WAAW,cAAc,MAAM,KAAK,IAAI,GAAG;AACzC,uBAAW,SAAS,QAAS;AAC7B,kBAAM,KAAK,IAAI,QAAQ;AAAA,UACzB,OAAO;AACL;AAAA,UACF;AAEA,gBAAM,iBAAiB,KAAK,QAAQ,IAAI,QAAQ;AAChD,cAAI;AAAA,YACA;AAAA,cACA;AAAA,gBACEC,QAAO;AAAA,gBACP,gBAAgB,QAAQ,KAAK,QAAQ;AAAA,cACvC;AAAA,YACF;AAAA,UACF;AAEA,cAAI,CAAC,gBAAgB;AACnB,gBAAI,WAAW,WAAW,kBAAkB;AAAA,UAC9C;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;;;AIrEO,IAAM,gBAAN,cAA4B,OAAO;AAAC;;;ALYpC,IAAM,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,MAAM,CAAC,QAAQ;AAAA,EACf,OAAO;AAAA,EACP,QAAQ,SAAS;AACf,UAAM,UAAU,oBAAI,IAA2B;AAE/C,UAAM,iBAAmB,YAAU,mBAAe,CAAC;AACnD,UAAM,yBAA2B;AAAA,MAC7B;AAAA;AAAA,QAEE;AAAA,UACE;AAAA,YACE,OAAK,uBAAmB,GAAK,4BAAwB,GAAG,IAAI;AAAA,UAChE;AAAA,QACF;AAAA;AAAA,QAEE;AAAA,UACE;AAAA,YACE;AAAA,cACE;AAAA,gBACE,OAAK,mBAAe,GAAK,kBAAc,GAAK,eAAW,CAAC;AAAA,gBACxD,OAAK,uBAAmB,GAAK,4BAAwB,CAAC;AAAA,cAC1D;AAAA;AAAA,cAEE,mBAAe,SAAS,GAAG,GAAK,kBAAc,CAAC;AAAA,YACnD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,UAAM,kBAAoB;AAAA,MACtB;AAAA,QACA;AAAA,QACA;AAAA,QACE;AAAA,UACE;AAAA,YACE,eAAW;AAAA,YACX,wBAAoB;AAAA,YACpB,eAAW;AAAA,YACX;AAAA,cACE;AAAA;AAAA,gBAEE;AAAA,kBACA;AAAA,kBACA,sBAAwB,eAAW,GAAG,GAAG;AAAA,kBACzC;AAAA,gBACF;AAAA;AAAA,gBAEE,mBAAiB,eAAW,GAAG,CAAC,cAAc,CAAC;AAAA,cACnD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,sBAAsB;AAAA,IACzB;AAEA,UAAM,kBAAoB;AAAA,MACtB;AAAA,QACA;AAAA,QACE;AAAA,UACE;AAAA,YACE,eAAW;AAAA,YACX,wBAAoB,QAAW;AAAA,cAC7B,uBAAmB,QAAW,sBAAsB;AAAA,YACxD,CAAC;AAAA;AAAA,YAEC,wBAAoB;AAAA,YACpB,eAAW;AAAA,YACX;AAAA;AAAA,cAEE;AAAA,gBACE;AAAA,kBACA;AAAA,kBACA,sBAAwB,eAAW,GAAG,GAAG;AAAA,kBACzC;AAAA,gBACF;AAAA,cACF;AAAA,cACE;AAAA,gBACE;AAAA,kBACA;AAAA,kBACA;AAAA,oBACI,eAAW;AAAA,oBACX,OAAG,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,kBAC9B;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,YACE,eAAW;AAAA;AAAA,YAEX;AAAA,cACE;AAAA,gBACA;AAAA,gBACA,sBAAwB,eAAW,GAAG,SAAS;AAAA,gBAC7C,eAAW;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,UAAM,cAAgB;AAAA,MACpB;AAAA,QACI;AAAA,UACE,eAAa,OAAG,QAAQ,UAAU,YAAY,CAAC;AAAA,UAC/C,mBAAe;AAAA,QACnB;AAAA,QACE,YAAQ,CAAC,aAAa,SAAS,WAAW,SAAS,CAAC;AAAA,MACxD;AAAA,IACF;AAEA,UAAM,eAAiB;AAAA,MACrB;AAAA,QACI;AAAA,UACA;AAAA,UACA;AAAA,UACE;AAAA,YACA;AAAA,YACE,gBAAY,WAAW;AAAA,YACvB,oBAAgB,CAAC,CAAC;AAAA,UACtB;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,QACI;AAAA,UACE;AAAA,YACE;AAAA,cACE,YAAU,OAAK,mBAAe,GAAK,kBAAc,CAAC,CAAC;AAAA,YACvD;AAAA;AAAA,YACA;AAAA,YACE,UAAM,EAAE,KAAK,EAAE,CAAC;AAAA;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,eAAe,MAAM;AACnB,YACE,CAAC,gBAAgB,MAAM,KAAK,IAAI,KAChC,CAAC,gBAAgB,MAAM,KAAK,IAAI,KAChC,CAAC,aAAa,MAAM,KAAK,IAAI;AAE7B;AACF,aAAK,KAAK;AAEV,cAAM,cAAc,KAAK;AAAA,UACvB,uBAAuB,YAAa,KAAK,GAAG;AAAA,QAC9C;AAEA,cAAM,iBAAiB,YAAY,kBAAkB,IAChD,YAAY,IAAI,UAAU,IAC1B,YAAY,IAAI,YAAY;AAEjC,uBAAe,QAAQ,CAAC,eAAe,UAAU;AAC/C,cAAI,WAAW,MAAM,SAAS;AAC9B,cAAM,qBAAiB,cAAc,IAAI,GAAG;AAC1C,uBAAW,YAAY,cAAc,KAAK,GAAG;AAC7C,4BAAgB,cAAc,IAAI,OAAO;AAAA,UAC3C;AAEA,cACE,cAAc,WAAW,KACzB,cAAc,KAAK,KAAK,SAAS,kBACjC;AACA,6BAAiB,eAAe,CAAC,UAAU,WAAW,SAAS,CAAC;AAChE,kBAAMC,QAAS,SAAO,YAAQ,cAAc,KAAK,KAAK,IAAI,CAAC;AAG3D,kBAAM,WAAWA,MAAK,QAAQ,KAAK,GAAG,EAAE;AACxC,gBACE,UAAU,kBAAkB,WAAW,KACvC,SAAS,iBAAiB,CAAC,EAAE,UAAU,KACvC;AACA,uBAAS,mBAAmB;AAAA,YAC9B;AAEA,kBAAMC,UAAS,IAAI;AAAA,cACjB;AAAA,cACAD;AAAA,cACA,aAAa,eAAe,SAAS,MAAM,SAAS;AAAA,YACtD;AAEA,oBAAQ,IAAI,UAAUC,OAAM;AAAA,UAC9B;AAAA,QACF,CAAC;AAED,YAAI,QAAQ,OAAO,GAAG;AACpB,gBAAM,UAAU,eAAe,SAAS,MAAM,SAAS,KAAK;AAC5D,kBAAS,SAAS,IAAI,cAAc,SAAS,OAAO;AAAA,QACtD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AP9MA,OAAOC,YAAW;AAgBX,SAAS,UACd,KACA,WAA+C,CAAC,GAC5B;AACpB,QAAM,UAA0C,EAAE,QAAQ,OAAU;AACpE,QAAM,UAAU,0BAAS,MAAM;AAAA,IAC7B,cAAc,QAAQ,OAAO;AAAA,IAC7B,iBAAiB,QAAQ,OAAO;AAAA,EAClC,CAAC;AACD,mBAAS,KAAK,SAAS,QAAW,EAAE,SAAS,EAAE,CAAC;AAEhD,MAAI,QAAQ,QAAQ;AAClB,YAAQ,OAAO,cAAc,QAAQ;AACrC,YAAQ,OAAO,gBAAgB;AAC/B,IAAAC,OAAM,iBAAiB,EAAE,WAAW,QAAQ,OAAO,IAAI;AAAA,EACzD;AACA,SAAO,QAAQ;AACjB;;;AaxCO,SAAS,YAAqB;AACnC,SAAO,OAAO,WAAW,eAAe,OAAO,kBAAkB;AACnE;;;AzEiGA,SAAS,aAAa,SAAwD;AAC5E,QAAM,gBAAmC;AAAA,IACvC,KAAK;AAAA,IACL,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,UAAU,OAAO,CAAC;AAAA,IAClB,YAAY,MAAM;AAAA,IAAC;AAAA,IACnB,SAAS,UAAU,IAAI,qBAAqB,IAAI,kBAAkB;AAAA,IAClE,GAAG;AAAA,EACL;AACA,SAAO,OAAO,SAAS,aAAa;AACtC;AAEA,eAAsB,SACpB,MACA,UAAmB,CAAC,GACK;AACzB,eAAa,OAAO;AACpB,UAAQ,WAAW,CAAC;AAEpB,MAAI,UAAU,GAAG;AACf,IAAAC,OAAM,OAAO,YAAY;AAAA,EAC3B;AAEA,QAAM,gBAAgB,gBAAgB,KAAK,IAAI;AAC/C,MAAI,eAAe;AACjB,WAAO,KACJ,QAAQ,gBAAgB,EAAE,EAC1B,MAAM,kBAAkB,EACxB,IAAI,kBAAkB,EACtB,KAAK,GAAG;AAAA,EACb;AAEA,MAAI,MAA2B;AAC/B,MAAI,aAAa;AACjB,MAAI;AAEJ,QAAM,SAAS;AAAA,IACb,MAAM;AACJ,YAAMC,OAAM,MAAM;AAAA,QAChB,YAAY;AAAA,QACZ,4BAA4B;AAAA,QAC5B,eAAe;AAAA,QACf,SAAS,CAAC,KAAK;AAAA,MACjB,CAAC;AACD,UAAI,IAAI,OAAO,QAAQ;AACrB,QAAAD,OAAM,gBAAgB,EAAE,UAAU,IAAI,MAAM;AAAA,MAC9C;AAAA,IACF;AAAA,IACA,MAAM;AACJ;AAAA,QACE;AAAA,QACA,CAAC,0BAAiB,kBAAU,qCAA2B,qBAAY;AAAA,QACnE,EAAE,MAAM,UAAU;AAAA,MACpB;AAAA,IACF;AAAA,IACA,QAAQ,gBACL,MAAM,oBAAoB,KAAK,qBAAa,QAAQ,OAAO;AAAA,IAC9D,QAAQ,aACL,MAAM;AACL,sBAAgB,KAAK,CAAC,mBAAW,gBAAQ,CAAC;AAAA,IAC5C;AAAA,IACF,QAAQ,WACL,MACC;AAAA,MACE;AAAA,MACA;AAAA,MACA,OAAO,QAAQ,WAAW,YAAY,MAAM,OAAO,QAAQ;AAAA,IAC7D;AAAA;AAAA,KAEH,QAAQ,eAAe,QAAQ,SAC7B,MAAM;AACL;AAAA,QACE;AAAA,QACA;AAAA;AAAA,UAEE,QAAQ,cAAc,CAAC,wBAAe,wBAAe,IAAI,CAAC;AAAA,UAC1D,QAAQ,MAAM,CAAC,aAAK,eAAM,IAAI,CAAC;AAAA,QACjC,EAAE,KAAK;AAAA,MACT;AAAA,IACF;AAAA,IACF,QAAQ,gBACL,MAAM,gBAAgB,KAAK,CAAC,kCAAwB,wBAAe,CAAC;AAAA,IACvE,MAAO,aAAa,SAAS,GAAG;AAAA;AAAA;AAAA,IAGhC,QAAQ,WAAW,MAAO,SAAS,UAAU,KAAK,QAAQ,SAASE,GAAC,CAAC;AAAA,EACvE,EAAE,OAAO,OAAO;AAEhB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,OAAO,CAAC,EAAE;AAChB,YAAQ,WAAY,MAAM,OAAO,UAAW,IAAI,EAAE;AAAA,EACpD;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,KAAK,MAAM;AACf,YAAM,EAAE,OAAO,UAAU,IAAI,MAAM,OAAO,kBAAkB;AAC5D,aAAOC,WAAU,IAAI;AACrB,YAAM,MAAM,MAAM,EAAE,WAAW,KAAK,CAAC;AACrC,YAAM,UAAUC,MAAK,MAAM,iBAAiB,GAAG,YAAY,MAAM;AACjE,YAAM,QAAQ,KAAK,IAAI;AAAA,IACzB;AAAA,EACF;AACF;", "names": ["parse", "m", "debug", "join", "normalize", "lib_star", "module", "t", "m", "t", "matcher", "path", "getNearestAssignment", "matcher", "binding", "ref", "t", "m", "t", "debug", "m", "matcher", "m", "callExpression", "callExpression", "matcher", "t", "m", "statement", "t", "m", "matcher", "t", "m", "m", "matcher", "t", "m", "m", "arrayExpression", "variableDeclaration", "matcher", "debug", "debug", "logger", "debug", "m", "ifStatement", "matcher", "ifStatement", "t", "m", "matcher", "t", "m", "m", "matcher", "t", "m", "matcher", "t", "m", "program", "matcher", "jsxValue", "t", "m", "convertType", "convertAttributes", "convertChil<PERSON>n", "matcher", "expression", "prop", "m", "expression", "t", "m", "t", "m", "t", "m", "t", "m", "t", "m", "t", "m", "transforms_exports", "t", "t", "m", "t", "t", "m", "t", "m", "logicalExpression", "binaryExpression", "m", "matcher", "t", "expression", "m", "matcher", "t", "m", "matcher", "t", "m", "expression", "t", "m", "matcher", "t", "m", "matcher", "t", "statement", "m", "t", "m", "matcher", "t", "m", "matcher", "t", "m", "trueM<PERSON><PERSON>", "false<PERSON><PERSON><PERSON>", "t", "m", "matcher", "t", "m", "matcher", "transforms_exports", "t", "m", "relativePath", "dirname", "join", "module", "matcher", "file", "module", "t", "m", "t", "m", "statement", "t", "m", "statement", "module", "<PERSON><PERSON><PERSON><PERSON>", "returnedValue", "expression", "m", "expression", "module", "statement", "m", "statement", "module", "program", "matcher", "<PERSON><PERSON><PERSON><PERSON>", "module", "file", "module", "debug", "debug", "debug", "parse", "m", "normalize", "join"]}