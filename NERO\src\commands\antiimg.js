/**
 * NERO Bot - Comando Anti-Imagem
 * Proteção contra envio de imagens
 */

const protectionSystem = require('../utils/protectionSystem');
const { validatePermissions, PERMISSION_LEVELS } = require('../utils/permissionSystem');
const logger = require('../utils/logger');

module.exports = {
    name: 'antiimg',
    aliases: ['anti-img', 'noimg', 'antiimage'],
    description: 'Ativa/desativa proteção contra imagens',
    usage: 'antiimg [on/off] [ação]',
    permissions: 'Admin',
    cooldown: 2,

    async execute({ sock, msg, args, reply }) {
        const groupId = msg.key.remoteJid;

        // Verificar se é um grupo
        if (!groupId.endsWith('@g.us')) {
            return await reply('❌ Este comando só pode ser usado em grupos!');
        }

        // Verificar permissões
        const hasPermission = await validatePermissions(sock, msg, PERMISSION_LEVELS.ADMIN);
        if (!hasPermission) {
            return await reply('❌ Você precisa ser admin para usar este comando!');
        }

        const action = args[0]?.toLowerCase();
        const actionType = args[1]?.toLowerCase();

        switch (action) {
            case 'on':
            case 'ativar':
            case 'enable':
                return await this.enableAntiImg(reply, groupId, actionType);

            case 'off':
            case 'desativar':
            case 'disable':
                return await this.disableAntiImg(reply, groupId);

            case 'config':
            case 'configurar':
                return await this.showConfig(reply, groupId);

            default:
                return await this.showStatus(reply, groupId);
        }
    },

    async enableAntiImg(reply, groupId, actionType = 'delete') {
        try {
            const validActions = ['delete', 'warn', 'kick'];
            if (actionType && !validActions.includes(actionType)) {
                return await reply(`❌ Ação inválida! Use: ${validActions.join(', ')}`);
            }

            const config = {
                action: actionType || 'delete'
            };

            const result = protectionSystem.toggleProtection(groupId, 'antiimg', true, config);

            if (result.success) {
                let message = '🟢 **ANTI-IMAGEM ATIVADO**\n\n';
                message += `🖼️ Imagens serão bloqueadas automaticamente\n`;
                message += `⚡ **Ação:** ${result.config.action}\n`;
                message += `🔢 **Máx. Violações:** ${result.config.maxViolations}\n\n`;
                
                message += '💡 **Comandos úteis:**\n';
                message += '• `!antiimg off` - Desativar proteção\n';
                message += '• `!antiimg config` - Ver configuração\n';
                message += '• `!protections status` - Ver todas as proteções';

                return await reply(message);
            } else {
                return await reply('❌ Erro ao ativar anti-imagem. Tente novamente.');
            }
        } catch (error) {
            logger.error('Erro ao ativar anti-imagem:', error);
            return await reply('❌ Erro interno. Tente novamente mais tarde.');
        }
    },

    async disableAntiImg(reply, groupId) {
        try {
            const result = protectionSystem.toggleProtection(groupId, 'antiimg', false);

            if (result.success) {
                return await reply('🔴 **ANTI-IMAGEM DESATIVADO**\n\n✅ Imagens agora são permitidas no grupo.');
            } else {
                return await reply('❌ Erro ao desativar anti-imagem. Tente novamente.');
            }
        } catch (error) {
            logger.error('Erro ao desativar anti-imagem:', error);
            return await reply('❌ Erro interno. Tente novamente mais tarde.');
        }
    },

    async showStatus(reply, groupId) {
        const config = protectionSystem.getGroupProtection(groupId, 'antiimg');
        
        let message = '🖼️ **STATUS DO ANTI-IMAGEM**\n\n';
        message += `🔘 **Status:** ${config.enabled ? '🟢 Ativo' : '🔴 Inativo'}\n`;
        
        if (config.enabled) {
            message += `⚡ **Ação:** ${config.action}\n`;
            message += `🔢 **Máx. Violações:** ${config.maxViolations}\n\n`;
            
            message += '⚙️ **Comandos:**\n';
            message += '• `!antiimg off` - Desativar\n';
            message += '• `!antiimg config` - Ver configuração\n';
            message += '• `!protections status` - Ver todas as proteções';
        } else {
            message += '\n💡 Use `!antiimg on` para ativar a proteção.';
        }

        return await reply(message);
    },

    async showConfig(reply, groupId) {
        const config = protectionSystem.getGroupProtection(groupId, 'antiimg');
        
        let message = '⚙️ **CONFIGURAÇÃO DO ANTI-IMAGEM**\n\n';
        message += `🔘 **Status:** ${config.enabled ? '🟢 Ativo' : '🔴 Inativo'}\n`;
        message += `⚡ **Ação:** ${config.action}\n`;
        message += `🔢 **Máx. Violações:** ${config.maxViolations}\n\n`;
        
        message += '📋 **Descrição das Ações:**\n';
        message += '• `delete` - Apaga a imagem automaticamente\n';
        message += '• `warn` - Avisa o usuário sobre a violação\n';
        message += '• `kick` - Remove o usuário do grupo\n\n';
        
        message += '💡 **Alterar ação:** `!antiimg on [ação]`\n';
        message += 'Exemplo: `!antiimg on warn`';

        return await reply(message);
    }
};
