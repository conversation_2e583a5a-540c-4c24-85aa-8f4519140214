{"version": 3, "sources": ["../src/cli.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport { program } from 'commander';\nimport debug from 'debug';\nimport { existsSync, readFileSync } from 'node:fs';\nimport { readFile, rm } from 'node:fs/promises';\nimport { join } from 'node:path';\nimport * as url from 'node:url';\nimport { webcrack } from './index.js';\n\nconst __dirname = url.fileURLToPath(new URL('.', import.meta.url));\nconst { version, description } = JSON.parse(\n  readFileSync(join(__dirname, '..', 'package.json'), 'utf8'),\n) as { version: string; description: string };\n\ndebug.enable('webcrack:*');\n\ninterface Options {\n  force: boolean;\n  output?: string;\n  mangle: boolean;\n  jsx: boolean;\n  unpack: boolean;\n  deobfuscate: boolean;\n  unminify: boolean;\n}\n\nasync function readStdin() {\n  let data = '';\n  process.stdin.setEncoding('utf8');\n  for await (const chunk of process.stdin) data += chunk;\n  return data;\n}\n\nprogram\n  .version(version)\n  .description(description)\n  .option('-o, --output <path>', 'output directory for bundled files')\n  .option('-f, --force', 'overwrite output directory')\n  .option('-m, --mangle', 'mangle variable names')\n  .option('--no-jsx', 'do not decompile JSX')\n  .option('--no-unpack', 'do not extract modules from the bundle')\n  .option('--no-deobfuscate', 'do not deobfuscate the code')\n  .option('--no-unminify', 'do not unminify the code')\n  .argument('[file]', 'input file, defaults to stdin')\n  .action(async (input: string | undefined) => {\n    const { output, force, ...options } = program.opts<Options>();\n    const code = await (input ? readFile(input, 'utf8') : readStdin());\n\n    if (output) {\n      if (force || !existsSync(output)) {\n        await rm(output, { recursive: true, force: true });\n      } else {\n        program.error('output directory already exists');\n      }\n    }\n\n    const result = await webcrack(code, options);\n\n    if (output) {\n      await result.save(output);\n    } else {\n      console.log(result.code);\n      if (result.bundle) {\n        debug('webcrack:unpack')(\n          `${result.bundle.modules.size} modules are not displayed in the terminal. Use the --output option to save them`,\n        );\n      }\n    }\n  })\n  .parse();\n"], "mappings": ";AAEA,SAAS,eAAe;AACxB,OAAO,WAAW;AAClB,SAAS,YAAY,oBAAoB;AACzC,SAAS,UAAU,UAAU;AAC7B,SAAS,YAAY;AACrB,YAAY,SAAS;AACrB,SAAS,gBAAgB;AAEzB,MAAM,YAAY,IAAI,cAAc,IAAI,IAAI,KAAK,YAAY,GAAG,CAAC;AACjE,MAAM,EAAE,SAAS,YAAY,IAAI,KAAK;AAAA,EACpC,aAAa,KAAK,WAAW,MAAM,cAAc,GAAG,MAAM;AAC5D;AAEA,MAAM,OAAO,YAAY;AAYzB,eAAe,YAAY;AACzB,MAAI,OAAO;AACX,UAAQ,MAAM,YAAY,MAAM;AAChC,mBAAiB,SAAS,QAAQ,MAAO,SAAQ;AACjD,SAAO;AACT;AAEA,QACG,QAAQ,OAAO,EACf,YAAY,WAAW,EACvB,OAAO,uBAAuB,oCAAoC,EAClE,OAAO,eAAe,4BAA4B,EAClD,OAAO,gBAAgB,uBAAuB,EAC9C,OAAO,YAAY,sBAAsB,EACzC,OAAO,eAAe,wCAAwC,EAC9D,OAAO,oBAAoB,6BAA6B,EACxD,OAAO,iBAAiB,0BAA0B,EAClD,SAAS,UAAU,+BAA+B,EAClD,OAAO,OAAO,UAA8B;AAC3C,QAAM,EAAE,QAAQ,OAAO,GAAG,QAAQ,IAAI,QAAQ,KAAc;AAC5D,QAAM,OAAO,OAAO,QAAQ,SAAS,OAAO,MAAM,IAAI,UAAU;AAEhE,MAAI,QAAQ;AACV,QAAI,SAAS,CAAC,WAAW,MAAM,GAAG;AAChC,YAAM,GAAG,QAAQ,EAAE,WAAW,MAAM,OAAO,KAAK,CAAC;AAAA,IACnD,OAAO;AACL,cAAQ,MAAM,iCAAiC;AAAA,IACjD;AAAA,EACF;AAEA,QAAM,SAAS,MAAM,SAAS,MAAM,OAAO;AAE3C,MAAI,QAAQ;AACV,UAAM,OAAO,KAAK,MAAM;AAAA,EAC1B,OAAO;AACL,YAAQ,IAAI,OAAO,IAAI;AACvB,QAAI,OAAO,QAAQ;AACjB,YAAM,iBAAiB;AAAA,QACrB,GAAG,OAAO,OAAO,QAAQ,IAAI;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AACF,CAAC,EACA,MAAM;", "names": []}