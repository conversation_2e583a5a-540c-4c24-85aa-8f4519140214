{"_sessions": {"BSElvFb0hHvoDsT2AJ9CN1jOA0SjmR48TczPrN8hckkw": {"registrationId": 249, "currentRatchet": {"ephemeralKeyPair": {"pubKey": "BR7UM1kjAjwHzjbzvoxqOifhXwkxMULfEfVVIft8AnRb", "privKey": "aDhtPfawrhwfLGnyJn5k6F2mGcJdd4GsdZ90nZcUIk8="}, "lastRemoteEphemeralKey": "Be5orSkSq666ggXu+qchYVFkHKnuQkFwnxqjpN2ARIxD", "previousCounter": 0, "rootKey": "O64cu+E5IiGafyggeXvEEoTMZrK8b828ucNMq9/7gbQ="}, "indexInfo": {"baseKey": "BSElvFb0hHvoDsT2AJ9CN1jOA0SjmR48TczPrN8hckkw", "baseKeyType": 1, "closed": -1, "used": 1755629848897, "created": 1755629848897, "remoteIdentityKey": "BYwiI032eedZvUGs2H+IK2RoJZxu5X1utMpn1DffAt9X"}, "_chains": {"BR7UM1kjAjwHzjbzvoxqOifhXwkxMULfEfVVIft8AnRb": {"chainKey": {"counter": 1, "key": "1KGPoaTGbHS6QpPvE1iaHnKWaqYXyjF7s42oGP+iWNE="}, "chainType": 1, "messageKeys": {}}}, "pendingPreKey": {"signedKeyId": 1, "baseKey": "BSElvFb0hHvoDsT2AJ9CN1jOA0SjmR48TczPrN8hckkw"}}}, "version": "v1"}