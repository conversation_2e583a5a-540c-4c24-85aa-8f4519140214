const axios = require('axios');
const { validateURL, validateCommandArgs } = require('../utils/validators');
const logger = require('../utils/logger');

module.exports = {
    name: 'facebook',
    aliases: ['fb', 'face'],
    description: 'Download de vídeos do Facebook',
    category: 'downloads',
    cooldown: 15, // 15 segundos de cooldown
    
    async execute({ sock, msg, args, reply, selo }) {
        try {
            // Validar argumentos
            const validation = validateCommandArgs(args, { minArgs: 1 });
            if (!validation.valid) {
                return reply(`❌ **Uso correto:** \`!facebook [URL do Facebook]\`\n\n**Exemplo:** \`!facebook https://www.facebook.com/watch/?v=xxx\`\n\n💡 **Suporta:** Vídeos públicos do Facebook`);
            }

            const url = args.join(' ').trim();

            // Validar URL
            const urlValidation = validateFacebookURL(url);
            if (!urlValidation.valid) {
                return reply(`❌ **URL inválida!**\n\n${urlValidation.reason}\n\n**Formatos aceitos:**\n• https://www.facebook.com/watch/?v=xxx\n• https://www.facebook.com/user/videos/xxx\n• https://fb.watch/xxx\n• https://m.facebook.com/watch/?v=xxx`);
            }

            logger.info(`[FACEBOOK] Download iniciado: ${url}`);

            // Reação de processamento
            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "🔍", key: msg.key }
            });

            await reply(`📘 **Baixando do Facebook...**\n\n🔗 **URL:** ${url}\n⏳ Aguarde, estou processando...`);

            // Tentar download
            const result = await downloadFacebookVideo(url);

            if (!result.success) {
                await sock.sendMessage(msg.key.remoteJid, {
                    react: { text: "❌", key: msg.key }
                });

                return reply(`❌ **Erro no download**\n\n${result.error}\n\n💡 **Dicas:**\n• Verifique se o vídeo é público\n• Alguns vídeos podem estar protegidos\n• Tente novamente em alguns minutos\n• Vídeos privados não podem ser baixados`);
            }

            // Reação de sucesso
            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "📤", key: msg.key }
            });

            // Enviar vídeo
            await sock.sendMessage(msg.key.remoteJid, {
                video: { url: result.downloadUrl },
                mimetype: 'video/mp4',
                caption: `📘 **Facebook Download Concluído**\n\n👤 **Página:** ${result.author || 'N/A'}\n📝 **Título:** ${result.title ? result.title.substring(0, 100) + '...' : 'N/A'}\n📊 **Qualidade:** ${result.quality || 'HD'}\n⏱️ **Duração:** ${result.duration || 'N/A'}\n📅 **Data:** ${result.date || 'N/A'}\n\n🤖 **NERO Facebook Downloader**`
            }, { quoted: selo });

            logger.info(`[FACEBOOK] Download concluído: ${result.author || 'unknown'}`);

        } catch (error) {
            logger.error('Erro no comando facebook:', error);
            
            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "❌", key: msg.key }
            });

            reply('❌ **Erro interno**\n\nOcorreu um erro inesperado. Tente novamente em alguns minutos.');
        }
    }
};

/**
 * Valida URL do Facebook
 * @param {string} url - URL para validar
 * @returns {Object}
 */
function validateFacebookURL(url) {
    if (!url || typeof url !== 'string') {
        return { valid: false, reason: 'URL não fornecida' };
    }

    // Padrões de URL do Facebook
    const facebookPatterns = [
        /^https?:\/\/(www\.|m\.)?facebook\.com\/watch\/\?v=\d+/,
        /^https?:\/\/(www\.|m\.)?facebook\.com\/[\w.-]+\/videos\/\d+/,
        /^https?:\/\/fb\.watch\/[\w-]+/,
        /^https?:\/\/(www\.|m\.)?facebook\.com\/video\.php\?v=\d+/,
        /^https?:\/\/(www\.|m\.)?facebook\.com\/[\w.-]+\/posts\/\d+/
    ];

    const isValid = facebookPatterns.some(pattern => pattern.test(url));
    
    if (!isValid) {
        return { valid: false, reason: 'URL do Facebook inválida' };
    }

    return { valid: true };
}

/**
 * Baixa vídeo do Facebook usando múltiplas APIs
 * @param {string} url - URL do Facebook
 * @returns {Promise<Object>}
 */
async function downloadFacebookVideo(url) {
    const apis = [
        {
            name: 'API 1',
            endpoint: 'https://api.facebookvideodownloader.com/download',
            method: 'POST'
        },
        {
            name: 'API 2',
            endpoint: 'https://api.savefrom.net/ajax.php',
            method: 'POST'
        }
    ];

    for (const api of apis) {
        try {
            logger.info(`[FACEBOOK] Tentando ${api.name}...`);
            
            let response;
            if (api.name === 'API 1') {
                response = await axios.post(api.endpoint, {
                    url: url
                }, {
                    timeout: 30000,
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });
            } else {
                response = await axios.post(api.endpoint, {
                    url: url,
                    format: 'json'
                }, {
                    timeout: 30000,
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });
            }

            const data = response.data;
            
            // Processar resposta baseada na API
            if (data && (data.data || data.result || data.url)) {
                const videoData = data.data || data.result || data;
                
                // Extrair URL de download
                let downloadUrl = null;
                if (videoData.video_url || videoData.url) {
                    downloadUrl = videoData.video_url || videoData.url;
                } else if (videoData.links && Array.isArray(videoData.links)) {
                    // Pegar a melhor qualidade disponível
                    const hdLink = videoData.links.find(link => link.quality === 'hd' || link.quality === '720p');
                    const sdLink = videoData.links.find(link => link.quality === 'sd' || link.quality === '480p');
                    downloadUrl = (hdLink || sdLink || videoData.links[0])?.url;
                }

                if (downloadUrl) {
                    return {
                        success: true,
                        downloadUrl: downloadUrl,
                        author: videoData.author || videoData.page_name || videoData.uploader,
                        title: videoData.title || videoData.description,
                        quality: videoData.quality || 'HD',
                        duration: videoData.duration || videoData.length,
                        date: videoData.upload_date || videoData.created_time
                    };
                }
            }

        } catch (error) {
            logger.warn(`[FACEBOOK] ${api.name} falhou:`, error.message);
            continue;
        }
    }

    // Fallback: tentar com API genérica
    try {
        logger.info('[FACEBOOK] Tentando API genérica...');
        
        const response = await axios.get(`https://api.cobalt.tools/api/json`, {
            params: {
                url: url,
                format: 'mp4',
                quality: 'max'
            },
            timeout: 30000,
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        const data = response.data;
        if (data && data.url) {
            return {
                success: true,
                downloadUrl: data.url,
                author: 'Facebook',
                title: 'Vídeo do Facebook',
                quality: 'HD'
            };
        }

    } catch (error) {
        logger.warn('[FACEBOOK] API genérica falhou:', error.message);
    }

    return {
        success: false,
        error: 'Não foi possível baixar o vídeo. Verifique se o conteúdo é público.'
    };
}
