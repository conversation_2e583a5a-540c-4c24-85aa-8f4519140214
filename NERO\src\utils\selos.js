/**
 * NERO Bot - Sistema de Selos
 * Centraliza todos os selos e assinaturas do bot
 */

const { BOT_CONSTANTS } = require('../constants');

/**
 * Criar selo personalizado do usuário
 * @param {string} pushname - Nome do usuário
 * @param {string} user - JID do usuário
 * @param {string} remoteJid - JID do chat
 * @returns {Object}
 */
const createUserSeal = (pushname, user, remoteJid) => {
  return {
    key: {
      fromMe: false,
      participant: `<EMAIL>`,
      ...(remoteJid ? { remoteJid } : {})
    },
    message: {
      contactMessage: {
        displayName: pushname,
        vcard: `BEGIN:VCARD\nVERSION:3.0\nN:XL;${pushname},;;;\nFN:${pushname},\nitem1.TEL;waid=${user.split('@')[0]}:${user.split('@')[0]}\nitem1.X-ABLabel:Ponsel\nEND:VCARD`
      }
    }
  };
};

/**
 * Selo do dono/desenvolvedor - Versão Premium
 */
const ownerSeal = {
  key: {
    participant: '<EMAIL>'
  },
  message: {
    liveLocationMessage: {
      caption: `👑 𝗗𝗘𝗦𝗘𝗡𝗩𝗢𝗟𝗩𝗘𝗗𝗢𝗥 𝗢𝗙𝗜𝗖𝗜𝗔𝗟 👑\n🔥 ${BOT_CONSTANTS.OWNER_NAME} • ${BOT_CONSTANTS.NAME} v2.0\n⚡ Sistema IA Avançado • Status: 🟢 Online\n🚀 Powered by Gemini AI & SerpApi`
    }
  }
};

/**
 * Selo do dono - Versão Simples e Elegante
 */
const ownerSealSimple = {
  key: {
    participant: '<EMAIL>'
  },
  message: {
    liveLocationMessage: {
      caption: `👑 ${BOT_CONSTANTS.OWNER_NAME} • Desenvolvedor Oficial\n🤖 ${BOT_CONSTANTS.NAME} Bot • Versão 2.0`
    }
  }
};

/**
 * Selo do dono - Versão Minimalista
 */
const ownerSealMinimal = {
  key: {
    participant: '<EMAIL>'
  },
  message: {
    liveLocationMessage: {
      caption: `👨‍💻 ${BOT_CONSTANTS.OWNER_NAME} | 🤖 ${BOT_CONSTANTS.NAME} Developer`
    }
  }
};

/**
 * Selo do dono - Versão Corporativa
 */
const ownerSealCorporate = {
  key: {
    participant: '<EMAIL>'
  },
  message: {
    liveLocationMessage: {
      caption: `🏢 𝗡𝗘𝗥𝗢 𝗗𝗘𝗩𝗘𝗟𝗢𝗣𝗠𝗘𝗡𝗧 𝗧𝗘𝗔𝗠\n👨‍💻 Lead Developer: ${BOT_CONSTANTS.OWNER_NAME}\n🔧 WhatsApp AI Bot Framework\n📊 Enterprise Grade Solution`
    }
  }
};

/**
 * Selo do Meta AI
 */
const metaAISeal = { 
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast", 
    fromMe: false, 
  },
  message: {
    contactMessage: {
      displayName: `${BOT_CONSTANTS.NAME} AI`, 
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Meta AI;;;\nFN:Meta AI\nitem1.TEL;waid=13135550002:13135550002\nitem1.X-ABLabel:Celular\nEND:VCARD`, 
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do WhatsApp oficial
 */
const whatsappSeal = {
  key: {
    fromMe: false,
    participant: "<EMAIL>",
    remoteJid: "<EMAIL>",
  },
  message: {
    contactMessage: {
      displayName: "WhatsApp",
      vcard: "BEGIN:VCARD\nVERSION:3.0\nFN:WhatsApp\nORG:WhatsApp\nTEL;type=CELL;waid=0000000000:+0000000000\nEND:VCARD",
    },
  },
};

/**
 * Selo do ChatGPT
 */
const chatGPTSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast", 
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: `${BOT_CONSTANTS.NAME} GPT`, 
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;OpenAI GPT;;;\nFN:${BOT_CONSTANTS.NAME} GPT\nitem1.TEL;waid=18002428478:18002428478\nitem1.X-ABLabel:Celular\nEND:VCARD`, 
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do Microsoft Copilot
 */
const copilotSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast", 
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "Microsoft Copilot", 
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Microsoft Copilot;;;\nFN:Microsoft Copilot\nitem1.TEL;waid=***********:***********\nitem1.X-ABLabel:Celular\nEND:VCARD`, 
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do Nubank
 */
const nubankSeal = {
  key: { 
    participant: '<EMAIL>', 
    fromMe: false 
  },
  message: { 
    contactMessage: { 
      displayName: 'Nubank',
      vcard: 'BEGIN:VCARD\nVERSION:3.0\nFN:Banco\nORG:Banco;\nTEL;type=MSG;type=CELL;type=VOICE;waid=************:+************\nEND:VCARD'
    }
  }
};

/**
 * Selo do Banco do Brasil
 */
const bancoBrasilSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast",
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "Banco do Brasil",
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Banco Do Brasil;;;\nFN:Banco do Brasil\nitem1.TEL;waid=************:************\nitem1.X-ABLabel:Celular\nEND:VCARD`,
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo da CAIXA - Estrutura igual Meta AI
 */
const caixaSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast",
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "CAIXA",
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;CAIXA;;;\nFN:CAIXA\nitem1.TEL;waid=***********:***********\nitem1.X-ABLabel:Celular\nEND:VCARD`,
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do Itaú - Estrutura igual Meta AI
 */
const itauSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast",
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "Itaú",
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Itaú;;;\nFN:Itaú\nitem1.TEL;waid=13135550003:13135550003\nitem1.X-ABLabel:Celular\nEND:VCARD`,
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do Bradesco - Estrutura igual Meta AI
 */
const bradescoSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast",
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "Bradesco",
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Bradesco;;;\nFN:Bradesco\nitem1.TEL;waid=13135550004:13135550004\nitem1.X-ABLabel:Celular\nEND:VCARD`,
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do Santander - Estrutura igual Meta AI
 */
const santanderSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast",
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "Santander",
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Santander;;;\nFN:Santander\nitem1.TEL;waid=13135550008:13135550008\nitem1.X-ABLabel:Celular\nEND:VCARD`,
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do Inter - Estrutura igual Meta AI
 */
const interSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast",
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "Banco Inter",
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Inter;;;\nFN:Banco Inter\nitem1.TEL;waid=13135550009:13135550009\nitem1.X-ABLabel:Celular\nEND:VCARD`,
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do BTG Pactual
 */
const btgSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast",
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "BTG Pactual",
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;BTG Pactual;;;\nFN:BTG Pactual\nitem1.TEL;waid=************:************\nitem1.X-ABLabel:Celular\nEND:VCARD`,
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do C6 Bank
 */
const c6BankSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast",
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "C6 Bank",
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;C6 Bank;;;\nFN:C6 Bank\nitem1.TEL;waid=************:************\nitem1.X-ABLabel:Celular\nEND:VCARD`,
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do Next (Bradesco)
 */
const nextSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast",
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "Next",
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Next Bradesco;;;\nFN:Next\nitem1.TEL;waid=************:************\nitem1.X-ABLabel:Celular\nEND:VCARD`,
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do PicPay
 */
const picpaySeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast",
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "PicPay",
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;PicPay;;;\nFN:PicPay\nitem1.TEL;waid=************:************\nitem1.X-ABLabel:Celular\nEND:VCARD`,
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do Mercado Pago
 */
const mercadoPagoSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast",
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "Mercado Pago",
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Mercado Pago;;;\nFN:Mercado Pago\nitem1.TEL;waid=551140003030:551140003030\nitem1.X-ABLabel:Celular\nEND:VCARD`,
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do Banco Safra
 */
const safraSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast",
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "Banco Safra",
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Banco Safra;;;\nFN:Banco Safra\nitem1.TEL;waid=551130302000:551130302000\nitem1.X-ABLabel:Celular\nEND:VCARD`,
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do Banco Original
 */
const originalSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast",
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "Banco Original",
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Banco Original;;;\nFN:Banco Original\nitem1.TEL;waid=551140000238:551140000238\nitem1.X-ABLabel:Celular\nEND:VCARD`,
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do Banco Neon
 */
const neonSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast",
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "Neon",
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Banco Neon;;;\nFN:Neon\nitem1.TEL;waid=551140420001:551140420001\nitem1.X-ABLabel:Celular\nEND:VCARD`,
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

// === APENAS SELOS DE BANCOS ===

/**
 * Coleção de selos - APENAS BANCOS E ESSENCIAIS
 */
const seals = {
  // Selos essenciais do sistema
  user: createUserSeal,
  owner: ownerSeal,
  ownerSimple: ownerSealSimple,
  ownerMinimal: ownerSealMinimal,
  ownerCorporate: ownerSealCorporate,
  metaAI: metaAISeal,
  whatsapp: whatsappSeal,
  chatGPT: chatGPTSeal,
  copilot: copilotSeal,

  // APENAS SELOS DE BANCOS
  nubank: nubankSeal,
  bancoBrasil: bancoBrasilSeal,
  caixa: caixaSeal,
  itau: itauSeal,
  bradesco: bradescoSeal,
  santander: santanderSeal,
  inter: interSeal,
  btg: btgSeal,
  c6bank: c6BankSeal,
  next: nextSeal,
  picpay: picpaySeal,
  mercadopago: mercadoPagoSeal,
  safra: safraSeal,
  original: originalSeal,
  neon: neonSeal
};

/**
 * Obter selo por tipo
 * @param {string} type - Tipo do selo
 * @param {Object} params - Parâmetros para selos dinâmicos
 * @returns {Object}
 */
const getSeal = (type, params = {}) => {
  if (type === 'user') {
    return seals.user(params.pushname, params.user, params.remoteJid);
  }
  return seals[type] || seals.user('Usuário', '<EMAIL>', '');
};

module.exports = {
  seals,
  getSeal,
  createUserSeal
};
