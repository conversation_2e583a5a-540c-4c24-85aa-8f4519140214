//copiar códigos? modificar? deixa os créditos no yt
//base do gutinho (my channel↓
//YouTube:https: @gutoxtazv9
//WhatsApp: +55 51 8992-6591
//telegram: t.me/gutox9
//local: Brasil, Bahia, Candelária,

global.owner = ["553184377531"]
global.ownername = "么🇵🇹ℙ𝕆ℝ𝕋𝕌𝔾𝕌𝕀ℕℍ𝔸🇵🇹么ツ"
global.location = "Brasil, Bahia, Candelária"
global.botname = '么🇵🇹ℙ𝕆ℝ𝕋𝕌𝔾𝕌𝕀ℕℍ𝔸🇵🇹么ツ' 
global.link ='https://whatsapp.com/channel/0029Vas26LsIt5rw9Z5jdk3s'
global.prefa = ['','!','.',','] 
global.limitawal = {
    premium: "Infinity",
    free: 20
}

let fs = require('fs')
let file = require.resolve(__filename)
fs.watchFile(file, () => {
fs.unwatchFile(file)
console.log(`Update ${__filename}`)
delete require.cache[file]
require(file)
})