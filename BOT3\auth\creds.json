{"noiseKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "GC1vGYzLnRr+x2dVYB7LoA0wmAZQImL16gb2FSqb22g="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "msYAnxrP8TAfzRbNdX4zFmH+57PjqfcEwnGA8F4MlhI="}}, "pairingEphemeralKeyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "mApthRKxHLUDMpMZQYlI4KS091otaE+FMGMesZ4XDGM="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "vXFcZVn2eNKqHObeS0Bz1H4CiHOK1VZGkzEG3ZK03T0="}}, "signedIdentityKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "yES4PNeVGCXsuAbt3Pj8tJ/F7/jacqIk1zv5WA71LWo="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "+3fX178JhMhE8KtgEE888lZRshxWBk3yx59xhmH9pjQ="}}, "signedPreKey": {"keyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "WNxfF5XRezTILUE9q0zpRxf79ESrmZ4XcAaL8NKZ9k0="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "L2IiDgVi4VEjgRN1LhmVkqYFok2t9Akec46Ku1lPnzE="}}, "signature": {"type": "<PERSON><PERSON><PERSON>", "data": "Tqr5xVtz6/F1qb7yNIlF5BPsn2tv+aW4cFxl+B8Ux4BYjXOjxB0FgZV7X+QNZ1B3/qfARuaHjZt7Zj9FMFtLgA=="}, "keyId": 1}, "registrationId": 204, "advSecretKey": "TDNgNqIQqhB88jRs20mbesOVecRk5NZ2FNIpjwyt9g0=", "processedHistoryMessages": [{"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "25BB42F99DD9D694654874537CE3F60F"}, "messageTimestamp": **********}, {"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "4B803C3761CB027E15E7AE64E6540A6F"}, "messageTimestamp": **********}, {"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "C6AE3D483CE23AF32BAE305370147BB3"}, "messageTimestamp": **********}], "nextPreKeyId": 31, "firstUnuploadedPreKeyId": 31, "accountSyncCounter": 1, "accountSettings": {"unarchiveChats": false}, "registered": false, "account": {"details": "CM7Uv1MQqYCUxQYYAyAAKAA=", "accountSignatureKey": "YyxPEbnkC71HWCaIjJQOEH1BL4qVpNBSexoyLgcel1s=", "accountSignature": "KU8fMp3j/SR37jDw60oz9IoZpX/EWywZafJaFebV8MqML2IoSRyR1YDUkcA5mhBL0s7os49Kbk+DTKtK1hRfCg==", "deviceSignature": "K/jMPCM98Q2GCWHWxTsm5HNpEPcyilXDl/6gAUerblTvG5uUy0mlIp8SRcTWO70ZPP5HF59PVc1oyj3wpiidhg=="}, "me": {"id": "************:<EMAIL>", "name": "<PERSON>", "lid": "**************:3@lid"}, "signalIdentities": [{"identifier": {"name": "************:<EMAIL>", "deviceId": 0}, "identifierKey": {"type": "<PERSON><PERSON><PERSON>", "data": "BWMsTxG55Au9R1gmiIyUDhB9QS+KlaTQUnsaMi4HHpdb"}}], "platform": "smba", "routingInfo": {"type": "<PERSON><PERSON><PERSON>", "data": "CAUIDQ=="}, "lastAccountSyncTimestamp": **********, "lastPropHash": "1Fcm9N", "myAppStateKeyId": "AAAAANSv"}