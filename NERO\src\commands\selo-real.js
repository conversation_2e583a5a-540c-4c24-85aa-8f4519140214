const { getSeloReal, listaSelosReais } = require('../utils/selos-reais');

module.exports = {
    name: 'selo-real',
    aliases: ['sealreal', 'real-seal'],
    description: 'Selos com informações REAIS verificadas',
    cooldown: 5,
    
    async execute({ sock, msg, args, reply }) {
        try {
            const remoteJid = msg.key.remoteJid;
            const tipo = args[0]?.toLowerCase();
            
            if (!tipo) {
                const menuText = `🔍 **SELOS COM WHATSAPP OFICIAL VERIFICADO**

✅ **DISPONÍVEIS:**
• \`!selo-real bb\` - 🏦 Banco do Brasil (61) 4004-0001
• \`!selo-real nubank\` - 💜 Nubank (11) 5180-7064

📊 **Comandos:**
• \`!selo-real lista\` - Ver detalhes de verificação
• \`!selo-real info\` - Sobre o sistema de verificação

🎯 **Critério:** Apenas empresas com WhatsApp oficial confirmado
🔍 **Fontes:** Sites oficiais das empresas
⚠️ **Garantia:** 100% números reais e verificados`;

                return reply(menuText);
            }

            switch (tipo) {
                case 'lista':
                case 'list':
                    let listaText = `📊 **SELOS VERIFICADOS**\n\n`;

                    listaSelosReais.verificados.forEach(selo => {
                        listaText += `✅ **${selo.nome}**\n`;
                        listaText += `📱 WhatsApp: ${selo.numero}\n`;
                        listaText += `🔗 Fonte: ${selo.fonte}\n`;
                        listaText += `📊 Status: ${selo.status}\n\n`;
                    });

                    listaText += `🎯 **Critérios de Verificação:**\n`;
                    listaText += `• Número oficial da empresa\n`;
                    listaText += `• Confirmado no site oficial\n`;
                    listaText += `• Testado e funcionando\n`;
                    listaText += `• Sem números falsos ou genéricos`;

                    await sock.sendMessage(remoteJid, {
                        text: listaText
                    }, { quoted: getSeloReal('bb') });
                    break;

                case 'info':
                case 'sobre':
                    const infoText = `🔍 **SISTEMA DE SELOS VERIFICADOS**

🎯 **Objetivo:**
Fornecer apenas selos com informações 100% reais e verificadas.

✅ **Processo de Verificação:**
1. 🔍 Pesquisa no site oficial da empresa
2. 📱 Confirmação do número de WhatsApp
3. 🧪 Teste de funcionamento
4. 📋 Documentação da fonte

🏆 **Garantias:**
• Apenas números oficiais das empresas
• Sem informações falsas ou genéricas
• Fontes documentadas e verificáveis
• Atualização constante

❌ **O que NÃO fazemos:**
• Números inventados ou falsos
• Selos genéricos sem base real
• Informações não verificadas

⚠️ **Importante:**
Se uma empresa não tem WhatsApp oficial, ela não terá selo!`;

                    await sock.sendMessage(remoteJid, {
                        text: infoText
                    }, { quoted: getSeloReal('nubank') });
                    break;

                // === SELOS VERIFICADOS ===
                case 'bb':
                case 'bancobrasil':
                    await sock.sendMessage(remoteJid, {
                        text: '🏦 **Banco do Brasil - VERIFICADO**\n\n✅ WhatsApp Oficial: (61) 4004-0001\n🔗 Fonte: bb.com.br/whatsapp-bb\n\n📱 Salve o número e inicie uma conversa!'
                    }, { quoted: getSeloReal('bb') });
                    break;

                case 'nubank':
                    await sock.sendMessage(remoteJid, {
                        text: '💜 **Nubank - VERIFICADO**\n\n✅ WhatsApp Oficial: (11) 5180-7064\n🔗 Fonte: comunidade.nubank.com.br\n\n📱 Número oficial confirmado!'
                    }, { quoted: getSeloReal('nubank') });
                    break;



                default:
                    await reply(`❌ Selo "${tipo}" não encontrado.\n\nUse \`!selo-real\` para ver todos os selos disponíveis.`);
            }

        } catch (error) {
            console.error('Erro no comando selo-real:', error);
            await reply('❌ Erro ao executar comando de selo real.');
        }
    }
};
