/**
 * NERO Bot - Sistema de Selos com Informações REAIS
 * Baseado em pesquisa web para garantir autenticidade
 */

/**
 * SELOS COM NÚMEROS REAIS VERIFICADOS
 */

// === BANCOS (NÚMEROS REAIS) ===

/**
 * Banco do Brasil - NÚMERO OFICIAL REAL
 * WhatsApp: (61) 4004-0001
 */
const bancoBrasilReal = {
  key: { participant: '<EMAIL>' },
  message: {
    liveLocationMessage: {
      caption: `🏦 Banco do Brasil | WhatsApp Oficial: (61) 4004-0001`
    }
  }
};

/**
 * Nubank - NÚMERO OFICIAL REAL
 * WhatsApp: (11) 5180-7064
 */
const nubankReal = {
  key: { participant: '<EMAIL>' },
  message: {
    liveLocationMessage: {
      caption: `💜 Nubank | WhatsApp Oficial: (11) 5180-7064`
    }
  }
};

// === APENAS SELOS COM WHATSAPP OFICIAL VERIFICADO ===

/**
 * Coleção de selos com informações REAIS e VERIFICADAS
 * APENAS empresas com WhatsApp oficial confirmado
 */
const selosReais = {
  bb: bancoBrasilReal,
  nubank: nubankReal
};

/**
 * Função para obter selo real
 */
function getSeloReal(type) {
  return selosReais[type] || selosReais.bb; // Fallback para BB se não encontrar
}

/**
 * Lista de selos disponíveis com status de verificação
 * APENAS selos com WhatsApp oficial verificado
 */
const listaSelosReais = {
  verificados: [
    { comando: 'bb', nome: 'Banco do Brasil', numero: '(61) 4004-0001', status: '✅ Verificado', fonte: 'bb.com.br/whatsapp-bb' },
    { comando: 'nubank', nome: 'Nubank', numero: '(11) 5180-7064', status: '✅ Verificado', fonte: 'comunidade.nubank.com.br' }
  ]
};

module.exports = {
  getSeloReal,
  listaSelosReais,
  selosReais
};
