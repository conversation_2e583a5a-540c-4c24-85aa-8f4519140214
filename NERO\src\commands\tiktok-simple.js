const axios = require('axios');
const { validateCommandArgs } = require('../utils/validators');
const logger = require('../utils/logger');

module.exports = {
    name: 'tiktok-simple',
    aliases: ['tks', 'tiktoksimple'],
    description: 'Download simples de vídeos do TikTok (versão de teste)',
    category: 'downloads',
    cooldown: 10,
    
    async execute({ sock, msg, args, reply, selo }) {
        try {
            // Validar argumentos
            const validation = validateCommandArgs(args, { minArgs: 1 });
            if (!validation.valid) {
                return reply(`❌ **Uso correto:** \`!tiktok-simple [URL do TikTok]\`\n\n**Exemplo:** \`!tiktok-simple https://vm.tiktok.com/ZMhxxx\`\n\n💡 **Versão de teste simplificada**`);
            }

            const url = args.join(' ').trim();

            // Validação básica de URL
            if (!url.includes('tiktok.com') && !url.includes('vm.tiktok.com') && !url.includes('vt.tiktok.com')) {
                return reply(`❌ **URL inválida!**\n\nPor favor, forneça uma URL válida do TikTok.`);
            }

            logger.info(`[TIKTOK-SIMPLE] Download iniciado: ${url}`);

            // Reação de processamento
            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "🔍", key: msg.key }
            });

            await reply(`📱 **Testando download do TikTok...**\n\n🔗 **URL:** ${url}\n⏳ Processando...`);

            // Tentar múltiplas APIs de forma simples
            const result = await testTikTokDownload(url);

            if (!result.success) {
                await sock.sendMessage(msg.key.remoteJid, {
                    react: { text: "❌", key: msg.key }
                });

                return reply(`❌ **Teste falhou**\n\n${result.error}\n\n🔧 **Status das APIs:**\n${result.apiStatus || 'Não disponível'}`);
            }

            // Reação de sucesso
            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "✅", key: msg.key }
            });

            // Se temos URL de download, tentar enviar
            if (result.downloadUrl) {
                try {
                    await sock.sendMessage(msg.key.remoteJid, {
                        video: { url: result.downloadUrl },
                        mimetype: 'video/mp4',
                        caption: `📱 **TikTok Download - TESTE**\n\n✅ **Status:** Sucesso\n🎯 **API:** ${result.apiUsed}\n📊 **Qualidade:** ${result.quality || 'Padrão'}\n\n🤖 **NERO TikTok Downloader (Teste)**`
                    }, { quoted: selo });
                } catch (sendError) {
                    await reply(`✅ **API funcionou!**\n\n🎯 **API:** ${result.apiUsed}\n🔗 **URL obtida:** ${result.downloadUrl.substring(0, 50)}...\n\n❌ **Erro no envio:** ${sendError.message}\n\n💡 A API está funcionando, mas houve problema no envio do vídeo.`);
                }
            } else {
                await reply(`✅ **Teste concluído!**\n\n🎯 **API:** ${result.apiUsed}\n📊 **Status:** ${result.message}\n\n💡 API respondeu corretamente.`);
            }

            logger.info(`[TIKTOK-SIMPLE] Teste concluído: ${result.apiUsed}`);

        } catch (error) {
            logger.error('Erro no comando tiktok-simple:', error);
            
            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "❌", key: msg.key }
            });

            reply('❌ **Erro interno no teste**\n\nVerifique os logs para mais detalhes.');
        }
    }
};

/**
 * Testa download do TikTok com múltiplas APIs
 * @param {string} url - URL do TikTok
 * @returns {Promise<Object>}
 */
async function testTikTokDownload(url) {
    const apis = [
        {
            name: 'TikWM',
            test: async () => {
                const response = await axios.get('https://tikwm.com/api/', {
                    params: { url: url, hd: 1 },
                    timeout: 15000,
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });
                
                const data = response.data;
                if (data && data.code === 0 && data.data) {
                    return {
                        success: true,
                        downloadUrl: data.data.hdplay || data.data.play,
                        quality: data.data.hdplay ? 'HD' : 'SD'
                    };
                }
                return { success: false, error: 'Resposta inválida' };
            }
        },
        {
            name: 'TiklyDown',
            test: async () => {
                const response = await axios.get(`https://api.tiklydown.eu.org/api/download?url=${encodeURIComponent(url)}`, {
                    timeout: 15000,
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });
                
                const data = response.data;
                if (data && data.video) {
                    return {
                        success: true,
                        downloadUrl: data.video.noWatermark || data.video.watermark,
                        quality: 'HD'
                    };
                }
                return { success: false, error: 'Resposta inválida' };
            }
        }
    ];

    let apiStatus = [];
    
    for (const api of apis) {
        try {
            logger.info(`[TIKTOK-SIMPLE] Testando ${api.name}...`);
            
            const result = await api.test();
            
            if (result.success) {
                apiStatus.push(`✅ ${api.name}: OK`);
                return {
                    success: true,
                    apiUsed: api.name,
                    downloadUrl: result.downloadUrl,
                    quality: result.quality,
                    apiStatus: apiStatus.join('\n')
                };
            } else {
                apiStatus.push(`❌ ${api.name}: ${result.error}`);
            }
            
        } catch (error) {
            apiStatus.push(`❌ ${api.name}: ${error.message}`);
            logger.warn(`[TIKTOK-SIMPLE] ${api.name} falhou:`, error.message);
        }
    }

    return {
        success: false,
        error: 'Todas as APIs falharam',
        apiStatus: apiStatus.join('\n')
    };
}
