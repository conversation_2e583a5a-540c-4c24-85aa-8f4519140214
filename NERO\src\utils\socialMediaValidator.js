/**
 * NERO Bot - Validador de URLs de Redes Sociais
 * Sistema de validação especializado para URLs de plataformas sociais
 */

const logger = require('./logger');

/**
 * Detecta automaticamente a plataforma baseada na URL
 * @param {string} url - URL para analisar
 * @returns {Object}
 */
function detectPlatform(url) {
    if (!url || typeof url !== 'string') {
        return { platform: null, valid: false, reason: 'URL inválida' };
    }

    const platforms = {
        tiktok: {
            patterns: [
                /^https?:\/\/(www\.)?tiktok\.com\/@[\w.-]+\/video\/\d+/,
                /^https?:\/\/vm\.tiktok\.com\/[\w]+/,
                /^https?:\/\/vt\.tiktok\.com\/[\w]+/,
                /^https?:\/\/m\.tiktok\.com\/v\/\d+/
            ],
            name: 'TikT<PERSON>'
        },
        instagram: {
            patterns: [
                /^https?:\/\/(www\.)?instagram\.com\/p\/[\w-]+/,
                /^https?:\/\/(www\.)?instagram\.com\/reel\/[\w-]+/,
                /^https?:\/\/(www\.)?instagram\.com\/tv\/[\w-]+/,
                /^https?:\/\/(www\.)?instagram\.com\/stories\/[\w.-]+\/\d+/
            ],
            name: 'Instagram'
        },
        twitter: {
            patterns: [
                /^https?:\/\/(www\.)?(twitter|x)\.com\/[\w]+\/status\/\d+/,
                /^https?:\/\/mobile\.twitter\.com\/[\w]+\/status\/\d+/,
                /^https?:\/\/t\.co\/[\w]+/
            ],
            name: 'Twitter/X'
        },
        facebook: {
            patterns: [
                /^https?:\/\/(www\.|m\.)?facebook\.com\/watch\/\?v=\d+/,
                /^https?:\/\/(www\.|m\.)?facebook\.com\/[\w.-]+\/videos\/\d+/,
                /^https?:\/\/fb\.watch\/[\w-]+/,
                /^https?:\/\/(www\.|m\.)?facebook\.com\/video\.php\?v=\d+/
            ],
            name: 'Facebook'
        }
    };

    for (const [platform, config] of Object.entries(platforms)) {
        const isValid = config.patterns.some(pattern => pattern.test(url));
        if (isValid) {
            return {
                platform,
                name: config.name,
                valid: true,
                url: url
            };
        }
    }

    return {
        platform: null,
        valid: false,
        reason: 'Plataforma não suportada ou URL inválida'
    };
}

/**
 * Valida e normaliza URL de rede social
 * @param {string} url - URL para validar
 * @param {string} expectedPlatform - Plataforma esperada (opcional)
 * @returns {Object}
 */
function validateSocialURL(url, expectedPlatform = null) {
    try {
        // Detectar plataforma
        const detection = detectPlatform(url);
        
        if (!detection.valid) {
            return detection;
        }

        // Verificar se é a plataforma esperada
        if (expectedPlatform && detection.platform !== expectedPlatform) {
            return {
                valid: false,
                reason: `URL não é do ${expectedPlatform}. Detectado: ${detection.name}`
            };
        }

        // Normalizar URL
        const normalizedUrl = normalizeURL(url, detection.platform);

        return {
            valid: true,
            platform: detection.platform,
            name: detection.name,
            originalUrl: url,
            normalizedUrl: normalizedUrl,
            metadata: extractMetadata(url, detection.platform)
        };

    } catch (error) {
        logger.error('Erro na validação de URL social:', error);
        return {
            valid: false,
            reason: 'Erro interno na validação'
        };
    }
}

/**
 * Normaliza URL removendo parâmetros desnecessários
 * @param {string} url - URL original
 * @param {string} platform - Plataforma detectada
 * @returns {string}
 */
function normalizeURL(url, platform) {
    try {
        const urlObj = new URL(url);
        
        switch (platform) {
            case 'tiktok':
                // Remover parâmetros de tracking
                urlObj.searchParams.delete('_r');
                urlObj.searchParams.delete('u_code');
                urlObj.searchParams.delete('preview_pb');
                break;
                
            case 'instagram':
                // Remover parâmetros de tracking
                urlObj.searchParams.delete('igshid');
                urlObj.searchParams.delete('utm_source');
                urlObj.searchParams.delete('utm_medium');
                break;
                
            case 'twitter':
                // Remover parâmetros de tracking
                urlObj.searchParams.delete('s');
                urlObj.searchParams.delete('t');
                urlObj.searchParams.delete('ref_src');
                break;
                
            case 'facebook':
                // Remover parâmetros de tracking
                urlObj.searchParams.delete('__tn__');
                urlObj.searchParams.delete('__cft__');
                urlObj.searchParams.delete('fb_source');
                break;
        }
        
        return urlObj.toString();
    } catch (error) {
        return url; // Retornar URL original se falhar
    }
}

/**
 * Extrai metadados básicos da URL
 * @param {string} url - URL para analisar
 * @param {string} platform - Plataforma
 * @returns {Object}
 */
function extractMetadata(url, platform) {
    const metadata = {
        platform,
        type: 'unknown'
    };

    try {
        switch (platform) {
            case 'tiktok':
                if (url.includes('/video/')) {
                    metadata.type = 'video';
                    const videoId = url.match(/\/video\/(\d+)/)?.[1];
                    if (videoId) metadata.videoId = videoId;
                }
                break;
                
            case 'instagram':
                if (url.includes('/p/')) {
                    metadata.type = 'post';
                } else if (url.includes('/reel/')) {
                    metadata.type = 'reel';
                } else if (url.includes('/tv/')) {
                    metadata.type = 'igtv';
                } else if (url.includes('/stories/')) {
                    metadata.type = 'story';
                }
                break;
                
            case 'twitter':
                if (url.includes('/status/')) {
                    metadata.type = 'tweet';
                    const tweetId = url.match(/\/status\/(\d+)/)?.[1];
                    if (tweetId) metadata.tweetId = tweetId;
                }
                break;
                
            case 'facebook':
                if (url.includes('/watch/') || url.includes('/videos/')) {
                    metadata.type = 'video';
                }
                break;
        }
    } catch (error) {
        logger.warn('Erro ao extrair metadados:', error);
    }

    return metadata;
}

/**
 * Verifica se a URL é de uma plataforma suportada
 * @param {string} url - URL para verificar
 * @returns {boolean}
 */
function isSupportedPlatform(url) {
    const detection = detectPlatform(url);
    return detection.valid;
}

/**
 * Obtém lista de plataformas suportadas
 * @returns {Array}
 */
function getSupportedPlatforms() {
    return [
        {
            platform: 'tiktok',
            name: 'TikTok',
            command: '!tiktok',
            description: 'Vídeos do TikTok (sem marca d\'água quando possível)'
        },
        {
            platform: 'instagram',
            name: 'Instagram',
            command: '!instagram',
            description: 'Posts, Reels, IGTV e Stories do Instagram'
        },
        {
            platform: 'twitter',
            name: 'Twitter/X',
            command: '!twitter',
            description: 'Vídeos e imagens do Twitter/X'
        },
        {
            platform: 'facebook',
            name: 'Facebook',
            command: '!facebook',
            description: 'Vídeos públicos do Facebook'
        }
    ];
}

/**
 * Gera sugestão de comando baseado na URL
 * @param {string} url - URL fornecida
 * @returns {string}
 */
function suggestCommand(url) {
    const detection = detectPlatform(url);
    
    if (!detection.valid) {
        const platforms = getSupportedPlatforms();
        return `❌ **URL não suportada**\n\n**Plataformas suportadas:**\n${platforms.map(p => `• ${p.command} - ${p.description}`).join('\n')}`;
    }

    return `💡 **Comando sugerido:** \`!${detection.platform} ${url}\`\n\n📱 **Plataforma detectada:** ${detection.name}`;
}

module.exports = {
    detectPlatform,
    validateSocialURL,
    normalizeURL,
    extractMetadata,
    isSupportedPlatform,
    getSupportedPlatforms,
    suggestCommand
};
