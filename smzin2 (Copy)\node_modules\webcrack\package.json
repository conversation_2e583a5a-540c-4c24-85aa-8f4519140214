{"name": "webcrack", "version": "2.15.0", "description": "Deobfuscate, unminify and unpack bundled javascript", "author": "j4k0xb", "license": "MIT", "type": "module", "bin": "src/cli-wrapper.js", "main": "dist/index.cjs", "types": "dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "files": ["dist", "src/cli-wrapper.js"], "repository": {"type": "git", "url": "https://github.com/j4k0xb/webcrack"}, "homepage": "https://webcrack.netlify.app", "keywords": ["webpack", "bundle", "extract", "reverse-engineering", "ast", "deobfuscation", "unpack", "debundle", "deobfuscator", "unminify", "unbundle"], "dependencies": {"@babel/generator": "^7.26.0", "@babel/helper-validator-identifier": "^7.25.9", "@babel/parser": "^7.26.1", "@babel/template": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/types": "^7.26.0", "@codemod/matchers": "^1.7.1", "commander": "^12.1.0", "debug": "^4.3.7", "isolated-vm": "^5.0.1"}, "devDependencies": {"@types/babel__generator": "^7.6.8", "@types/babel__helper-validator-identifier": "^7.15.2", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/debug": "^4.1.12", "@types/node": "^22.8.1", "esbuild": "^0.24.0", "typescript": "^5.6.3", "@webcrack/eslint-config": "0.0.0", "@webcrack/typescript-config": "0.0.0"}, "scripts": {"build": "node esbuild.config.js && tsc -p tsconfig.build.json", "watch": "node esbuild.config.js --watch", "start": "node dist/cli.js", "tmp": "node esbuild.config.js && node --enable-source-maps dist/cli.js tmp/test.js -f -o tmp/webcrack-out", "lint": "eslint src test", "lint:fix": "eslint src test --fix", "test": "vitest --no-isolate"}}