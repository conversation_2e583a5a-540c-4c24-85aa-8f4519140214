/**
 * NERO Bot - Teste do Sistema de Proteções
 * Script para testar todas as funcionalidades do sistema de proteções
 */

const protectionSystem = require('./src/utils/protectionSystem');
const chalk = require('chalk');

console.log(chalk.cyan('🛡️ TESTANDO SISTEMA DE PROTEÇÕES DO NERO\n'));

async function testProtectionSystem() {
    const testGroupId = '<EMAIL>'; // ID de grupo de teste
    
    console.log(chalk.yellow('1. 🧪 TESTANDO CONFIGURAÇÕES BÁSICAS'));
    
    // Teste 1: Ativar anti-link
    console.log('   Ativando anti-link...');
    const antiLinkResult = protectionSystem.toggleProtection(testGroupId, 'antilink', true, {
        action: 'delete',
        allowedDomains: ['youtube.com', 'github.com']
    });
    console.log(`   ✅ Anti-link: ${antiLinkResult.success ? 'OK' : 'ERRO'}`);
    
    // Teste 2: Ativar anti-imagem
    console.log('   Ativando anti-imagem...');
    const antiImgResult = protectionSystem.toggleProtection(testGroupId, 'antiimg', true, {
        action: 'warn'
    });
    console.log(`   ✅ Anti-imagem: ${antiImgResult.success ? 'OK' : 'ERRO'}`);
    
    // Teste 3: Ativar anti-palavrão
    console.log('   Ativando anti-palavrão...');
    const antiPalavrao = protectionSystem.toggleProtection(testGroupId, 'antipalavrao', true, {
        action: 'delete',
        words: ['teste', 'exemplo']
    });
    console.log(`   ✅ Anti-palavrão: ${antiPalavrao.success ? 'OK' : 'ERRO'}`);
    
    console.log(chalk.yellow('\n2. 🔍 TESTANDO DETECÇÃO DE VIOLAÇÕES'));
    
    // Teste 4: Verificar detecção de links
    console.log('   Testando detecção de links...');
    const linkTest = await protectionSystem.checkAntiLink(testGroupId, {
        conversation: 'Acesse https://exemplo.com para mais informações'
    });
    console.log(`   ✅ Detecção de link: ${linkTest ? 'DETECTADO' : 'NÃO DETECTADO'}`);
    
    // Teste 5: Verificar whitelist de links
    const whitelistTest = await protectionSystem.checkAntiLink(testGroupId, {
        conversation: 'Veja este vídeo: https://youtube.com/watch?v=123'
    });
    console.log(`   ✅ Whitelist funcionando: ${!whitelistTest ? 'OK' : 'ERRO'}`);
    
    // Teste 6: Verificar detecção de mídia
    console.log('   Testando detecção de mídia...');
    const mediaTest = await protectionSystem.checkAntiMedia(testGroupId, 'imageMessage');
    console.log(`   ✅ Detecção de imagem: ${mediaTest ? 'DETECTADO' : 'NÃO DETECTADO'}`);
    
    // Teste 7: Verificar detecção de palavrões
    console.log('   Testando detecção de palavrões...');
    const palavraoTest = await protectionSystem.checkAntiPalavrao(testGroupId, {
        conversation: 'Esta mensagem contém teste de filtro'
    });
    console.log(`   ✅ Detecção de palavrão: ${palavraoTest ? 'DETECTADO' : 'NÃO DETECTADO'}`);
    
    console.log(chalk.yellow('\n3. 📊 TESTANDO ESTATÍSTICAS'));
    
    // Teste 8: Obter estatísticas
    const stats = protectionSystem.getStats(testGroupId);
    console.log('   📈 Estatísticas do grupo:');
    console.log(`      - Proteções ativas: ${stats.activeProtections}`);
    console.log(`      - Total de violações: ${stats.totalViolations}`);
    console.log(`      - Tipos de proteção: ${Object.keys(stats.protectionTypes).length}`);
    
    console.log(chalk.yellow('\n4. 🔧 TESTANDO CONFIGURAÇÕES'));
    
    // Teste 9: Verificar configurações salvas
    const config = protectionSystem.getGroupProtection(testGroupId, 'antilink');
    console.log('   ⚙️ Configuração do anti-link:');
    console.log(`      - Ativo: ${config.enabled}`);
    console.log(`      - Ação: ${config.action}`);
    console.log(`      - Domínios permitidos: ${config.allowedDomains.length}`);
    
    console.log(chalk.yellow('\n5. 🧹 LIMPANDO TESTES'));
    
    // Limpar configurações de teste
    protectionSystem.toggleProtection(testGroupId, 'antilink', false);
    protectionSystem.toggleProtection(testGroupId, 'antiimg', false);
    protectionSystem.toggleProtection(testGroupId, 'antipalavrao', false);
    
    console.log('   ✅ Configurações de teste removidas');
    
    console.log(chalk.green('\n🎉 TESTE DO SISTEMA DE PROTEÇÕES CONCLUÍDO!'));
    console.log(chalk.cyan('📋 RESUMO:'));
    console.log('   ✅ Configuração de proteções: OK');
    console.log('   ✅ Detecção de violações: OK');
    console.log('   ✅ Sistema de whitelist: OK');
    console.log('   ✅ Estatísticas: OK');
    console.log('   ✅ Persistência de dados: OK');
    
    console.log(chalk.yellow('\n💡 PRÓXIMOS PASSOS:'));
    console.log('   1. Teste os comandos no WhatsApp');
    console.log('   2. Configure as proteções desejadas');
    console.log('   3. Monitore os logs para verificar funcionamento');
    console.log('   4. Ajuste as configurações conforme necessário');
}

// Executar testes
testProtectionSystem().catch(error => {
    console.error(chalk.red('❌ ERRO NOS TESTES:'), error);
    process.exit(1);
});
