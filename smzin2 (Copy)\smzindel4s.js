//zapzap: 5511954801380
require("./config")
const { normalizeMessageContent,generateMessageIDV2, generateMessageID, WA_DEFAULT_EPHEMERAL, getAggregateVotesInPollMessage, generateWAMessageFromContent, proto, generateWAMessageContent, generateWAMessage, prepareWAMessageMedia, downloadContentFromMessage, areJidsSameUser, getContentType, useMultiFileAuthState, makeWASocket, fetchLatestBaileysVersion, makeCacheableSignalKeyStore, makeWaSocket } = require("@adiwajshing/baileys")
const FormData = require('form-data')
const fs = require('fs')
const moment = require('moment-timezone');
const pino = require('pino')
const logger = pino({ level: 'debug' });

module.exports = async (conn, m, chatUpdate) => {
try {
const from = m.key.remoteJid
const info = m
var body = (m.mtype === 'interactiveResponseMessage') ? JSON.parse(m.message.interactiveResponseMessage.nativeFlowResponseMessage.paramsJson).id:(m.mtype === 'conversation') ? m.message.conversation :(m.mtype === 'deviceSentMessage') ? m.message.extendedTextMessage.text :(m.mtype == 'imageMessage') ? m.message.imageMessage.caption :(m.mtype == 'videoMessage') ? m.message.videoMessage.caption : (m.mtype == 'extendedTextMessage') ? m.message.extendedTextMessage.text : (m.mtype == 'buttonsResponseMessage') ? m.message.buttonsResponseMessage.selectedButtonId : (m.mtype == 'listResponseMessage') ? m.message.listResponseMessage.singleSelectReply.selectedRowId : (m.mtype == 'templateButtonReplyMessage') ? m.message.templateButtonReplyMessage.selectedId : (m.mtype == 'messageContextInfo') ? (m.message.buttonsResponseMessage?.selectedButtonId || m.message.listResponseMessage?.singleSelectReply.selectedRowId || m.text) : ""
const { smsg, fetchJson, getBuffer, fetchBuffer, getGroupAdmins, TelegraPh, isUrl, hitungmundur, sleep, clockString, checkBandwidth, runtime, tanggal, getRandom } = require('./lib/myfunc')
var budy = (typeof m.text == 'string' ? m.text: '')
var prefix = global.prefa ? /^[°•π÷×¶∆£¢€¥®™+✓_=|~!?@#$%^&.©^]/gi.test(body) ? body.match(/^[°•π÷×¶∆£¢€¥®™+✓_=|~!?@#$%^&.©^]/gi)[0] : "" : global.prefa ?? global.prefix
const isCmd = body.startsWith(prefix);
const command = isCmd ? body.slice(prefix.length).trim().split(' ').shift().toLowerCase() : '';
const args = body.trim().split(/ +/).slice(1)
const text = args.join(" ")
const q = args.join(" ")
const sender = m.key.fromMe ? (conn.user.id.split(':')[0]+'@s.whatsapp.net' || conn.user.id) : (m.key.participant || m.key.remoteJid)
const botNumber = await conn.decodeJid(conn.user.id)
const senderNumber = sender.split('@')[0]
const userList = [
"<EMAIL>",
"<EMAIL>",
"<EMAIL>"
];
const isCreator = userList.includes(sender);
const pushname = m.pushName || `${senderNumber}`
const isBot = m.key.fromMe ? true : false
const os = require('os')
const time = hora = moment.tz('America/Sao_Paulo').format('HH:mm:ss');
const data = date = dataa = moment.tz('America/Sao_Paulo').format('DD/MM/YY');

const quoted = m.quoted ? m.quoted : m
const mime = (quoted.msg || quoted).mimetype || ''
const groupMetadata = m.isGroup ? await conn.groupMetadata(from).catch(e => {}) : ''
const groupName = m.isGroup ? groupMetadata.subject : ''
const participants = m.isGroup ? await groupMetadata.participants : ''
const PrecisaSerMembro = m.isGroup ? await participants.filter(v => v.admin === null).map(v => v.id) : [];
const groupAdmins = m.isGroup ? await getGroupAdmins(participants) : ''
const isBotAdmins = m.isGroup ? groupAdmins.includes(botNumber) : false
const isAdmins = m.isGroup ? groupAdmins.includes(m.sender) : false
const xtime = moment.tz('Asia/Kolkata').format('HH:mm:ss')
const xdate = moment.tz('Asia/Kolkata').format('DD/MM/YYYY')
const time2 = moment().tz('Asia/Kolkata').format('HH:mm:ss')
const pickRandom = (arr) => {return arr[Math.floor(Math.random() * arr.length)]}
const dispositivo = '' + (m.key.id.length > 21 ? 'Android' : m.key.id.substring(0, 2) == '3A' ? 'IOS' : 'WhatsApp web');
const numeroFormatado = q.replace(/[^\d]/g, '');
const numi = numeroFormatado + '@s.whatsapp.net'
const enviarVideoButton = async (id, link, captionText, idbutton, displayButton) => {await conn.sendMessage(id, {video: { url: link},caption: captionText,buttons: [{buttonId: idbutton,buttonText: { displayText: displayButton },type: 1,}],headerType: 1,viewOnce: true,})};
const enviariMageButton = async (id, link, captionText, idbutton, displayButton) => {await conn.sendMessage(id, {image: { url: link},caption: captionText,buttons: [{buttonId: idbutton,buttonText: { displayText: displayButton },type: 1,}],headerType: 1,viewOnce: true,})};
const enviarTextButton = async (id, texto, footertexto, idbutton, displayButton) => {await conn.sendMessage(id, {text: texto,footer: footertexto,buttons: [{buttonId: idbutton,buttonText: { displayText: displayButton },type: 1,}],headerType: 1,viewOnce: true,});};
const enviar = (texto) => {conn.sendMessage(from, { text: texto})}
const reply = (texto) => {conn.sendMessage(from, { text: texto})}

const RESET = '\x1b[0m';
const GREEN = '\x1b[32m';
const BLUE = '\x1b[34m';
const RED = '\x1b[31m';
const YELLOW = '\x1b[33m';
const CYAN = '\x1b[36m';
const WHITE = '\x1b[37m';

if (m.message) {
console.log(`
╭─────────────────────────────────
│
│〔 ${RED}PRIVADO${RESET} 〕: ${WHITE}${from}${RESET}
│
│〔 ${RED}DE${RESET} 〕: ${YELLOW}${sender}${RESET}
│〔 ${RED}MENSAGEM${RESET} 〕: ${GREEN}${body.length > 90 ? "" : body}${RESET}
│〔 ${RED}NiCK${RESET} 〕: ${GREEN}${pushname}${RESET}
│〔 ${RED}TYPE${RESET} 〕: ${GREEN}${m.mtype}${RESET}
│〔 ${RED}DiSPOSiTiVO${RESET} 〕: ${GREEN}${dispositivo}${RESET}
╰─────────────────────────────────`) 
}

const bytesToSize = (bytes) => {const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];if (bytes === 0) return '0 Byte';const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)), 10);return `${Math.round(bytes / Math.pow(1024, i), 2)} ${sizes[i]}`;};
const used = process.memoryUsage();
const cpus = os.cpus().map(cpu => {cpu.total = Object.keys(cpu.times).reduce((last, type) => last + cpu.times[type], 0);return cpu;});
const cpu = cpus.reduce((last, cpu, _, {length}) => {last.total += cpu.total;last.speed += cpu.speed / length;last.times.user += cpu.times.user;last.times.nice += cpu.times.nice;last.times.sys += cpu.times.sys;last.times.idle += cpu.times.idle;last.times.irq += cpu.times.irq; return last;}, {speed: 0,total: 0,times: {user: 0,nice: 0,sys: 0,idle: 0,irq: 0}});
const timestamp = new Date().getTime();
const latencia = timestamp - new Date().getTime();
const neww = Date.now();
const oldd = Date.now();
function timefunction(seconds) {seconds = Number(seconds);var d = Math.floor(seconds / (3600 * 24));var h = Math.floor(seconds % (3600 * 24) / 3600);var m = Math.floor(seconds % 3600 / 60);var s = Math.floor(seconds % 60);var dDisplay = d > 0 ? d + (d == 1 ? " Dia, " : " Dias, ") : "00 dias, ";var hDisplay = h > 0 ? h + (h == 1 ? " Hora, " : " Horas, ") : "00 horas, ";var mDisplay = m > 0 ? m + (m == 1 ? " Minuto, " : " Minutos, ") : "00 minutos, ";var sDisplay = s > 0 ? s + (s == 1 ? " Segundo" : " Segundos") : "00 segundos";return `${dDisplay}${hDisplay}${mDisplay}${sDisplay}`;}
const uptimeSeconds = Math.floor(process.uptime());
const uptimeFormatted = timefunction(uptimeSeconds);
const iphost = await fetchJson(`https://api.ipify.org/?format=json`)

const UsuarioVip = JSON.parse(fs.readFileSync('./dados/vip.json'))
const isVip = UsuarioVip.includes(sender)


function adicionarUsuarioVIP(usuario) {
  // Carrega os dados atuais, se existirem
  let usuariosVIP = [];
  try {
    const dados = fs.readFileSync('./dados/vip.json');
    usuariosVIP = JSON.parse(dados);
  } catch (error) {
    // Arquivo ainda não existe ou está vazio
  }

  // Adiciona o usuário à lista de usuários VIP
  if (!usuariosVIP.includes(usuario)) {
    usuariosVIP.push(usuario);
    fs.writeFileSync('./dados/vip.json', JSON.stringify(usuariosVIP, null, 2));
    enviar(`${pushname} adicionou o ${usuario.replace(/[^0-9]/g, '')} como VIP ✅`);
  } else {
    enviar(`Este Usuário ${usuario.replace(/[^0-9]/g, '')} já é VIP ✅`);
  }
}

// Função para deletar um usuário VIP
function deletarUsuarioVIP(usuario) {
  let usuariosVIP = [];
  try {
    const dados = fs.readFileSync('./dados/vip.json');
    usuariosVIP = JSON.parse(dados);
    const index = usuariosVIP.indexOf(usuario);
    if (index !== -1) {
      usuariosVIP.splice(index, 1);
      fs.writeFileSync('./dados/vip.json', JSON.stringify(usuariosVIP, null, 2));
      enviar(`o Usuário ${usuario.replace(/[^0-9]/g, '')} foi removido dos VIPs com sucesso ✅\n ~~> bye 👋 ${usuario.replace(/[^0-9]/g, '')} `);
    } else {
      enviar(`O Usuário ${usuario.replace(/[^0-9]/g, '')} nunca foi ViP`);
    }
  } catch (error) {
    enviar('Erro ao acessar o arquivo de usuários VIP.');
  }
}
switch(command) {

            case 'button':
                await conn.relayMessage(from, proto.Message.fromObject({
                    viewOnceMessage: {
                        message: {
                            buttonsMessage: {
                                contentText: 'texto acima do footer',
                                footerText: "footer do botão",
                                buttons: [{
                                    buttonId: `${prefixo}ping`,
                                    buttonText: {
                                        displayText: " ' ping"
                                    },
                                    type: "RESPONSE",
                                }
                                ],
                                headerType: 1
                            }
                        }
                    }
                }), {});
                break;
                
                
   
///vip///
case 'addvip': {
if (!isBot && !isCreator) return enviar(' apenas dono')
const hors = moment.tz('America/Sao_Paulo').format('HH:mm:ss');
if(!q) return enviar(`Digite o número do usuário que você deseja adicionar como vip!\n\nExemplo: ${prefix + command} 55000`)

const numervip = q.replace(/[^0-9]/g, '');

adicionarUsuarioVIP(`${numervip + "@s.whatsapp.net"}`);

                }
break

case 'delvip': {

if (!isCreator && !isBot) return enviar('apenas dono')
X = q.replace(/[^0-9]/g,'')+"@s.whatsapp.net"
if(!q) return enviar(`Digite o número do usuário que você deseja deletar como vip!\n\nExemplo: ${prefix + command} 55000`)
const numerovip = q.replace(/[^0-9]/g, '');

deletarUsuarioVIP(`${numerovip + "@s.whatsapp.net"}`);               
pato.sendMessage(X, {text: `Olá, aparentemente seu plano já acabou, para renovar seu plano envie o Pix e o novo comprovante novamente.\n🗒️› ${X} \n`})
}
break
///fim///

default:
}
} catch(e) {
console.log(e)
}
}

let file = require.resolve(__filename)
fs.watchFile(file, () => {
fs.unwatchFile(file)
console.log(`Update ${__filename}`)
delete require.cache[file]
require(file)
})
