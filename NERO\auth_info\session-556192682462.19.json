{"_sessions": {"BS060jk5rMpKA+pB4PjOHEI6eIPYNAOqkIXn7x7nDYs+": {"registrationId": 181, "currentRatchet": {"ephemeralKeyPair": {"pubKey": "BfQqAvZeFFvKS1/hwBjcWFU0t9pkkoPL0y50nfD1NhEY", "privKey": "6Exilu0apW8SBqZbq6Bwb38s3qPzhM6YxY2P4l8YIkk="}, "lastRemoteEphemeralKey": "BV4g2rlcXWK32uUiQfE8Z+w+O5OBoWGaNIGfy+2rfTsh", "previousCounter": 0, "rootKey": "zhfNrGDE+J+4xUw86wCPIXaVgSE04rnjDkHd2k1ljCg="}, "indexInfo": {"baseKey": "BS060jk5rMpKA+pB4PjOHEI6eIPYNAOqkIXn7x7nDYs+", "baseKeyType": 1, "closed": -1, "used": 1755629848872, "created": 1755629848872, "remoteIdentityKey": "BeAdQEPw3d7Yq2cfoXBVHTQVIQ2rWleYNAIAKL2B3SsN"}, "_chains": {"BfQqAvZeFFvKS1/hwBjcWFU0t9pkkoPL0y50nfD1NhEY": {"chainKey": {"counter": 1, "key": "s6c/1tQE2WNKZNWVUEHOnsu7i0jtqs1vAcccZghTNiI="}, "chainType": 1, "messageKeys": {}}}, "pendingPreKey": {"signedKeyId": 1, "baseKey": "BS060jk5rMpKA+pB4PjOHEI6eIPYNAOqkIXn7x7nDYs+"}}}, "version": "v1"}