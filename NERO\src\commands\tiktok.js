const axios = require('axios');
const { validateURL, validateCommandArgs } = require('../utils/validators');
const logger = require('../utils/logger');

// Cache para evitar execuções duplicadas
const executionCache = new Map();

module.exports = {
    name: 'tiktok',
    aliases: ['tk', 'tt'],
    description: 'Download de vídeos do TikTok (sem marca d\'água quando possível)',
    category: 'downloads',
    cooldown: 15, // 15 segundos de cooldown

    async execute({ sock, msg, args, reply, selo }) {
        try {
            // Criar chave única para evitar execuções duplicadas
            const executionKey = `${msg.key.remoteJid}-${msg.key.id}-tiktok`;

            // Verificar se já está sendo executado
            if (executionCache.has(executionKey)) {
                logger.warn(`[TIKTOK] Execução duplicada bloqueada: ${executionKey}`);
                return;
            }

            // Marcar como em execução
            executionCache.set(executionKey, Date.now());

            // Limpar cache após 30 segundos
            setTimeout(() => {
                executionCache.delete(executionKey);
            }, 30000);

            // Validar argumentos
            const validation = validateCommandArgs(args, { minArgs: 1 });
            if (!validation.valid) {
                executionCache.delete(executionKey);
                return reply(`❌ **Uso correto:** \`!tiktok [URL do TikTok]\`\n\n**Exemplo:** \`!tiktok https://vm.tiktok.com/ZMhxxx\`\n\n💡 **Dica:** Cole o link do vídeo do TikTok`);
            }

            const url = args.join(' ').trim();

            // Validar URL
            const urlValidation = validateTikTokURL(url);
            if (!urlValidation.valid) {
                executionCache.delete(executionKey);
                return reply(`❌ **URL inválida!**\n\n${urlValidation.reason}\n\n**Formatos aceitos:**\n• https://www.tiktok.com/@user/video/xxx\n• https://vm.tiktok.com/xxx\n• https://vt.tiktok.com/xxx`);
            }

            logger.info(`[TIKTOK] Download iniciado: ${url}`);

            // Enviar APENAS uma mensagem de processamento
            await reply(`📱 **Baixando do TikTok...**\n\n🔗 **URL:** ${url}\n⏳ Aguarde, estou processando...`);

            // Reação de processamento
            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "🔍", key: msg.key }
            });

            // Tentar download usando múltiplas APIs
            const result = await downloadTikTokVideo(url);

            if (!result.success) {
                await sock.sendMessage(msg.key.remoteJid, {
                    react: { text: "❌", key: msg.key }
                });

                executionCache.delete(executionKey);
                return reply(`❌ **Erro no download**\n\n${result.error}\n\n💡 **Dicas:**\n• Verifique se o vídeo é público\n• Tente novamente em alguns minutos\n• Alguns vídeos podem estar protegidos`);
            }

            // Reação de sucesso
            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "✅", key: msg.key }
            });

            // Enviar vídeo (APENAS UMA VEZ)
            if (result.type === 'video') {
                await sock.sendMessage(msg.key.remoteJid, {
                    video: { url: result.downloadUrl },
                    mimetype: 'video/mp4',
                    caption: `📱 **TikTok Download Concluído**\n\n👤 **Autor:** ${result.author || 'N/A'}\n📝 **Descrição:** ${result.title || 'N/A'}\n🎵 **Música:** ${result.music || 'N/A'}\n📊 **Qualidade:** ${result.quality || 'HD'}\n💧 **Sem marca d'água:** ${result.noWatermark ? '✅' : '❌'}\n\n🤖 **NERO TikTok Downloader**`
                }, { quoted: selo });
            } else if (result.type === 'image') {
                // Para posts com múltiplas imagens
                for (let i = 0; i < result.images.length; i++) {
                    await sock.sendMessage(msg.key.remoteJid, {
                        image: { url: result.images[i] },
                        caption: i === 0 ? `📱 **TikTok Imagens (${result.images.length})**\n\n👤 **Autor:** ${result.author || 'N/A'}\n📝 **Descrição:** ${result.title || 'N/A'}\n\n🤖 **NERO TikTok Downloader**` : ''
                    }, { quoted: selo });
                }
            }

            logger.info(`[TIKTOK] Download concluído: ${result.author || 'unknown'}`);

            // Limpar cache de execução
            executionCache.delete(executionKey);

        } catch (error) {
            logger.error('Erro no comando tiktok:', error);

            // Limpar cache em caso de erro
            const executionKey = `${msg.key.remoteJid}-${msg.key.id}-tiktok`;
            executionCache.delete(executionKey);

            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "❌", key: msg.key }
            });

            reply('❌ **Erro interno**\n\nOcorreu um erro inesperado. Tente novamente em alguns minutos.');
        }
    }
};

/**
 * Valida URL do TikTok
 * @param {string} url - URL para validar
 * @returns {Object}
 */
function validateTikTokURL(url) {
    if (!url || typeof url !== 'string') {
        return { valid: false, reason: 'URL não fornecida' };
    }

    // Padrões de URL do TikTok
    const tiktokPatterns = [
        /^https?:\/\/(www\.)?tiktok\.com\/@[\w.-]+\/video\/\d+/,
        /^https?:\/\/vm\.tiktok\.com\/[\w]+/,
        /^https?:\/\/vt\.tiktok\.com\/[\w]+/,
        /^https?:\/\/m\.tiktok\.com\/v\/\d+/
    ];

    const isValid = tiktokPatterns.some(pattern => pattern.test(url));
    
    if (!isValid) {
        return { valid: false, reason: 'URL do TikTok inválida' };
    }

    return { valid: true };
}

/**
 * Baixa vídeo do TikTok usando API simples e funcional
 * @param {string} url - URL do TikTok
 * @returns {Promise<Object>}
 */
async function downloadTikTokVideo(url) {
    const apis = [
        {
            name: 'TikWM API',
            url: 'https://tikwm.com/api/',
            params: { url: url, hd: 1 }
        },
        {
            name: 'TiklyDown API',
            url: `https://api.tiklydown.eu.org/api/download?url=${encodeURIComponent(url)}`
        }
    ];

    for (const api of apis) {
        try {
            logger.info(`[TIKTOK] Tentando ${api.name}...`);

            let response;
            if (api.params) {
                response = await axios.get(api.url, {
                    params: api.params,
                    timeout: 20000,
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/json'
                    }
                });
            } else {
                response = await axios.get(api.url, {
                    timeout: 20000,
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/json'
                    }
                });
            }

            const data = response.data;
            logger.info(`[TIKTOK] Resposta de ${api.name}:`, JSON.stringify(data).substring(0, 200));

            // Processar resposta da TikWM API
            if (api.name === 'TikWM API' && data && data.code === 0 && data.data) {
                const videoData = data.data;
                if (videoData.play || videoData.hdplay) {
                    return {
                        success: true,
                        type: 'video',
                        downloadUrl: videoData.hdplay || videoData.play,
                        author: videoData.author?.nickname || 'TikTok User',
                        title: videoData.title || 'Vídeo do TikTok',
                        music: videoData.music || 'Som Original',
                        quality: videoData.hdplay ? 'HD' : 'SD',
                        noWatermark: !!videoData.hdplay
                    };
                }
            }

            // Processar resposta da TiklyDown API
            if (api.name === 'TiklyDown API' && data && data.video) {
                return {
                    success: true,
                    type: 'video',
                    downloadUrl: data.video.noWatermark || data.video.watermark,
                    author: data.author?.nickname || 'TikTok User',
                    title: data.title || 'Vídeo do TikTok',
                    music: data.music?.title || 'Som Original',
                    quality: 'HD',
                    noWatermark: !!data.video.noWatermark
                };
            }

        } catch (error) {
            logger.warn(`[TIKTOK] ${api.name} falhou:`, error.message);
            continue;
        }
    }

    return {
        success: false,
        error: 'Todas as APIs falharam. O vídeo pode estar privado ou as APIs estão indisponíveis.'
    };
}
