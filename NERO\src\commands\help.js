/**
 * NERO Bot - Comando Help Avançado
 * Sistema de ajuda inteligente com busca
 */

const { BOT_CONSTANTS, EMOJIS } = require('../constants');
const { isOwner } = require('../utils/permissions');

module.exports = {
    name: 'help',
    aliases: ['ajuda', 'comandos'],
    description: 'Sistema de ajuda avançado com busca',
    async execute({ sock, msg, args, reply, selo }) {
        const sender = msg.key.participant || msg.key.remoteJid;
        const isOwnerUser = isOwner(sender);
        const searchTerm = args[0]?.toLowerCase();

        try {
            if (searchTerm) {
                await showCommandHelp(reply, searchTerm, isOwnerUser);
            } else {
                await showMainHelp(reply, isOwnerUser);
            }

            // Reagir com emoji de ajuda
            await sock.sendMessage(msg.key.remoteJid, {
                react: {
                    text: EMOJIS.INFO,
                    key: msg.key
                }
            });

        } catch (error) {
            console.error('Erro no comando help:', error);
            await reply(`${EMOJIS.ERROR} Erro ao exibir ajuda.`);
        }
    }
};

/**
 * Mostrar ajuda principal
 */
const showMainHelp = async (reply, isOwner) => {
    const helpText = `╭─────────────────────╮\n` +
                    `│  ${EMOJIS.FIRE} *${BOT_CONSTANTS.NAME} HELP* ${EMOJIS.FIRE}  │\n` +
                    `╰─────────────────────╯\n\n` +
                    
                    `🔍 *Como usar:*\n` +
                    `• \`${BOT_CONSTANTS.PREFIX}help [comando]\` - Ajuda específica\n` +
                    `• \`${BOT_CONSTANTS.PREFIX}menu\` - Menu completo\n\n` +
                    
                    `🛡️ *ADMINISTRAÇÃO*\n` +
                    `• \`ban\` - Banir membro\n` +
                    `• \`promover\` - Promover a admin\n` +
                    `• \`rebaixar\` - Rebaixar admin\n` +
                    `• \`mute\` - Silenciar usuário\n` +
                    `• \`desmute\` - Dessilenciar usuário\n` +
                    `• \`deletar\` - Deletar mensagem\n` +
                    `• \`grupo\` - Abrir/fechar grupo\n` +
                    `• \`marcar\` - Marcar membros\n\n` +
                    
                    `⚙️ *UTILIDADES*\n` +
                    `• \`ping\` - Testar velocidade (modo visual)\n` +
                    `• \`ping-mode\` - Alternar estilo do ping\n` +
                    `• \`ping-test\` - Teste avançado de latência\n` +
                    `• \`info\` - Informações do bot\n` +
                    `• \`status\` - Status do sistema\n` +
                    `• \`criador\` - Contato do dev\n\n` +
                    
                    `🎮 *DIVERSÃO*\n` +
                    `• \`beijar\` - Beijar alguém\n` +
                    `• \`matar\` - Atacar alguém\n` +
                    `• \`tapanaraba\` - Dar um tapa\n\n` +
                    
                    `🤖 *IA NERO*\n` +
                    `• \`nero [mensagem]\` - Conversar\n` +
                    `• \`nero ![comando]\` - Executar via IA\n` +
                    `• \`!nero on/off\` - Ativar/desativar IA\n\n` +

                    `🌐 *BUSCA NA INTERNET*\n` +
                    `• \`nero, pesquise [termo]\` - Busca automática\n` +
                    `• \`nero, cotação do dólar\` - Info atual\n` +
                    `• \`nero, notícias do Brasil\` - Últimas notícias\n` +
                    `• \`!websearch on/off\` - Ativar/desativar busca\n` +
                    `• 🚀 Powered by SerpApi (Google)\n\n` +
                    
                    (isOwner ? `👑 *DONO APENAS*\n` +
                              `• \`admin\` - Painel administrativo\n` +
                              `• \`cache\` - Gerenciar cache\n` +
                              `• \`keys\` - Gerenciar chaves API\n` +
                              `• \`selo\` - Sistema de selos profissionais\n\n` : '') +
                    
                    `💡 *Dicas:*\n` +
                    `• Alguns comandos funcionam sem prefixo\n` +
                    `• Use \`${BOT_CONSTANTS.PREFIX}help [comando]\` para detalhes\n` +
                    `• Digite \`nero\` para conversar com a IA\n\n` +
                    
                    `╭─────────────────────╮\n` +
                    `│  Powered by ${BOT_CONSTANTS.NAME}  │\n` +
                    `╰─────────────────────╯`;

    await reply(helpText);
};

/**
 * Mostrar ajuda de comando específico
 */
const showCommandHelp = async (reply, searchTerm, isOwner) => {
    const commands = getCommandsInfo(isOwner);
    
    // Buscar comando exato
    let command = commands.find(cmd => 
        cmd.name === searchTerm || 
        cmd.aliases.includes(searchTerm)
    );

    // Se não encontrou, buscar por similaridade
    if (!command) {
        command = commands.find(cmd => 
            cmd.name.includes(searchTerm) ||
            cmd.description.toLowerCase().includes(searchTerm) ||
            cmd.aliases.some(alias => alias.includes(searchTerm))
        );
    }

    if (!command) {
        const suggestions = commands
            .filter(cmd => 
                cmd.name.includes(searchTerm.charAt(0)) ||
                cmd.description.toLowerCase().includes(searchTerm.charAt(0))
            )
            .slice(0, 3)
            .map(cmd => cmd.name)
            .join(', ');

        const notFoundText = `${EMOJIS.ERROR} Comando não encontrado: \`${searchTerm}\`\n\n` +
                            (suggestions ? `💡 Talvez você quis dizer: ${suggestions}` : 
                             `Use \`${BOT_CONSTANTS.PREFIX}help\` para ver todos os comandos.`);
        
        return reply(notFoundText);
    }

    // Mostrar detalhes do comando
    const detailText = `📋 *DETALHES DO COMANDO*\n\n` +
                      `🔹 *Nome:* ${command.name}\n` +
                      `📝 *Descrição:* ${command.description}\n` +
                      `⚡ *Uso:* \`${BOT_CONSTANTS.PREFIX}${command.name}${command.usage ? ' ' + command.usage : ''}\`\n` +
                      (command.aliases.length > 0 ? `🔄 *Aliases:* ${command.aliases.join(', ')}\n` : '') +
                      (command.examples ? `💡 *Exemplos:*\n${command.examples.join('\n')}\n` : '') +
                      (command.permissions ? `🔒 *Permissões:* ${command.permissions}\n` : '') +
                      (command.cooldown ? `⏱️ *Cooldown:* ${command.cooldown}s\n` : '') +
                      `\n📚 Use \`${BOT_CONSTANTS.PREFIX}help\` para ver todos os comandos.`;

    await reply(detailText);
};

/**
 * Obter informações dos comandos
 */
const getCommandsInfo = (isOwner) => {
    const commands = [
        {
            name: 'ban',
            aliases: ['b', 'banir'],
            description: 'Bane um membro do grupo',
            usage: '@membro',
            permissions: 'Admin',
            cooldown: 5,
            examples: [`${BOT_CONSTANTS.PREFIX}ban @usuario`, `${BOT_CONSTANTS.PREFIX}b 5511999999999`]
        },
        {
            name: 'promover',
            aliases: ['promote'],
            description: 'Promove membro a administrador',
            usage: '@membro',
            permissions: 'Admin',
            cooldown: 3
        },
        {
            name: 'rebaixar',
            aliases: ['demote'],
            description: 'Rebaixa administrador para membro',
            usage: '@admin',
            permissions: 'Admin',
            cooldown: 3
        },
        {
            name: 'mute',
            aliases: ['silenciar'],
            description: 'Silencia um usuário (deleta suas mensagens)',
            usage: '@membro',
            permissions: 'Admin',
            cooldown: 2
        },
        {
            name: 'desmute',
            aliases: ['dessilenciar'],
            description: 'Remove silenciamento de usuário',
            usage: '@membro',
            permissions: 'Admin',
            cooldown: 2
        },
        {
            name: 'deletar',
            aliases: ['d', 'del'],
            description: 'Deleta mensagem respondida',
            usage: '(responder mensagem)',
            permissions: 'Admin'
        },
        {
            name: 'grupo',
            aliases: ['group'],
            description: 'Abre ou fecha grupo para membros',
            usage: 'a/f (abrir/fechar)',
            permissions: 'Admin',
            examples: [`${BOT_CONSTANTS.PREFIX}grupo f`, `${BOT_CONSTANTS.PREFIX}grupo a`]
        },
        {
            name: 'marcar',
            aliases: ['tag', 'todos'],
            description: 'Marca todos os membros do grupo',
            permissions: 'Admin'
        },
        {
            name: 'ping',
            aliases: ['pong'],
            description: 'Testa velocidade de resposta do bot (modo visual/simples)',
            cooldown: 1
        },
        {
            name: 'ping-mode',
            aliases: ['pingmode', 'ping-style'],
            description: 'Alterna entre modo simples e show do ping',
            permissions: 'Dono'
        },
        {
            name: 'ping-test',
            aliases: ['pingtest', 'latency-test'],
            description: 'Teste avançado de latência com múltiplas medições',
            permissions: 'Dono'
        },
        {
            name: 'ping-compare',
            aliases: ['pingcompare', 'ping-demo'],
            description: 'Demonstra diferentes estilos de ping',
            permissions: 'Dono'
        },
        {
            name: 'info',
            aliases: ['informacoes'],
            description: 'Mostra informações do bot'
        },
        {
            name: 'status',
            aliases: ['sistema', 'uptime'],
            description: 'Exibe status e estatísticas do sistema'
        },
        {
            name: 'criador',
            aliases: ['dono', 'owner', 'dev'],
            description: 'Mostra contato do desenvolvedor'
        },
        {
            name: 'menu',
            aliases: ['help'],
            description: 'Exibe menu principal com imagem'
        },
        {
            name: 'beijar',
            aliases: ['kiss'],
            description: 'Beija alguém com GIF',
            usage: '@pessoa',
            cooldown: 1
        },
        {
            name: 'matar',
            aliases: ['kill'],
            description: 'Ataca alguém com GIF',
            usage: '@pessoa',
            cooldown: 1
        },
        {
            name: 'nero',
            aliases: ['ai', 'ia'],
            description: 'Sistema de IA conversacional avançado',
            usage: '[mensagem/on/off/status/reload]',
            permissions: 'Admin (para controle)',
            examples: [
                'nero olá, como vai?',
                'nero !ping',
                '!nero on',
                '!nero status'
            ]
        },
        {
            name: 'websearch',
            aliases: ['busca', 'search', 'internet'],
            description: 'Sistema de busca na internet para NERO',
            usage: '[on/off/status/test/cache]',
            permissions: 'Admin',
            examples: [
                '!websearch on',
                '!websearch status',
                '!websearch test',
                'nero, pesquise sobre Node.js',
                'nero, cotação do dólar hoje'
            ]
        }
    ];

    // Adicionar comandos exclusivos do dono
    if (isOwner) {
        commands.push(
            {
                name: 'admin',
                aliases: ['adm', 'sistema'],
                description: 'Painel administrativo completo',
                permissions: 'Dono',
                usage: '[stats/backup/logs/cache/restart/eval]'
            },
            {
                name: 'cache',
                aliases: ['memoria'],
                description: 'Gerencia sistema de cache',
                permissions: 'Dono',
                usage: '[stats/clear]'
            },
            {
                name: 'selo',
                aliases: ['seal', 'assinatura'],
                description: 'Sistema de selos profissionais',
                permissions: 'Dono',
                usage: '[dono/auto/lista/info]',
                examples: [
                    `${BOT_CONSTANTS.PREFIX}selo dono`,
                    `${BOT_CONSTANTS.PREFIX}selo auto on`,
                    `${BOT_CONSTANTS.PREFIX}selo lista`,
                    `${BOT_CONSTANTS.PREFIX}selo info`
                ]
            }
        );
    }

    return commands;
};
