const { getSeal } = require('../utils/selos');

module.exports = {
    name: 'teste-selo',
    aliases: ['testseal', 'selo-test'],
    description: 'Testa performance dos selos (apenas dono)',
    cooldown: 5,
    
    async execute({ sock, msg, args, reply }) {
        try {
            const sender = msg.key.participant || msg.key.remoteJid;
            const config = require('../config.json');
            const ownerNumber = config.OwnerNumber.value.replace(/[^0-9]/g, '') + '@s.whatsapp.net';
            
            // Verificar se é o dono
            if (sender !== ownerNumber) {
                return reply('❌ Apenas o dono do bot pode usar este comando.');
            }

            const tipo = args[0]?.toLowerCase();
            
            if (!tipo) {
                return reply(`🧪 **Teste de Performance de Selos**\n\n**Uso:** \`!teste-selo [tipo]\`\n\n**Tipos disponíveis:**\n• rapido - Selos otimizados\n• lento - Selos complexos\n• comparar - Comparação de velocidade`);
            }

            const remoteJid = msg.key.remoteJid;

            switch (tipo) {
                case 'rapido':
                case 'fast':
                    const startTime = Date.now();
                    
                    await sock.sendMessage(remoteJid, {
                        text: '⚡ **Teste de Selo Rápido**\n\nTestando selo otimizado...'
                    }, { quoted: getSeal('netflix') });
                    
                    const endTime = Date.now();
                    const duration = endTime - startTime;
                    
                    await reply(`✅ **Selo Rápido Enviado**\n\n⏱️ **Tempo:** ${duration}ms\n🎯 **Tipo:** liveLocationMessage otimizado\n📊 **Performance:** ${duration < 1000 ? 'Excelente' : duration < 2000 ? 'Boa' : 'Lenta'}`);
                    break;

                case 'lento':
                case 'slow':
                    await reply('🐌 **Teste de Selo Complexo**\n\nTestando selo com vCard completo...');
                    
                    const complexSeal = {
                        key: {
                            participant: "<EMAIL>",
                            remoteJid: "status@broadcast", 
                            fromMe: false,
                        },
                        message: {
                            contactMessage: {
                                displayName: "Teste Complexo", 
                                vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Teste Complexo;;;\nFN:Teste Complexo\nitem1.TEL;waid=13135550002:13135550002\nitem1.X-ABLabel:Celular\nEND:VCARD`, 
                                contextInfo: {
                                    forwardingScore: 1,
                                    isForwarded: true
                                }
                            }
                        }
                    };

                    const startTime2 = Date.now();
                    
                    await sock.sendMessage(remoteJid, {
                        text: '🐌 **Selo Complexo Enviado**\n\nEste selo usa contactMessage com vCard completo.'
                    }, { quoted: complexSeal });
                    
                    const endTime2 = Date.now();
                    const duration2 = endTime2 - startTime2;
                    
                    await reply(`✅ **Selo Complexo Enviado**\n\n⏱️ **Tempo:** ${duration2}ms\n🎯 **Tipo:** contactMessage com vCard\n📊 **Performance:** ${duration2 < 1000 ? 'Excelente' : duration2 < 2000 ? 'Boa' : 'Lenta'}`);
                    break;

                case 'comparar':
                case 'compare':
                    await reply('🔄 **Iniciando Comparação de Performance...**\n\nTestando ambos os tipos...');
                    
                    // Teste 1: Selo Rápido
                    const start1 = Date.now();
                    await sock.sendMessage(remoteJid, {
                        text: '⚡ Selo Rápido (liveLocationMessage)'
                    }, { quoted: getSeal('spotify') });
                    const end1 = Date.now();
                    const time1 = end1 - start1;
                    
                    // Aguardar um pouco
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    // Teste 2: Selo Complexo
                    const complexSeal2 = {
                        key: { participant: "<EMAIL>", remoteJid: "status@broadcast", fromMe: false },
                        message: {
                            contactMessage: {
                                displayName: "Teste Performance", 
                                vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Teste Performance;;;\nFN:Teste Performance\nitem1.TEL;waid=13135550002:13135550002\nitem1.X-ABLabel:Celular\nEND:VCARD`, 
                                contextInfo: { forwardingScore: 1, isForwarded: true }
                            }
                        }
                    };
                    
                    const start2 = Date.now();
                    await sock.sendMessage(remoteJid, {
                        text: '🐌 Selo Complexo (contactMessage)'
                    }, { quoted: complexSeal2 });
                    const end2 = Date.now();
                    const time2 = end2 - start2;
                    
                    // Resultado da comparação
                    const diferenca = time2 - time1;
                    const percentual = ((diferenca / time1) * 100).toFixed(1);
                    
                    await reply(`📊 **Comparação de Performance**\n\n⚡ **Selo Rápido:** ${time1}ms\n🐌 **Selo Complexo:** ${time2}ms\n\n📈 **Diferença:** ${diferenca}ms (${percentual}% ${diferenca > 0 ? 'mais lento' : 'mais rápido'})\n\n🏆 **Vencedor:** ${time1 < time2 ? 'Selo Rápido' : 'Selo Complexo'}\n\n💡 **Recomendação:** ${time1 < time2 ? 'Use selos otimizados para melhor performance' : 'Ambos têm performance similar'}`);
                    break;

                default:
                    await reply('❌ Tipo de teste inválido. Use: rapido, lento ou comparar');
            }

        } catch (error) {
            console.error('Erro no teste de selo:', error);
            await reply('❌ Erro ao executar teste de performance.');
        }
    }
};
