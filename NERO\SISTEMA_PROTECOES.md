# 🛡️ Sistema de Proteções NERO

O NERO agora possui um **sistema de proteções avançado** para grupos do WhatsApp, oferecendo controle total sobre o conteúdo e comportamento dos membros.

## 🎯 Funcionalidades Implementadas

### 🔗 **Anti-Link**
- Bloqueia links não autorizados
- Sistema de whitelist para domínios permitidos
- Ações configuráveis: deletar, avisar ou expulsar

### 🖼️ **Anti-Imagem**
- Bloqueia envio de imagens
- Controle total sobre conteúdo visual
- Ideal para grupos focados em texto

### 🤬 **Anti-Palavrão**
- Filtro inteligente de palavrões
- Lista personalizável de palavras bloqueadas
- Proteção contra conteúdo impróprio

### 🚫 **Anti-Fake**
- Bloqueia números estrangeiros/fake
- Configuração por código de país
- Proteção contra spam internacional

### 💨 **Anti-Flood**
- Previne spam de mensagens
- Limite configurável de mensagens por tempo
- Sistema de janela deslizante

## 📋 Comandos Disponíveis

### 🎛️ **Comando Principal**
```bash
!protections list          # Listar todas as proteções
!protections status        # Ver status das proteções
!protections stats         # Ver estatísticas
!protections enable [tipo] # Ativar proteção
!protections disable [tipo]# Desativar proteção
```

### 🔗 **Anti-Link**
```bash
!antilink on [ação]        # Ativar (delete/warn/kick)
!antilink off              # Desativar
!antilink config           # Ver configuração
!antilink whitelist add exemplo.com    # Adicionar domínio
!antilink whitelist remove exemplo.com # Remover domínio
!antilink test https://exemplo.com     # Testar link
```

### 🖼️ **Anti-Imagem**
```bash
!antiimg on [ação]         # Ativar proteção
!antiimg off               # Desativar proteção
!antiimg config            # Ver configuração
```

### 🤬 **Anti-Palavrão**
```bash
!antipalavrao on [ação]    # Ativar filtro
!antipalavrao off          # Desativar filtro
!antipalavrao words add palavra    # Adicionar palavra
!antipalavrao words remove palavra # Remover palavra
!antipalavrao test frase aqui      # Testar filtro
```

## ⚙️ Configurações

### 🎯 **Tipos de Ação**
- **delete**: Apaga a mensagem automaticamente
- **warn**: Avisa o usuário sobre a violação
- **kick**: Remove o usuário do grupo

### 📊 **Sistema de Violações**
- Contador automático de violações por usuário
- Limite configurável antes da ação drástica
- Histórico completo de violações

### 💾 **Persistência de Dados**
- Configurações salvas automaticamente
- Backup das configurações de proteção
- Histórico de violações mantido

## 🚀 Como Usar

### 1. **Ativar Proteções Básicas**
```bash
# Ativar anti-link com ação de deletar
!antilink on delete

# Ativar anti-imagem com aviso
!antiimg on warn

# Ativar filtro de palavrões
!antipalavrao on delete
```

### 2. **Configurar Whitelist**
```bash
# Permitir YouTube e GitHub
!antilink whitelist add youtube.com
!antilink whitelist add github.com

# Ver lista de domínios permitidos
!antilink whitelist list
```

### 3. **Monitorar Atividade**
```bash
# Ver status de todas as proteções
!protections status

# Ver estatísticas detalhadas
!protections stats

# Ver configuração específica
!antilink config
```

## 🔧 Arquitetura Técnica

### 📁 **Estrutura de Arquivos**
```
src/
├── utils/
│   └── protectionSystem.js    # Sistema principal
├── commands/
│   ├── protections.js         # Comando principal
│   ├── antilink.js           # Comando anti-link
│   ├── antiimg.js            # Comando anti-imagem
│   └── antipalavrao.js       # Comando anti-palavrão
└── database/
    ├── protections.json      # Configurações
    └── violations.json       # Histórico de violações
```

### 🔄 **Fluxo de Funcionamento**
1. **Mensagem recebida** → Sistema de proteções verifica
2. **Violação detectada** → Ação configurada é executada
3. **Violação registrada** → Contador atualizado
4. **Log gerado** → Atividade registrada

### 🛡️ **Integração com Sistema Principal**
- Verificação automática em todas as mensagens
- Integração com sistema de permissões existente
- Compatibilidade com sistema de logs
- Respeita hierarquia de usuários (dono/admin)

## 📈 Estatísticas e Monitoramento

### 📊 **Métricas Disponíveis**
- Número de proteções ativas
- Total de violações por grupo
- Violações por tipo de proteção
- Histórico temporal de atividade

### 🔍 **Sistema de Logs**
- Todas as ações são registradas
- Logs estruturados para análise
- Integração com sistema de logs existente

## 🎯 Exemplos Práticos

### 📝 **Grupo de Estudos**
```bash
!antilink on delete          # Bloquear links
!antiimg on warn             # Avisar sobre imagens
!antipalavrao on delete      # Filtrar palavrões
```

### 💼 **Grupo Corporativo**
```bash
!antilink on kick            # Expulsar por links
!antipalavrao on kick        # Expulsar por palavrões
!antiimg on delete           # Deletar imagens
```

### 🎮 **Grupo de Entretenimento**
```bash
!antilink on warn            # Apenas avisar sobre links
!antipalavrao on warn        # Avisar sobre palavrões
# Permitir imagens e outros conteúdos
```

## 🔒 Segurança e Permissões

### 👑 **Níveis de Acesso**
- **Dono**: Acesso total a todas as proteções
- **Admin**: Pode configurar proteções do grupo
- **Membro**: Não pode alterar configurações

### 🛡️ **Proteções do Sistema**
- Validação de permissões em todos os comandos
- Proteção contra auto-banimento de admins
- Sistema de backup automático das configurações

## 🚀 Próximas Funcionalidades

### 📅 **Em Desenvolvimento**
- 🎬 Anti-vídeo
- 🎵 Anti-áudio  
- 🎭 Anti-sticker
- 📞 Anti-call
- 📍 Anti-localização

### 💡 **Melhorias Planejadas**
- Interface web para configuração
- Relatórios detalhados de atividade
- Sistema de alertas para admins
- Integração com IA para detecção avançada

---

## 🎉 **Sistema de Proteções NERO - Segurança Profissional para Grupos**

*Mantenha seus grupos seguros e organizados com o sistema de proteções mais avançado para WhatsApp!*
