const { getSeal } = require('../utils/selos');
const config = require('../config.json');
const fs = require('fs');
const path = require('path');

module.exports = {
    name: 'selo',
    aliases: ['seal', 'assinatura'],
    description: 'Gerencia os selos do bot (apenas dono)',
    async execute({ sock, msg, args, reply }) {
        try {
            const sender = msg.key.participant || msg.key.remoteJid;
            const ownerNumber = config.OwnerNumber.value.replace(/[^0-9]/g, '') + '@s.whatsapp.net';
            
            // Verificar se é o dono
            if (sender !== ownerNumber) {
                return reply('❌ Apenas o dono do bot pode usar este comando.');
            }

            const subcommand = args[0]?.toLowerCase();
            const remoteJid = msg.key.remoteJid;

            switch (subcommand) {
                case 'dono':
                case 'owner':
                    await sock.sendMessage(remoteJid, {
                        text: '👑 *Selo do Dono Premium*\n\n✅ Versão completa com todas as informações!'
                    }, { quoted: getSeal('owner') });
                    break;

                case 'simples':
                case 'simple':
                    await sock.sendMessage(remoteJid, {
                        text: '👑 *Selo do Dono Simples*\n\n✅ Versão elegante e clean!'
                    }, { quoted: getSeal('ownerSimple') });
                    break;

                case 'minimal':
                case 'minimo':
                    await sock.sendMessage(remoteJid, {
                        text: '👑 *Selo do Dono Minimalista*\n\n✅ Versão compacta e discreta!'
                    }, { quoted: getSeal('ownerMinimal') });
                    break;

                case 'corporativo':
                case 'corporate':
                    await sock.sendMessage(remoteJid, {
                        text: '👑 *Selo do Dono Corporativo*\n\n✅ Versão profissional empresarial!'
                    }, { quoted: getSeal('ownerCorporate') });
                    break;

                case 'meta':
                case 'ai':
                    await sock.sendMessage(remoteJid, {
                        text: '🤖 *Selo Meta AI*\n\n✅ Mensagem enviada com selo do Meta AI!'
                    }, { quoted: getSeal('metaAI') });
                    break;

                case 'whatsapp':
                case 'wpp':
                    await sock.sendMessage(remoteJid, {
                        text: '📱 *Selo WhatsApp*\n\n✅ Mensagem enviada com selo oficial do WhatsApp!'
                    }, { quoted: getSeal('whatsapp') });
                    break;

                case 'chatgpt':
                case 'gpt':
                    await sock.sendMessage(remoteJid, {
                        text: '🧠 *Selo ChatGPT*\n\n✅ Mensagem enviada com selo do ChatGPT!'
                    }, { quoted: getSeal('chatGPT') });
                    break;

                case 'copilot':
                    await sock.sendMessage(remoteJid, {
                        text: '💻 *Selo Microsoft Copilot*\n\n✅ Mensagem enviada com selo do Copilot!'
                    }, { quoted: getSeal('copilot') });
                    break;

                case 'nubank':
                    await sock.sendMessage(remoteJid, {
                        text: '💳 *Selo Nubank*\n\n✅ Mensagem enviada com selo do Nubank!'
                    }, { quoted: getSeal('nubank') });
                    break;

                case 'bb':
                case 'bancobrasil':
                    await sock.sendMessage(remoteJid, {
                        text: '🏦 *Selo Banco do Brasil*\n\n✅ Mensagem enviada com selo do Banco do Brasil!'
                    }, { quoted: getSeal('bancoBrasil') });
                    break;

                case 'caixa':
                case 'cef':
                    await sock.sendMessage(remoteJid, {
                        text: '🏦 *Selo CAIXA*\n\n✅ Mensagem enviada com selo da CAIXA!'
                    }, { quoted: getSeal('caixa') });
                    break;

                case 'itau':
                case 'itaú':
                    await sock.sendMessage(remoteJid, {
                        text: '🧡 *Selo Itaú*\n\n✅ Mensagem enviada com selo do Itaú!'
                    }, { quoted: getSeal('itau') });
                    break;

                case 'bradesco':
                    await sock.sendMessage(remoteJid, {
                        text: '🔴 *Selo Bradesco*\n\n✅ Mensagem enviada com selo do Bradesco!'
                    }, { quoted: getSeal('bradesco') });
                    break;

                case 'santander':
                    await sock.sendMessage(remoteJid, {
                        text: '🔴 *Selo Santander*\n\n✅ Mensagem enviada com selo do Banco Santander!'
                    }, { quoted: getSeal('santander') });
                    break;

                case 'inter':
                    await sock.sendMessage(remoteJid, {
                        text: '🧡 *Selo Banco Inter*\n\n✅ Mensagem enviada com selo do Banco Inter!'
                    }, { quoted: getSeal('inter') });
                    break;

                case 'btg':
                case 'btgpactual':
                    await sock.sendMessage(remoteJid, {
                        text: '⚫ *Selo BTG Pactual*\n\n✅ Mensagem enviada com selo do BTG Pactual!'
                    }, { quoted: getSeal('btg') });
                    break;

                case 'c6':
                case 'c6bank':
                    await sock.sendMessage(remoteJid, {
                        text: '💜 *Selo C6 Bank*\n\n✅ Mensagem enviada com selo do C6 Bank!'
                    }, { quoted: getSeal('c6bank') });
                    break;

                case 'next':
                    await sock.sendMessage(remoteJid, {
                        text: '🟣 *Selo Next*\n\n✅ Mensagem enviada com selo do Next (Bradesco)!'
                    }, { quoted: getSeal('next') });
                    break;

                case 'picpay':
                    await sock.sendMessage(remoteJid, {
                        text: '💚 *Selo PicPay*\n\n✅ Mensagem enviada com selo do PicPay!'
                    }, { quoted: getSeal('picpay') });
                    break;

                case 'mercadopago':
                case 'mp':
                    await sock.sendMessage(remoteJid, {
                        text: '💙 *Selo Mercado Pago*\n\n✅ Mensagem enviada com selo do Mercado Pago!'
                    }, { quoted: getSeal('mercadopago') });
                    break;

                case 'safra':
                    await sock.sendMessage(remoteJid, {
                        text: '🟤 *Selo Banco Safra*\n\n✅ Mensagem enviada com selo do Banco Safra!'
                    }, { quoted: getSeal('safra') });
                    break;

                case 'original':
                    await sock.sendMessage(remoteJid, {
                        text: '🟢 *Selo Banco Original*\n\n✅ Mensagem enviada com selo do Banco Original!'
                    }, { quoted: getSeal('original') });
                    break;

                case 'neon':
                    await sock.sendMessage(remoteJid, {
                        text: '💙 *Selo Neon*\n\n✅ Mensagem enviada com selo do Banco Neon!'
                    }, { quoted: getSeal('neon') });
                    break;

                case 'bancos':
                case 'banks':
                    const bancosText = `🏦 *SELOS DE BANCOS DISPONÍVEIS*

🔵 **BANCOS TRADICIONAIS:**
• \`!selo bb\` - 🏦 Banco do Brasil
• \`!selo caixa\` - 🏦 CAIXA Econômica Federal
• \`!selo itau\` - 🧡 Banco Itaú
• \`!selo bradesco\` - 🔴 Banco Bradesco
• \`!selo santander\` - 🔴 Banco Santander
• \`!selo safra\` - 🟤 Banco Safra

🟣 **BANCOS DIGITAIS:**
• \`!selo nubank\` - 💜 Nubank
• \`!selo inter\` - 🧡 Banco Inter
• \`!selo c6bank\` - 💜 C6 Bank
• \`!selo next\` - 🟣 Next (Bradesco)
• \`!selo original\` - 🟢 Banco Original
• \`!selo neon\` - 💙 Banco Neon

💰 **FINTECHS:**
• \`!selo picpay\` - 💚 PicPay
• \`!selo mercadopago\` - 💙 Mercado Pago

🏛️ **INVESTIMENTOS:**
• \`!selo btg\` - ⚫ BTG Pactual

📋 **Como usar:**
\`!selo [banco]\` - Enviar mensagem com selo do banco
\`!selo lista\` - Ver todos os selos disponíveis`;

                    await sock.sendMessage(remoteJid, {
                        text: bancosText
                    }, { quoted: getSeal('owner') });
                    break;





                case 'list':
                case 'lista':
                    const autoStatusList = config.autoOwnerSeal?.value !== false ? '✅ ATIVADO' : '❌ DESATIVADO';
                    const selosList = `🔖 *SELOS DISPONÍVEIS*

👑 **SELOS DO DONO:**
• \`!selo dono\` - Selo Premium (completo)
• \`!selo simples\` - Selo Elegante
• \`!selo minimal\` - Selo Minimalista
• \`!selo corporativo\` - Selo Empresarial

🤖 **SELOS DE IA:**
• \`!selo meta\` - Selo Meta AI
• \`!selo whatsapp\` - Selo WhatsApp
• \`!selo chatgpt\` - Selo ChatGPT
• \`!selo copilot\` - Selo Microsoft Copilot

🏦 **SELOS DE BANCOS:**
• \`!selo bancos\` - Ver todos os bancos disponíveis
• \`!selo nubank\` - 💜 Nubank
• \`!selo bb\` - 🏦 Banco do Brasil
• \`!selo caixa\` - 🏦 CAIXA
• \`!selo itau\` - 🧡 Itaú
• \`!selo bradesco\` - 🔴 Bradesco
• \`!selo santander\` - 🔴 Santander
• \`!selo inter\` - 🧡 Banco Inter
• \`!selo btg\` - ⚫ BTG Pactual
• \`!selo c6bank\` - ⚫ C6 Bank
• \`!selo next\` - 🔴 Next
• \`!selo picpay\` - 💚 PicPay
• \`!selo mercadopago\` - 💙 Mercado Pago
• \`!selo safra\` - 💜 Banco Safra
• \`!selo original\` - 💚 Banco Original
• \`!selo neon\` - 💙 Banco Neon

📋 *COMANDOS:*
• \`!selo [banco]\` - Enviar mensagem com selo do banco
• \`!selo bancos\` - Ver lista completa de bancos
• \`!selo auto on/off\` - Ativar/desativar automático
• \`!selo lista\` - Ver todos os selos
• \`!selo info\` - Informações sobre selos

👑 *SELO AUTOMÁTICO:* ${autoStatusList}`;

                    await sock.sendMessage(remoteJid, {
                        text: selosList
                    }, { quoted: getSeal('owner') });
                    break;

                case 'auto':
                case 'automatico':
                    const currentStatus = config.autoOwnerSeal?.value !== false;
                    const newStatus = args[1]?.toLowerCase();

                    if (newStatus === 'on' || newStatus === 'ativar' || newStatus === 'true') {
                        config.autoOwnerSeal.value = true;
                        fs.writeFileSync(path.join(__dirname, '../config.json'), JSON.stringify(config, null, 2));
                        await sock.sendMessage(remoteJid, {
                            text: '✅ *Selo Automático do Dono ATIVADO*\n\n👑 Suas mensagens agora aparecerão automaticamente com o selo do desenvolvedor!'
                        }, { quoted: getSeal('owner') });
                    } else if (newStatus === 'off' || newStatus === 'desativar' || newStatus === 'false') {
                        config.autoOwnerSeal.value = false;
                        fs.writeFileSync(path.join(__dirname, '../config.json'), JSON.stringify(config, null, 2));
                        await sock.sendMessage(remoteJid, {
                            text: '❌ *Selo Automático do Dono DESATIVADO*\n\n📝 Suas mensagens voltarão a aparecer normalmente.'
                        });
                    } else {
                        const statusText = currentStatus ? '✅ ATIVADO' : '❌ DESATIVADO';
                        await sock.sendMessage(remoteJid, {
                            text: `🔖 *STATUS DO SELO AUTOMÁTICO*\n\n**Status atual:** ${statusText}\n\n**Para alterar:**\n• \`!selo auto on\` - Ativar\n• \`!selo auto off\` - Desativar`
                        }, { quoted: getSeal('owner') });
                    }
                    break;

                case 'estilo':
                case 'style':
                    const newStyle = args[1]?.toLowerCase();
                    const validStyles = ['dono', 'simples', 'minimal', 'corporativo'];

                    if (!newStyle) {
                        const currentStyle = config.ownerSealStyle?.value || 'dono';
                        await sock.sendMessage(remoteJid, {
                            text: `🎨 *ESTILO ATUAL DO SELO*\n\n**Estilo ativo:** ${currentStyle}\n\n**Estilos disponíveis:**\n• \`dono\` - Premium (padrão)\n• \`simples\` - Elegante\n• \`minimal\` - Minimalista\n• \`corporativo\` - Empresarial\n\n**Uso:** \`!selo estilo [tipo]\``
                        }, { quoted: getSeal('owner') });
                        return;
                    }

                    if (!validStyles.includes(newStyle)) {
                        await sock.sendMessage(remoteJid, {
                            text: '❌ *Estilo inválido!*\n\n**Estilos disponíveis:**\n• dono\n• simples\n• minimal\n• corporativo'
                        }, { quoted: getSeal('owner') });
                        return;
                    }

                    // Adicionar configuração se não existir
                    if (!config.ownerSealStyle) {
                        config.ownerSealStyle = {
                            description: "Estilo padrão do selo do dono",
                            value: "dono"
                        };
                    }

                    config.ownerSealStyle.value = newStyle;
                    fs.writeFileSync(path.join(__dirname, '../config.json'), JSON.stringify(config, null, 2));

                    const styleNames = {
                        'dono': 'Premium',
                        'simples': 'Elegante',
                        'minimal': 'Minimalista',
                        'corporativo': 'Empresarial'
                    };

                    await sock.sendMessage(remoteJid, {
                        text: `✅ *Estilo do Selo Alterado*\n\n🎨 **Novo estilo:** ${styleNames[newStyle]}\n\n👑 Suas mensagens automáticas agora usarão este estilo!`
                    }, { quoted: getSeal(newStyle === 'dono' ? 'owner' : `owner${newStyle.charAt(0).toUpperCase() + newStyle.slice(1)}`) });
                    break;

                case 'info':
                case 'ajuda':
                    const autoStatus = config.autoOwnerSeal?.value !== false ? '✅ ATIVADO' : '❌ DESATIVADO';
                    const infoText = `ℹ️ *SISTEMA DE SELOS*

🎯 **O que são selos?**
Selos são assinaturas especiais que aparecem nas mensagens, dando credibilidade e profissionalismo.

👑 **Selo Automático do Dono:** ${autoStatus}
• Aparece como: "Desenvolvedor: ${config.nameOwner.value} | Bot: ${config.botName.value}"
• Dá credibilidade e autoridade às suas mensagens
• Use \`!selo auto on/off\` para ativar/desativar

🔧 **Como usar:**
• \`!selo dono\` - Testar selo do dono
• \`!selo auto on/off\` - Ativar/desativar automático
• \`!selo lista\` - Ver todos os selos disponíveis
• \`!selo [tipo]\` - Enviar mensagem com selo específico

✨ **Benefícios:**
• Credibilidade profissional
• Identificação como desenvolvedor oficial
• Diferenciação das mensagens comuns
• Aparência mais séria e confiável`;

                    await sock.sendMessage(remoteJid, {
                        text: infoText
                    }, { quoted: getSeal('owner') });
                    break;

                default:
                    const autoStatusDefault = config.autoOwnerSeal?.value !== false ? '✅ ATIVADO' : '❌ DESATIVADO';
                    const currentStyle = config.ownerSealStyle?.value || 'dono';
                    const helpText = `🔖 *COMANDO SELO*

**Uso:** \`!selo [tipo]\`

**Selos do Dono:**
• \`!selo dono\` - Selo Premium
• \`!selo simples\` - Selo Elegante
• \`!selo minimal\` - Selo Minimalista
• \`!selo corporativo\` - Selo Empresarial

**Configurações:**
• \`!selo auto on/off\` - Ativar/desativar automático
• \`!selo estilo [tipo]\` - Mudar estilo padrão
• \`!selo lista\` - Ver todos os selos
• \`!selo info\` - Informações detalhadas

👑 **Selo Automático:** ${autoStatusDefault}
🎨 **Estilo Atual:** ${currentStyle}`;

                    await sock.sendMessage(remoteJid, {
                        text: helpText
                    }, { quoted: getSeal('owner') });
                    break;
            }

        } catch (error) {
            console.error('Erro no comando selo:', error);
            await reply('❌ Erro ao executar comando de selo.');
        }
    }
};
