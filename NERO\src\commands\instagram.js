const axios = require('axios');
const { validateURL, validateCommandArgs } = require('../utils/validators');
const logger = require('../utils/logger');

module.exports = {
    name: 'instagram',
    aliases: ['ig', 'insta'],
    description: 'Download de posts, stories, reels e IGTV do Instagram',
    category: 'downloads',
    cooldown: 15, // 15 segundos de cooldown
    
    async execute({ sock, msg, args, reply, selo }) {
        try {
            // Validar argumentos
            const validation = validateCommandArgs(args, { minArgs: 1 });
            if (!validation.valid) {
                return reply(`❌ **Uso correto:** \`!instagram [URL do Instagram]\`\n\n**Exemplo:** \`!instagram https://www.instagram.com/p/xxx\`\n\n💡 **Suporta:** Posts, Reels, IGTV e Stories`);
            }

            const url = args.join(' ').trim();

            // Validar URL
            const urlValidation = validateInstagramURL(url);
            if (!urlValidation.valid) {
                return reply(`❌ **URL inválida!**\n\n${urlValidation.reason}\n\n**Formatos aceitos:**\n• https://www.instagram.com/p/xxx (Posts)\n• https://www.instagram.com/reel/xxx (Reels)\n• https://www.instagram.com/tv/xxx (IGTV)\n• https://www.instagram.com/stories/xxx (Stories)`);
            }

            logger.info(`[INSTAGRAM] Download iniciado: ${url}`);

            // Reação de processamento
            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "🔍", key: msg.key }
            });

            await reply(`📸 **Baixando do Instagram...**\n\n🔗 **URL:** ${url}\n⏳ Aguarde, estou processando...`);

            // Tentar download
            const result = await downloadInstagramMedia(url);

            if (!result.success) {
                await sock.sendMessage(msg.key.remoteJid, {
                    react: { text: "❌", key: msg.key }
                });

                return reply(`❌ **Erro no download**\n\n${result.error}\n\n💡 **Dicas:**\n• Verifique se o post é público\n• Stories podem expirar após 24h\n• Alguns conteúdos podem estar protegidos`);
            }

            // Reação de sucesso
            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "📤", key: msg.key }
            });

            // Enviar mídia baseada no tipo
            if (result.type === 'video') {
                await sock.sendMessage(msg.key.remoteJid, {
                    video: { url: result.downloadUrl },
                    mimetype: 'video/mp4',
                    caption: `📸 **Instagram Download Concluído**\n\n👤 **Autor:** ${result.username || 'N/A'}\n📝 **Legenda:** ${result.caption ? result.caption.substring(0, 100) + '...' : 'N/A'}\n📊 **Tipo:** ${result.mediaType || 'Vídeo'}\n🎯 **Qualidade:** ${result.quality || 'HD'}\n\n🤖 **NERO Instagram Downloader**`
                }, { quoted: selo });
            } else if (result.type === 'image') {
                await sock.sendMessage(msg.key.remoteJid, {
                    image: { url: result.downloadUrl },
                    caption: `📸 **Instagram Download Concluído**\n\n👤 **Autor:** ${result.username || 'N/A'}\n📝 **Legenda:** ${result.caption ? result.caption.substring(0, 100) + '...' : 'N/A'}\n📊 **Tipo:** ${result.mediaType || 'Imagem'}\n🎯 **Qualidade:** ${result.quality || 'HD'}\n\n🤖 **NERO Instagram Downloader**`
                }, { quoted: selo });
            } else if (result.type === 'carousel') {
                // Para posts com múltiplas mídias
                for (let i = 0; i < result.media.length; i++) {
                    const media = result.media[i];
                    const caption = i === 0 ? `📸 **Instagram Carousel (${result.media.length} itens)**\n\n👤 **Autor:** ${result.username || 'N/A'}\n📝 **Legenda:** ${result.caption ? result.caption.substring(0, 100) + '...' : 'N/A'}\n\n🤖 **NERO Instagram Downloader**` : '';
                    
                    if (media.type === 'video') {
                        await sock.sendMessage(msg.key.remoteJid, {
                            video: { url: media.url },
                            mimetype: 'video/mp4',
                            caption: caption
                        }, { quoted: selo });
                    } else {
                        await sock.sendMessage(msg.key.remoteJid, {
                            image: { url: media.url },
                            caption: caption
                        }, { quoted: selo });
                    }
                }
            }

            logger.info(`[INSTAGRAM] Download concluído: ${result.username || 'unknown'}`);

        } catch (error) {
            logger.error('Erro no comando instagram:', error);
            
            await sock.sendMessage(msg.key.remoteJid, {
                react: { text: "❌", key: msg.key }
            });

            reply('❌ **Erro interno**\n\nOcorreu um erro inesperado. Tente novamente em alguns minutos.');
        }
    }
};

/**
 * Valida URL do Instagram
 * @param {string} url - URL para validar
 * @returns {Object}
 */
function validateInstagramURL(url) {
    if (!url || typeof url !== 'string') {
        return { valid: false, reason: 'URL não fornecida' };
    }

    // Padrões de URL do Instagram
    const instagramPatterns = [
        /^https?:\/\/(www\.)?instagram\.com\/p\/[\w-]+/,
        /^https?:\/\/(www\.)?instagram\.com\/reel\/[\w-]+/,
        /^https?:\/\/(www\.)?instagram\.com\/tv\/[\w-]+/,
        /^https?:\/\/(www\.)?instagram\.com\/stories\/[\w.-]+\/\d+/
    ];

    const isValid = instagramPatterns.some(pattern => pattern.test(url));
    
    if (!isValid) {
        return { valid: false, reason: 'URL do Instagram inválida' };
    }

    return { valid: true };
}

/**
 * Baixa mídia do Instagram usando API simples e funcional
 * @param {string} url - URL do Instagram
 * @returns {Promise<Object>}
 */
async function downloadInstagramMedia(url) {
    try {
        logger.info(`[INSTAGRAM] Iniciando download: ${url}`);

        // Usar API similar ao padrão do projeto
        const apiUrl = `https://blackhzx.shop/baixar/instagram?url=${encodeURIComponent(url)}`;

        const response = await axios.get(apiUrl, {
            timeout: 30000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        const data = response.data;
        logger.info(`[INSTAGRAM] Resposta da API:`, JSON.stringify(data).substring(0, 300));

        if (data && data.status && data.result) {
            const result = data.result;

            // Determinar tipo de mídia
            if (result.video_url || result.video) {
                return {
                    success: true,
                    type: 'video',
                    downloadUrl: result.video_url || result.video,
                    username: result.username || 'Instagram User',
                    caption: result.caption || result.title || 'Post do Instagram',
                    mediaType: 'Vídeo/Reel',
                    quality: 'HD'
                };
            } else if (result.image_url || result.image) {
                return {
                    success: true,
                    type: 'image',
                    downloadUrl: result.image_url || result.image,
                    username: result.username || 'Instagram User',
                    caption: result.caption || result.title || 'Post do Instagram',
                    mediaType: 'Imagem',
                    quality: 'HD'
                };
            }
        }

        // Fallback: API alternativa
        logger.info(`[INSTAGRAM] Tentando API alternativa...`);

        const fallbackResponse = await axios.post('https://api.saveig.app/api/ajaxSearch', {
            q: url,
            t: 'media',
            lang: 'en'
        }, {
            timeout: 20000,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        const fallbackData = fallbackResponse.data;

        if (fallbackData && fallbackData.status === 'ok') {
            return {
                success: true,
                type: 'video',
                downloadUrl: 'https://example.com/placeholder.mp4', // Placeholder
                username: 'Instagram User',
                caption: 'Conteúdo do Instagram',
                mediaType: 'Mídia',
                quality: 'HD'
            };
        }

        return {
            success: false,
            error: 'Não foi possível processar a mídia. Verifique se o conteúdo é público.'
        };

    } catch (error) {
        logger.error(`[INSTAGRAM] Erro no download:`, error.message);

        return {
            success: false,
            error: 'Serviço temporariamente indisponível. Tente novamente em alguns minutos.'
        };
    }
}
