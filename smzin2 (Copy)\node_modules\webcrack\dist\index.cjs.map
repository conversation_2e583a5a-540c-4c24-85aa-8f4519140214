{"version": 3, "sources": ["../src/cjs-wrapper.ts"], "sourcesContent": ["import type { webcrack as wc } from './index.js';\n\nexport const webcrack: typeof wc = async (...args) => {\n  const { webcrack } = await import('./index.js');\n  return webcrack(...args);\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,MAAM,WAAsB,UAAU,SAAS;AACpD,QAAM,EAAE,UAAAA,UAAS,IAAI,MAAM,OAAO,YAAY;AAC9C,SAAOA,UAAS,GAAG,IAAI;AACzB;", "names": ["webcrack"]}