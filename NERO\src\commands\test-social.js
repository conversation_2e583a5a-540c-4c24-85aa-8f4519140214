const axios = require('axios');
const logger = require('../utils/logger');

module.exports = {
    name: 'test-social',
    aliases: ['testsocial', 'social-test'],
    description: 'Testa APIs de redes sociais',
    category: 'debug',
    cooldown: 5,
    
    async execute({ sock, msg, args, reply, selo }) {
        try {
            const platform = args[0]?.toLowerCase();
            
            if (!platform) {
                return reply(`🧪 **Teste de APIs Sociais**\n\n**Uso:** \`!test-social [plataforma]\`\n\n**Plataformas:**\n• tiktok\n• instagram\n• twitter\n• facebook`);
            }

            await reply(`🧪 **Testando APIs de ${platform}...**\n\n⏳ Aguarde...`);

            let result;
            switch (platform) {
                case 'tiktok':
                    result = await testTikTokAPI();
                    break;
                case 'instagram':
                    result = await testInstagramAPI();
                    break;
                case 'twitter':
                    result = await testTwitterAPI();
                    break;
                case 'facebook':
                    result = await testFacebookAPI();
                    break;
                default:
                    return reply('❌ Plataforma não suportada');
            }

            await reply(`📊 **Resultado do Teste - ${platform.toUpperCase()}**\n\n${result}`);

        } catch (error) {
            logger.error('Erro no teste de APIs sociais:', error);
            reply('❌ Erro interno no teste');
        }
    }
};

async function testTikTokAPI() {
    const testUrl = 'https://vm.tiktok.com/ZMhxxx'; // URL de teste
    const apis = [
        {
            name: 'TikWM API',
            endpoint: 'https://tikwm.com/api/',
            test: async () => {
                const response = await axios.get('https://tikwm.com/api/', {
                    params: { url: testUrl, hd: 1 },
                    timeout: 10000
                });
                return response.status === 200;
            }
        },
        {
            name: 'TiklyDown API',
            endpoint: 'https://api.tiklydown.eu.org/api/download',
            test: async () => {
                const response = await axios.get(`https://api.tiklydown.eu.org/api/download?url=${encodeURIComponent(testUrl)}`, {
                    timeout: 10000
                });
                return response.status === 200;
            }
        }
    ];

    let results = [];
    for (const api of apis) {
        try {
            const success = await api.test();
            results.push(`✅ ${api.name}: OK`);
        } catch (error) {
            results.push(`❌ ${api.name}: ${error.message}`);
        }
    }

    return results.join('\n');
}

async function testInstagramAPI() {
    const apis = [
        {
            name: 'SaveIG API',
            test: async () => {
                const response = await axios.post('https://api.saveig.app/api/ajaxSearch', {
                    q: 'https://www.instagram.com/p/test',
                    t: 'media',
                    lang: 'en'
                }, { timeout: 10000 });
                return response.status === 200;
            }
        },
        {
            name: 'SaveIG V3 API',
            test: async () => {
                const response = await axios.post('https://v3.saveig.app/api/ajaxSearch', {
                    q: 'https://www.instagram.com/p/test',
                    t: 'media',
                    lang: 'en'
                }, { timeout: 10000 });
                return response.status === 200;
            }
        }
    ];

    let results = [];
    for (const api of apis) {
        try {
            const success = await api.test();
            results.push(`✅ ${api.name}: OK`);
        } catch (error) {
            results.push(`❌ ${api.name}: ${error.message}`);
        }
    }

    return results.join('\n');
}

async function testTwitterAPI() {
    return '🚧 Twitter API em desenvolvimento';
}

async function testFacebookAPI() {
    return '🚧 Facebook API em desenvolvimento';
}
